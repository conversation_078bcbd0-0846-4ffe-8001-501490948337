---
alwaysApply: false
---


Overview

The OpenAI Agents SDK for TypeScript enables you to build agentic AI apps in a lightweight, easy-to-use package with very few abstractions. It’s a production-ready upgrade of our previous experimentation for agents, Swarm, that’s also available in Python. The Agents SDK has a very small set of primitives:

    Agents, which are LLMs equipped with instructions and tools
    Handoffs, which allow agents to delegate to other agents for specific tasks
    Guardrails, which enable the inputs to agents to be validated

In combination with TypeScript, these primitives are powerful enough to express complex relationships between tools and agents, and allow you to build real-world applications without a steep learning curve. In addition, the SDK comes with built-in tracing that lets you visualize and debug your agentic flows, as well as evaluate them and even fine-tune models for your application.
Why use the Agents SDK

The SDK has two driving design principles:

    Enough features to be worth using, but few enough primitives to make it quick to learn.
    Works great out of the box, but you can customize exactly what happens.

Here are the main features of the SDK:

    Agent loop: Built-in agent loop that handles calling tools, sending results to the LLM, and looping until the LLM is done.
    TypeScript-first: Use built-in language features to orchestrate and chain agents, rather than needing to learn new abstractions.
    Handoffs: A powerful feature to coordinate and delegate between multiple agents.
    Guardrails: Run input validations and checks in parallel to your agents, breaking early if the checks fail.
    Function tools: Turn any TypeScript function into a tool, with automatic schema generation and Zod-powered validation.
    Tracing: Built-in tracing that lets you visualize, debug and monitor your workflows, as well as use the OpenAI suite of evaluation, fine-tuning and distillation tools.
    Realtime Agents: Build powerful voice agents including automatic interruption detection, context management, guardrails, and more.

Installation
Terminal window

npm install @openai/agents zod@3

Hello world example
Hello World

import { Agent, run } from '@openai/agents';

const agent = new Agent({
  name: 'Assistant',
  instructions: 'You are a helpful assistant',
});

const result = await run(
  agent,
  'Write a haiku about recursion in programming.',
);
console.log(result.finalOutput);

// Code within the code,
// Functions calling themselves,
// Infinite loop's dance.

(If running this, ensure you set the OPENAI_API_KEY environment variable)
Terminal window

gents

Agents are the main building‑block of the OpenAI Agents SDK. An Agent is a Large Language Model (LLM) that has been configured with:

    Instructions – the system prompt that tells the model who it is and how it should respond.
    Model – which OpenAI model to call, plus any optional model tuning parameters.
    Tools – a list of functions or APIs the LLM can invoke to accomplish a task.

Basic Agent definition

import { Agent } from '@openai/agents';

const agent = new Agent({
  name: 'Haiku Agent',
  instructions: 'Always respond in haiku form.',
  model: 'o4-mini', // optional – falls back to the default model
});

The rest of this page walks through every Agent feature in more detail.
Basic configuration

The Agent constructor takes a single configuration object. The most commonly‑used properties are shown below.
Property	Required	Description
name	yes	A short human‑readable identifier.
instructions	yes	System prompt (string or function – see Dynamic instructions).
model	no	Model name or a custom Model implementation.
modelSettings	no	Tuning parameters (temperature, top_p, etc.).
tools	no	Array of Tool instances the model can call.
Agent with tools

import { Agent, tool } from '@openai/agents';
import { z } from 'zod';

const getWeather = tool({
  name: 'get_weather',
  description: 'Return the weather for a given city.',
  parameters: z.object({ city: z.string() }),
  async execute({ city }) {
    return `The weather in ${city} is sunny.`;
  },
});

const agent = new Agent({
  name: 'Weather bot',
  instructions: 'You are a helpful weather bot.',
  model: 'o4-mini',
  tools: [getWeather],
});

Context

Agents are generic on their context type – i.e. Agent<TContext, TOutput>. The context is a dependency‑injection object that you create and pass to Runner.run(). It is forwarded to every tool, guardrail, handoff, etc. and is useful for storing state or providing shared services (database connections, user metadata, feature flags, …).
Agent with context

import { Agent } from '@openai/agents';

interface Purchase {
  id: string;
  uid: string;
  deliveryStatus: string;
}
interface UserContext {
  uid: string;
  isProUser: boolean;

  // this function can be used within tools
  fetchPurchases(): Promise<Purchase[]>;
}

const agent = new Agent<UserContext>({
  name: 'Personal shopper',
  instructions: 'Recommend products the user will love.',
});

// Later
import { run } from '@openai/agents';

const result = await run(agent, 'Find me a new pair of running shoes', {
  context: { uid: 'abc', isProUser: true, fetchPurchases: async () => [] },
});

Output types

By default, an Agent returns plain text (string). If you want the model to return a structured object you can specify the outputType property. The SDK accepts:

    A Zod schema (z.object({...})).
    Any JSON‑schema‑compatible object.

Structured output with Zod

import { Agent } from '@openai/agents';
import { z } from 'zod';

const CalendarEvent = z.object({
  name: z.string(),
  date: z.string(),
  participants: z.array(z.string()),
});

const extractor = new Agent({
  name: 'Calendar extractor',
  instructions: 'Extract calendar events from the supplied text.',
  outputType: CalendarEvent,
});

When outputType is provided, the SDK automatically uses structured outputs instead of plain text.
Multi-agent system design patterns

There are many ways to compose agents together. Two patterns we regularly see in production apps are:

    Manager (agents as tools) – a central agent owns the conversation and invokes specialized agents that are exposed as tools.
    Handoffs – the initial agent delegates the entire conversation to a specialist once it has identified the user’s request.

These approaches are complementary. Managers give you a single place to enforce guardrails or rate limits, while handoffs let each agent focus on a single task without retaining control of the conversation.
Manager (agents as tools)

In this pattern the manager never hands over control—the LLM uses the tools and the manager summarizes the final answer. Read more in the tools guide.
Agents as tools

import { Agent } from '@openai/agents';

const bookingAgent = new Agent({
  name: 'Booking expert',
  instructions: 'Answer booking questions and modify reservations.',
});

const refundAgent = new Agent({
  name: 'Refund expert',
  instructions: 'Help customers process refunds and credits.',
});

const customerFacingAgent = new Agent({
  name: 'Customer-facing agent',
  instructions:
    'Talk to the user directly. When they need booking or refund help, call the matching tool.',
  tools: [
    bookingAgent.asTool({
      toolName: 'booking_expert',
      toolDescription: 'Handles booking questions and requests.',
    }),
    refundAgent.asTool({
      toolName: 'refund_expert',
      toolDescription: 'Handles refund questions and requests.',
    }),
  ],
});

Handoffs

With handoffs the triage agent routes requests, but once a handoff occurs the specialist agent owns the conversation until it produces a final output. This keeps prompts short and lets you reason about each agent independently. Learn more in the handoffs guide.
Agent with handoffs

import { Agent } from '@openai/agents';

const bookingAgent = new Agent({
  name: 'Booking Agent',
  instructions: 'Help users with booking requests.',
});

const refundAgent = new Agent({
  name: 'Refund Agent',
  instructions: 'Process refund requests politely and efficiently.',
});

// Use Agent.create method to ensure the finalOutput type considers handoffs
const triageAgent = Agent.create({
  name: 'Triage Agent',
  instructions: [
    'Help the user with their questions.',
    'If the user asks about booking, hand off to the booking agent.',
    'If the user asks about refunds, hand off to the refund agent.',
  ].join('\n'),
  handoffs: [bookingAgent, refundAgent],
});

Dynamic instructions

instructions can be a function instead of a string. The function receives the current RunContext and the Agent instance and can return a string or a Promise<string>.
Agent with dynamic instructions

import { Agent, RunContext } from '@openai/agents';

interface UserContext {
  name: string;
}

function buildInstructions(runContext: RunContext<UserContext>) {
  return `The user's name is ${runContext.context.name}.  Be extra friendly!`;
}

const agent = new Agent<UserContext>({
  name: 'Personalized helper',
  instructions: buildInstructions,
});

Both synchronous and async functions are supported.
Lifecycle hooks

For advanced use‑cases you can observe the Agent lifecycle by listening on events
Agent with lifecycle hooks

import { Agent } from '@openai/agents';

const agent = new Agent({
  name: 'Verbose agent',
  instructions: 'Explain things thoroughly.',
});

agent.on('agent_start', (ctx, agent) => {
  console.log(`[${agent.name}] started`);
});
agent.on('agent_end', (ctx, output) => {
  console.log(`[agent] produced:`, output);
});

Guardrails

Guardrails allow you to validate or transform user input and agent output. They are configured via the inputGuardrails and outputGuardrails arrays. See the guardrails guide for details.
Cloning / copying agents

Need a slightly modified version of an existing agent? Use the clone() method, which returns an entirely new Agent instance.
Cloning Agents

import { Agent } from '@openai/agents';

const pirateAgent = new Agent({
  name: 'Pirate',
  instructions: 'Respond like a pirate – lots of “Arrr!”',
  model: 'o4-mini',
});

const robotAgent = pirateAgent.clone({
  name: 'Robot',
  instructions: 'Respond like a robot – be precise and factual.',
});

Forcing tool use

Supplying tools doesn’t guarantee the LLM will call one. You can force tool use with modelSettings.tool_choice:

    'auto' (default) – the LLM decides whether to use a tool.
    'required' – the LLM must call a tool (it can choose which one).
    'none' – the LLM must not call a tool.
    A specific tool name, e.g. 'calculator' – the LLM must call that particular tool.

Forcing tool use

import { Agent, tool } from '@openai/agents';
import { z } from 'zod';

const calculatorTool = tool({
  name: 'Calculator',
  description: 'Use this tool to answer questions about math problems.',
  parameters: z.object({ question: z.string() }),
  execute: async (input) => {
    throw new Error('TODO: implement this');
  },
});

const agent = new Agent({
  name: 'Strict tool user',
  instructions: 'Always answer using the calculator tool.',
  tools: [calculatorTool],
  modelSettings: { toolChoice: 'auto' },
});

Preventing infinite loops

After a tool call the SDK automatically resets tool_choice back to 'auto'. This prevents the model from entering an infinite loop where it repeatedly tries to call the tool. You can override this behavior via the resetToolChoice flag or by configuring toolUseBehavior:

    'run_llm_again' (default) – run the LLM again with the tool result.
    'stop_on_first_tool' – treat the first tool result as the final answer.
    { stopAtToolNames: ['my_tool'] } – stop when any of the listed tools is called.
    (context, toolResults) => ... – custom function returning whether the run should finish.

const agent = new Agent({
  ...,
  toolUseBehavior: 'stop_on_first_tool',
});
Running agents

Agents do nothing by themselves – you run them with the Runner class or the run() utility.
Simple run

import { Agent, run } from '@openai/agents';

const agent = new Agent({
  name: 'Assistant',
  instructions: 'You are a helpful assistant',
});

const result = await run(
  agent,
  'Write a haiku about recursion in programming.',
);
console.log(result.finalOutput);

// Code within the code,
// Functions calling themselves,
// Infinite loop's dance.

When you don’t need a custom runner, you can also use the run() utility, which runs a singleton default Runner instance.

Alternatively, you can create your own runner instance:
Simple run

import { Agent, Runner } from '@openai/agents';

const agent = new Agent({
  name: 'Assistant',
  instructions: 'You are a helpful assistant',
});

// You can pass custom configuration to the runner
const runner = new Runner();

const result = await runner.run(
  agent,
  'Write a haiku about recursion in programming.',
);
console.log(result.finalOutput);

// Code within the code,
// Functions calling themselves,
// Infinite loop's dance.

After running your agent, you will receive a result object that contains the final output and the full history of the run.
The agent loop

When you use the run method in Runner, you pass in a starting agent and input. The input can either be a string (which is considered a user message), or a list of input items, which are the items in the OpenAI Responses API.

The runner then runs a loop:

    Call the current agent’s model with the current input.
    Inspect the LLM response.
        Final output → return.
        Handoff → switch to the new agent, keep the accumulated conversation history, go to 1.
        Tool calls → execute tools, append their results to the conversation, go to 1.
    Throw MaxTurnsExceededError once maxTurns is reached.

Note

The rule for whether the LLM output is considered as a “final output” is that it produces text output with the desired type, and there are no tool calls.
Runner lifecycle

Create a Runner when your app starts and reuse it across requests. The instance stores global configuration such as model provider and tracing options. Only create another Runner if you need a completely different setup. For simple scripts you can also call run() which uses a default runner internally.
Run arguments

The input to the run() method is an initial agent to start the run on, input for the run and a set of options.

The input can either be a string (which is considered a user message), or a list of input items, or a RunState object in case you are building a human-in-the-loop agent.

The additional options are:
Option	Default	Description
stream	false	If true the call returns a StreamedRunResult and emits events as they arrive from the model.
context	–	Context object forwarded to every tool / guardrail / handoff. Learn more in the context guide.
maxTurns	10	Safety limit – throws MaxTurnsExceededError when reached.
signal	–	AbortSignal for cancellation.
Streaming

Streaming allows you to additionally receive streaming events as the LLM runs. Once the stream is started, the StreamedRunResult will contain the complete information about the run, including all the new outputs produces. You can iterate over the streaming events using a for await loop. Read more in the streaming guide.
Run config

If you are creating your own Runner instance, you can pass in a RunConfig object to configure the runner.
Field	Type	Purpose
model	string | Model	Force a specific model for all agents in the run.
modelProvider	ModelProvider	Resolves model names – defaults to the OpenAI provider.
modelSettings	ModelSettings	Global tuning parameters that override per‑agent settings.
handoffInputFilter	HandoffInputFilter	Mutates input items when performing handoffs (if the handoff itself doesn’t already define one).
inputGuardrails	InputGuardrail[]	Guardrails applied to the initial user input.
outputGuardrails	OutputGuardrail[]	Guardrails applied to the final output.
tracingDisabled	boolean	Disable OpenAI Tracing completely.
traceIncludeSensitiveData	boolean	Exclude LLM/tool inputs & outputs from traces while still emitting spans.
workflowName	string	Appears in the Traces dashboard – helps group related runs.
traceId / groupId	string	Manually specify the trace or group ID instead of letting the SDK generate one.
traceMetadata	Record<string, any>	Arbitrary metadata to attach to every span.
Conversations / chat threads

Each call to runner.run() (or run() utility) represents one turn in your application-level conversation. You choose how much of the RunResult you show the end‑user – sometimes only finalOutput, other times every generated item.
Example of carrying over the conversation history

import { Agent, AgentInputItem, run } from '@openai/agents';

let thread: AgentInputItem[] = [];

const agent = new Agent({
  name: 'Assistant',
});

async function userSays(text: string) {
  const result = await run(
    agent,
    thread.concat({ role: 'user', content: text }),
  );

  thread = result.history; // Carry over history + newly generated items
  return result.finalOutput;
}

await userSays('What city is the Golden Gate Bridge in?');
// -> "San Francisco"

await userSays('What state is it in?');
// -> "California"

See the chat example for an interactive version.
Exceptions

The SDK throws a small set of errors you can catch:

    MaxTurnsExceededError – maxTurns reached.
    ModelBehaviorError – model produced invalid output (e.g. malformed JSON, unknown tool).
    InputGuardrailTripwireTriggered / OutputGuardrailTripwireTriggered – guardrail violations.
    GuardrailExecutionError – guardrails failed to complete.
    ToolCallError – any of function tool calls failed.
    UserError – any error thrown based on configuration or user input.

All extend the base AgentsError class, which could provide the state property to access the current run state.

Here is an example code that handles GuardrailExecutionError:
Guardrail execution error

import {
  Agent,
  run,
  GuardrailExecutionError,
  InputGuardrail,
  InputGuardrailTripwireTriggered,
} from '@openai/agents';
import { z } from 'zod';

const guardrailAgent = new Agent({
  name: 'Guardrail check',
  instructions: 'Check if the user is asking you to do their math homework.',
  outputType: z.object({
    isMathHomework: z.boolean(),
    reasoning: z.string(),
  }),
});

const unstableGuardrail: InputGuardrail = {
  name: 'Math Homework Guardrail (unstable)',
  execute: async () => {
    throw new Error('Something is wrong!');
  },
};

const fallbackGuardrail: InputGuardrail = {
  name: 'Math Homework Guardrail (fallback)',
  execute: async ({ input, context }) => {
    const result = await run(guardrailAgent, input, { context });
    return {
      outputInfo: result.finalOutput,
      tripwireTriggered: result.finalOutput?.isMathHomework ?? false,
    };
  },
};

const agent = new Agent({
  name: 'Customer support agent',
  instructions:
    'You are a customer support agent. You help customers with their questions.',
  inputGuardrails: [unstableGuardrail],
});

async function main() {
  try {
    const input = 'Hello, can you help me solve for x: 2x + 3 = 11?';
    const result = await run(agent, input);
    console.log(result.finalOutput);
  } catch (e) {
    if (e instanceof GuardrailExecutionError) {
      console.error(`Guardrail execution failed: ${e}`);
      // If you want to retry the execution with different settings,
      // you can reuse the runner's latest state this way:
      if (e.state) {
        try {
          agent.inputGuardrails = [fallbackGuardrail]; // fallback
          const result = await run(agent, e.state);
          console.log(result.finalOutput);
        } catch (ee) {
          if (ee instanceof InputGuardrailTripwireTriggered) {
            console.log('Math homework guardrail tripped');
          }
        }
      }
    } else {
      throw e;
    }
  }
}

main().catch(console.error);

When you run the above example, you will see the following output:

Guardrail execution failed: Error: Input guardrail failed to complete: Error: Something is wrong!
Math homework guardrail tripped

Next steps

    Learn how to configure models.
    Provide your agents with tools.
    Add guardrails or tracing for production readiness.

Results

When you run your agent, you will either receive a:

    RunResult if you call run without stream: true
    StreamedRunResult if you call run with stream: true. For details on streaming, also check the streaming guide.

Final output

The finalOutput property contains the final output of the last agent that ran. This result is either:

    string — default for any agent that has no outputType defined
    unknown — if the agent has a JSON schema defined as output type. In this case the JSON was parsed but you still have to verify its type manually.
    z.infer<outputType> — if the agent has a Zod schema defined as output type. The output will automatically be parsed against this schema.
    undefined — if the agent did not produce an output (for example stopped before it could produce an output)

If you are using handoffs with different output types, you should use the Agent.create() method instead of the new Agent() constructor to create your agents.

This will enable the SDK to infer the output types across all possible handoffs and provide a union type for the finalOutput property.

For example:
Handoff final output types

import { Agent, run } from '@openai/agents';
import { z } from 'zod';

const refundAgent = new Agent({
  name: 'Refund Agent',
  instructions:
    'You are a refund agent. You are responsible for refunding customers.',
  outputType: z.object({
    refundApproved: z.boolean(),
  }),
});

const orderAgent = new Agent({
  name: 'Order Agent',
  instructions:
    'You are an order agent. You are responsible for processing orders.',
  outputType: z.object({
    orderId: z.string(),
  }),
});

const triageAgent = Agent.create({
  name: 'Triage Agent',
  instructions:
    'You are a triage agent. You are responsible for triaging customer issues.',
  handoffs: [refundAgent, orderAgent],
});

const result = await run(triageAgent, 'I need to a refund for my order');

const output = result.finalOutput;
// ^? { refundApproved: boolean } | { orderId: string } | string | undefined

Inputs for the next turn

There are two ways you can access the inputs for your next turn:

    result.history — contains a copy of both your input and the output of the agents.
    result.output — contains the output of the full agent run.

history is a convenient way to maintain a full history in a chat-like use case:
History loop

import { AgentInputItem, Agent, user, run } from '@openai/agents';

const agent = new Agent({
  name: 'Assistant',
  instructions:
    'You are a helpful assistant knowledgeable about recent AGI research.',
});

let history: AgentInputItem[] = [
  // initial message
  user('Are we there yet?'),
];

for (let i = 0; i < 10; i++) {
  // run 10 times
  const result = await run(agent, history);

  // update the history to the new output
  history = result.history;

  history.push(user('How about now?'));
}

Last agent

The lastAgent property contains the last agent that ran. Depending on your application, this is often useful for the next time the user inputs something. For example, if you have a frontline triage agent that hands off to a language-specific agent, you can store the last agent, and reuse it the next time the user messages the agent.

In streaming mode it can also be useful to access the currentAgent property that’s mapping to the current agent that is running.
New items

The newItems property contains the new items generated during the run. The items are RunItems. A run item wraps the raw item generated by the LLM. These can be used to access additionally to the output of the LLM which agent these events were associated with.

    RunMessageOutputItem indicates a message from the LLM. The raw item is the message generated.
    RunHandoffCallItem indicates that the LLM called the handoff tool. The raw item is the tool call item from the LLM.
    RunHandoffOutputItem indicates that a handoff occurred. The raw item is the tool response to the handoff tool call. You can also access the source/target agents from the item.
    RunToolCallItem indicates that the LLM invoked a tool.
    RunToolCallOutputItem indicates that a tool was called. The raw item is the tool response. You can also access the tool output from the item.
    RunReasoningItem indicates a reasoning item from the LLM. The raw item is the reasoning generated.
    RunToolApprovalItem indicates that the LLM requested approval for a tool call. The raw item is the tool call item from the LLM.

State

The state property contains the state of the run. Most of what is attached to the result is derived from the state but the state is serializable/deserializable and can also be used as input for a subsequent call to run in case you need to recover from an error or deal with an interruption.
Interruptions

If you are using needsApproval in your agent, your run might trigger some interruptions that you need to handle before continuing. In that case interruptions will be an array of ToolApprovalItems that caused the interruption. Check out the human-in-the-loop guide for more information on how to work with interruptions.
Other information
Raw responses

The rawResponses property contains the raw LLM responses generated by the model during the agent run.
Last response ID

The lastResponseId property contains the ID of the last response generated by the model during the agent run.
Guardrail results

The inputGuardrailResults and outputGuardrailResults properties contain the results of the guardrails, if any. Guardrail results can sometimes contain useful information you want to log or store, so we make these available to you.
Original input
The input property contains the original input you provided to the run method. In most cases you won’t need this, but it’s available in case you do.


Tools

Tools let an Agent take actions – fetch data, call external APIs, execute code, or even use a computer. The JavaScript/TypeScript SDK supports four categories:

    Hosted tools – run alongside the model on OpenAI servers. (web search, file search, computer use, code interpreter, image generation)
    Function tools – wrap any local function with a JSON schema so the LLM can call it.
    Agents as tools – expose an entire Agent as a callable tool.
    Local MCP servers – attach a Model Context Protocol server running on your machine.

1. Hosted tools

When you use the OpenAIResponsesModel you can add the following built‑in tools:
Tool	Type string	Purpose
Web search	'web_search'	Internet search.
File / retrieval search	'file_search'	Query vector stores hosted on OpenAI.
Computer use	'computer'	Automate GUI interactions.
Code Interpreter	'code_interpreter'	Run code in a sandboxed environment.
Image generation	'image_generation'	Generate images based on text.
Hosted tools

import { Agent, webSearchTool, fileSearchTool } from '@openai/agents';

const agent = new Agent({
  name: 'Travel assistant',
  tools: [webSearchTool(), fileSearchTool('VS_ID')],
});

The exact parameter sets match the OpenAI Responses API – refer to the official documentation for advanced options like rankingOptions or semantic filters.
2. Function tools

You can turn any function into a tool with the tool() helper.
Function tool with Zod parameters

import { tool } from '@openai/agents';
import { z } from 'zod';

const getWeatherTool = tool({
  name: 'get_weather',
  description: 'Get the weather for a given city',
  parameters: z.object({ city: z.string() }),
  async execute({ city }) {
    return `The weather in ${city} is sunny.`;
  },
});

Options reference
Field	Required	Description
name	No	Defaults to the function name (e.g., get_weather).
description	Yes	Clear, human-readable description shown to the LLM.
parameters	Yes	Either a Zod schema or a raw JSON schema object. Zod parameters automatically enable strict mode.
strict	No	When true (default), the SDK returns a model error if the arguments don’t validate. Set to false for fuzzy matching.
execute	Yes	(args, context) => string | Promise<string>– your business logic. The optional second parameter is theRunContext.
errorFunction	No	Custom handler (context, error) => string for transforming internal errors into a user-visible string.
Non‑strict JSON‑schema tools

If you need the model to guess invalid or partial input you can disable strict mode when using raw JSON schema:
Non-strict JSON schema tools

import { tool } from '@openai/agents';

interface LooseToolInput {
  text: string;
}

const looseTool = tool({
  description: 'Echo input; be forgiving about typos',
  strict: false,
  parameters: {
    type: 'object',
    properties: { text: { type: 'string' } },
    required: ['text'],
    additionalProperties: true,
  },
  execute: async (input) => {
    // because strict is false we need to do our own verification
    if (typeof input !== 'object' || input === null || !('text' in input)) {
      return 'Invalid input. Please try again';
    }
    return (input as LooseToolInput).text;
  },
});

3. Agents as tools

Sometimes you want an Agent to assist another Agent without fully handing off the conversation. Use agent.asTool():
Agents as tools

import { Agent } from '@openai/agents';

const summarizer = new Agent({
  name: 'Summarizer',
  instructions: 'Generate a concise summary of the supplied text.',
});

const summarizerTool = summarizer.asTool({
  toolName: 'summarize_text',
  toolDescription: 'Generate a concise summary of the supplied text.',
});

const mainAgent = new Agent({
  name: 'Research assistant',
  tools: [summarizerTool],
});

Under the hood the SDK:

    Creates a function tool with a single input parameter.
    Runs the sub‑agent with that input when the tool is called.
    Returns either the last message or the output extracted by customOutputExtractor.

4. Local MCP servers

You can expose tools via a local Model Context Protocol server and attach them to an agent. Use MCPServerStdio to spawn and connect to the server:
Local MCP server

import { Agent, MCPServerStdio } from '@openai/agents';

const server = new MCPServerStdio({
  fullCommand: 'npx -y @modelcontextprotocol/server-filesystem ./sample_files',
});

await server.connect();

const agent = new Agent({
  name: 'Assistant',
  mcpServers: [server],
});

See filesystem-example.ts for a complete example.
Tool use behavior

Refer to the Agents guide for controlling when and how a model must use tools (tool_choice, toolUseBehavior, etc.).
Best practices

    Short, explicit descriptions – describe what the tool does and when to use it.
    Validate inputs – use Zod schemas for strict JSON validation where possible.
    Avoid side‑effects in error handlers – errorFunction should return a helpful string, not throw.
    One responsibility per tool – small, composable tools lead to better model reasoning.

Next steps
Learn about forcing tool use.
Add guardrails to validate tool inputs or outputs.

Orchestrating multiple agents

Orchestration refers to the flow of agents in your app. Which agents run, in what order, and how do they decide what happens next? There are two main ways to orchestrate agents:

    Allowing the LLM to make decisions: this uses the intelligence of an LLM to plan, reason, and decide on what steps to take based on that.
    Orchestrating via code: determining the flow of agents via your code.

You can mix and match these patterns. Each has their own tradeoffs, described below.
Orchestrating via LLM

An agent is an LLM equipped with instructions, tools and handoffs. This means that given an open-ended task, the LLM can autonomously plan how it will tackle the task, using tools to take actions and acquire data, and using handoffs to delegate tasks to sub-agents. For example, a research agent could be equipped with tools like:

    Web search to find information online
    File search and retrieval to search through proprietary data and connections
    Computer use to take actions on a computer
    Code execution to do data analysis
    Handoffs to specialized agents that are great at planning, report writing and more.

This pattern is great when the task is open-ended and you want to rely on the intelligence of an LLM. The most important tactics here are:

    Invest in good prompts. Make it clear what tools are available, how to use them, and what parameters it must operate within.
    Monitor your app and iterate on it. See where things go wrong, and iterate on your prompts.
    Allow the agent to introspect and improve. For example, run it in a loop, and let it critique itself; or, provide error messages and let it improve.
    Have specialized agents that excel in one task, rather than having a general purpose agent that is expected to be good at anything.
    Invest in evals. This lets you train your agents to improve and get better at tasks.

Orchestrating via code

While orchestrating via LLM is powerful, orchestrating via code makes tasks more deterministic and predictable, in terms of speed, cost and performance. Common patterns here are:

    Using structured outputs to generate well formed data that you can inspect with your code. For example, you might ask an agent to classify the task into a few categories, and then pick the next agent based on the category.
    Chaining multiple agents by transforming the output of one into the input of the next. You can decompose a task like writing a blog post into a series of steps - do research, write an outline, write the blog post, critique it, and then improve it.
    Running the agent that performs the task in a while loop with an agent that evaluates and provides feedback, until the evaluator says the output passes certain criteria.
    Running multiple agents in parallel, e.g. via JavaScript primitives like Promise.all. This is useful for speed when you have multiple tasks that don’t depend on each other.

We have a number of examples in examples/agent-patterns.

andoffs

Handoffs let an agent delegate part of a conversation to another agent. This is useful when different agents specialise in specific areas. In a customer support app for example, you might have agents that handle bookings, refunds or FAQs.

Handoffs are represented as tools to the LLM. If you hand off to an agent called Refund Agent, the tool name would be transfer_to_refund_agent.
Creating a handoff

Every agent accepts a handoffs option. It can contain other Agent instances or Handoff objects returned by the handoff() helper.
Basic usage
Basic handoffs

import { Agent, handoff } from '@openai/agents';

const billingAgent = new Agent({ name: 'Billing agent' });
const refundAgent = new Agent({ name: 'Refund agent' });

// Use Agent.create method to ensure the finalOutput type considers handoffs
const triageAgent = Agent.create({
  name: 'Triage agent',
  handoffs: [billingAgent, handoff(refundAgent)],
});

Customising handoffs via handoff()

The handoff() function lets you tweak the generated tool.

    agent – the agent to hand off to.
    toolNameOverride – override the default transfer_to_<agent_name> tool name.
    toolDescriptionOverride – override the default tool description.
    onHandoff – callback when the handoff occurs. Receives a RunContext and optionally parsed input.
    inputType – expected input schema for the handoff.
    inputFilter – filter the history passed to the next agent.

Customized handoffs

import { Agent, handoff, RunContext } from '@openai/agents';

function onHandoff(ctx: RunContext) {
  console.log('Handoff called');
}

const agent = new Agent({ name: 'My agent' });

const handoffObj = handoff(agent, {
  onHandoff,
  toolNameOverride: 'custom_handoff_tool',
  toolDescriptionOverride: 'Custom description',
});

Handoff inputs

Sometimes you want the LLM to provide data when invoking a handoff. Define an input schema and use it in handoff().
Handoff inputs

import { z } from 'zod';
import { Agent, handoff, RunContext } from '@openai/agents';

const EscalationData = z.object({ reason: z.string() });
type EscalationData = z.infer<typeof EscalationData>;

async function onHandoff(
  ctx: RunContext<EscalationData>,
  input: EscalationData | undefined,
) {
  console.log(`Escalation agent called with reason: ${input?.reason}`);
}

const agent = new Agent<EscalationData>({ name: 'Escalation agent' });

const handoffObj = handoff(agent, {
  onHandoff,
  inputType: EscalationData,
});

Input filters

By default a handoff receives the entire conversation history. To modify what gets passed to the next agent, provide an inputFilter. Common helpers live in @openai/agents-core/extensions.
Input filters

import { Agent, handoff } from '@openai/agents';
import { removeAllTools } from '@openai/agents-core/extensions';

const agent = new Agent({ name: 'FAQ agent' });

const handoffObj = handoff(agent, {
  inputFilter: removeAllTools,
});

Recommended prompts

LLMs respond more reliably when your prompts mention handoffs. The SDK exposes a recommended prefix via RECOMMENDED_PROMPT_PREFIX.
Recommended prompts

import { Agent } from '@openai/agents';
import { RECOMMENDED_PROMPT_PREFIX } from '@openai/agents-core/extensions';

const billingAgent = new Agent({
  name: 'Billing agent',
  instructions: `${RECOMMENDED_PROMPT_PREFIX}
Fill in the rest of your prompt here.`,
});


Context management

Context is an overloaded term. There are two main classes of context you might care about:

    Local context that your code can access during a run: dependencies or data needed by tools, callbacks like onHandoff, and lifecycle hooks.
    Agent/LLM context that the language model can see when generating a response.

Local context

Local context is represented by the RunContext<T> type. You create any object to hold your state or dependencies and pass it to Runner.run(). All tool calls and hooks receive a RunContext wrapper so they can read from or modify that object.
Local context example

import { Agent, run, RunContext, tool } from '@openai/agents';
import { z } from 'zod';

interface UserInfo {
  name: string;
  uid: number;
}

const fetchUserAge = tool({
  name: 'fetch_user_age',
  description: 'Return the age of the current user',
  parameters: z.object({}),
  execute: async (
    _args,
    runContext?: RunContext<UserInfo>,
  ): Promise<string> => {
    return `User ${runContext?.context.name} is 47 years old`;
  },
});

async function main() {
  const userInfo: UserInfo = { name: 'John', uid: 123 };

  const agent = new Agent<UserInfo>({
    name: 'Assistant',
    tools: [fetchUserAge],
  });

  const result = await run(agent, 'What is the age of the user?', {
    context: userInfo,
  });

  console.log(result.finalOutput);
  // The user John is 47 years old.
}

if (require.main === module) {
  main().catch(console.error);
}

Every agent, tool and hook participating in a single run must use the same type of context.

Use local context for things like:

    Data about the run (user name, IDs, etc.)
    Dependencies such as loggers or data fetchers
    Helper functions

Note

The context object is not sent to the LLM. It is purely local and you can read from or write to it freely.
Agent/LLM context

When the LLM is called, the only data it can see comes from the conversation history. To make additional information available you have a few options:

    Add it to the Agent instructions – also known as a system or developer message. This can be a static string or a function that receives the context and returns a string.
    Include it in the input when calling Runner.run(). This is similar to the instructions technique but lets you place the message lower in the chain of command.
    Expose it via function tools so the LLM can fetch data on demand.
    Use retrieval or web search tools to ground responses in relevant data from files, databases, or the web.
