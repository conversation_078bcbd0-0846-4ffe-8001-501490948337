name: Deploy Zero Server

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment (preview | staging | production)'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 15

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9.12.0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'pnpm'
        cache-dependency-path: 'pnpm-lock.yaml'

    - name: Cache dependencies and Fly CLI
      uses: actions/cache@v4
      with:
        path: |
          ~/.pnpm-store
          ~/.fly
          packages/zero-schema/dist
        key: ${{ runner.os }}-zero-server-${{ hashFiles('**/pnpm-lock.yaml', 'packages/zero-schema/src/**/*') }}-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-zero-server-${{ hashFiles('**/pnpm-lock.yaml', 'packages/zero-schema/src/**/*') }}-
          ${{ runner.os }}-zero-server-

    - name: Install system dependencies
      run: sudo apt-get update && sudo apt-get install -y libreadline-dev

    - name: Download schema artifacts
      uses: actions/download-artifact@v4
      with:
        name: zero-schema-dist
        path: packages/zero-schema/dist

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Setup Fly CLI
      run: |
        if [ ! -f "$HOME/.fly/bin/fly" ]; then
          curl -L https://fly.io/install.sh | sh
        fi
        echo "$HOME/.fly/bin" >> $GITHUB_PATH

    - name: Map environment for Zero Server
      id: env
      run: |
        case "${{ inputs.environment }}" in
          preview)
            echo "environment=DEV" >> $GITHUB_OUTPUT
            echo "app_name=axc-dev-zero-cache" >> $GITHUB_OUTPUT
            echo "config_file=deploy/fly-dev.toml" >> $GITHUB_OUTPUT
            ;;
          staging)
            echo "environment=STAGING" >> $GITHUB_OUTPUT
            echo "app_name=axc-staging-zero-cache" >> $GITHUB_OUTPUT
            echo "config_file=deploy/fly-staging.toml" >> $GITHUB_OUTPUT
            ;;
          production)
            echo "environment=PROD" >> $GITHUB_OUTPUT
            echo "app_name=axc-prod-zero-cache" >> $GITHUB_OUTPUT
            echo "config_file=fly.toml" >> $GITHUB_OUTPUT
            ;;
          *)
            echo "❌ Unsupported environment: ${{ inputs.environment }}"
            exit 1
            ;;
        esac

    - name: Deploy Zero Server with optimized retry
      id: deploy
      run: |
        cd apps/zero-server
        
        MAX_RETRIES=2  # Reduced from 3
        RETRY_COUNT=0
        WAIT_TIME=15  # Reduced initial wait
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "🚀 Deployment attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
          
          if fly deploy --config ${{ steps.env.outputs.config_file }} --app ${{ steps.env.outputs.app_name }}; then
            echo "✅ Zero Server deployment successful!"
            echo "deployment_success=true" >> $GITHUB_OUTPUT
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⚠️ Deployment failed, retrying in ${WAIT_TIME}s..."
              sleep $WAIT_TIME
              WAIT_TIME=$((WAIT_TIME * 2))  # Exponential backoff
            else
              echo "❌ All deployment attempts failed"
              echo "deployment_success=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          fi
        done
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Deploy Permissions with optimized retry
      if: steps.deploy.outputs.deployment_success == 'true'
      run: |
        cd apps/zero-server
        
        echo "⏳ Waiting for Zero Server to be ready..."
        sleep 20  # Reduced wait time
        
        MAX_RETRIES=2  # Reduced from 3
        RETRY_COUNT=0
        WAIT_TIME=15
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "🔑 Permissions deployment attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
          
          if timeout 90s npx zero-deploy-permissions --schema-path="../../packages/zero-schema/src/schema.ts"; then
            echo "✅ Permissions deployed successfully!"
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⚠️ Permissions deployment failed, retrying in ${WAIT_TIME}s..."
              sleep $WAIT_TIME
            else
              echo "❌ Permissions deployment failed after all retries"
              echo "⚠️ Zero Server is deployed but permissions may need manual deployment"
              break
            fi
          fi
        done
      env:
        ZERO_UPSTREAM_DB: ${{ secrets[format('SUPABASE_CONNECTION_{0}', steps.env.outputs.environment)] }}

    - name: Quick health check
      if: steps.deploy.outputs.deployment_success == 'true'
      run: |
        echo "🔍 Verifying Zero Server deployment..."
        
        sleep 10  # Reduced wait time
        
        APP_URL="https://${{ steps.env.outputs.app_name }}.fly.dev"
        
        if curl -f --max-time 20 "$APP_URL/health" 2>/dev/null; then
          echo "✅ Zero Server health check passed"
        else
          echo "⚠️ Health check failed, but deployment completed"
        fi

    - name: Add deployment summary
      if: always()
      run: |
        echo "## 🚀 Zero Server Deployment" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ steps.env.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
        echo "**App Name:** ${{ steps.env.outputs.app_name }}" >> $GITHUB_STEP_SUMMARY
        echo "**Status:** ${{ steps.deploy.outputs.deployment_success == 'true' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        if [[ "${{ steps.deploy.outputs.deployment_success }}" == "true" ]]; then
          echo "**Service URL:** https://${{ steps.env.outputs.app_name }}.fly.dev" >> $GITHUB_STEP_SUMMARY
        fi