name: Deploy Supabase Migrations

on:
  workflow_call:
    inputs:
      environment:
        description: 'Target environment (preview | staging | production)'
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    timeout-minutes: 10

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Cache Supabase CLI and migrations
      uses: actions/cache@v4
      with:
        path: |
          ~/.cache/supabase
          apps/web/supabase/migrations
        key: ${{ runner.os }}-supabase-${{ hashFiles('apps/web/supabase/migrations/**/*.sql') }}-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-supabase-${{ hashFiles('apps/web/supabase/migrations/**/*.sql') }}-
          ${{ runner.os }}-supabase-

    - name: Install Supabase CLI
      uses: supabase/setup-cli@v1
      with:
        version: latest

    - name: Set environment variables
      id: env
      run: |
        ENVIRONMENT="${{ inputs.environment }}"
        echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
        
        case "$ENVIRONMENT" in
          production)
            echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_PRODUCTION }}@db.${{ secrets.SUPABASE_PROJECT_ID_PRODUCTION }}.supabase.co:5432/postgres" >> $GITHUB_ENV
            echo "project_id=${{ secrets.SUPABASE_PROJECT_ID_PRODUCTION }}" >> $GITHUB_OUTPUT
            ;;
          staging)
            echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_STAGING }}@db.${{ secrets.SUPABASE_PROJECT_ID_STAGING }}.supabase.co:5432/postgres" >> $GITHUB_ENV
            echo "project_id=${{ secrets.SUPABASE_PROJECT_ID_STAGING }}" >> $GITHUB_OUTPUT
            ;;
          preview)
            echo "SUPABASE_DB_URL=postgresql://postgres:${{ secrets.SUPABASE_DB_PASSWORD_PREVIEW }}@db.qbdtpzsbkacowlbdoolk.supabase.co:5432/postgres" >> $GITHUB_ENV
            echo "project_id=qbdtpzsbkacowlbdoolk" >> $GITHUB_OUTPUT
            ;;
          *)
            echo "❌ Unsupported environment: $ENVIRONMENT"
            exit 1
            ;;
        esac

    - name: Authenticate with Supabase
      run: supabase login --token ${{ secrets.SUPABASE_ACCESS_TOKEN }}

    - name: Deploy migrations with optimized retry
      id: deploy
      run: |
        cd apps/web
        
        # Check if migrations exist
        MIGRATION_COUNT=$(find supabase/migrations -name "*.sql" 2>/dev/null | wc -l)
        if [ $MIGRATION_COUNT -eq 0 ]; then
          echo "⏭️ No migrations found, skipping deployment"
          echo "deployment_success=true" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        echo "📋 Found $MIGRATION_COUNT migration files"
        
        # Optimized retry with exponential backoff
        MAX_RETRIES=2
        RETRY_COUNT=0
        WAIT_TIME=5
        
        while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
          echo "🚀 Migration deployment attempt $((RETRY_COUNT + 1))/$MAX_RETRIES"
          
          if timeout 180s supabase db push --db-url ${{ env.SUPABASE_DB_URL }} --include-all; then
            echo "✅ Supabase migrations deployed successfully!"
            echo "deployment_success=true" >> $GITHUB_OUTPUT
            break
          else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
              echo "⚠️ Migration deployment failed, retrying in ${WAIT_TIME}s..."
              sleep $WAIT_TIME
              WAIT_TIME=$((WAIT_TIME * 2))  # Exponential backoff
            else
              echo "❌ All migration deployment attempts failed"
              echo "deployment_success=false" >> $GITHUB_OUTPUT
              exit 1
            fi
          fi
        done

    - name: Quick verification
      if: steps.deploy.outputs.deployment_success == 'true'
      run: |
        echo "🔍 Quick database verification..."
        sleep 5  # Reduced wait time
        
        if timeout 15s psql "${{ env.SUPABASE_DB_URL }}" -c "SELECT 1;" > /dev/null 2>&1; then
          echo "✅ Database connectivity verified"
        else
          echo "⚠️ Database connectivity test failed, but migrations completed"
        fi

    - name: Add deployment summary
      if: always()
      run: |
        echo "## 🗄️ Supabase Migrations Deployed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Environment:** ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
        echo "**Project ID:** ${{ steps.env.outputs.project_id }}" >> $GITHUB_STEP_SUMMARY
        echo "**Status:** ${{ steps.deploy.outputs.deployment_success == 'true' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
        echo "**Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY