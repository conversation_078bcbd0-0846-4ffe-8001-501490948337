export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      accounts: {
        Row: {
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          updated_at: string | null
          updated_by: string | null
          website: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name: string
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          updated_at?: string | null
          updated_by?: string | null
          website?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          email?: string | null
          id?: string
          is_personal_account?: boolean
          name?: string
          picture_url?: string | null
          primary_owner_user_id?: string
          public_data?: Json
          slug?: string | null
          updated_at?: string | null
          updated_by?: string | null
          website?: string | null
        }
        Relationships: []
      }
      accounts_memberships: {
        Row: {
          account_id: string
          account_role: string
          created_at: string
          created_by: string | null
          updated_at: string
          updated_by: string | null
          user_id: string
        }
        Insert: {
          account_id: string
          account_role: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id: string
        }
        Update: {
          account_id?: string
          account_role?: string
          created_at?: string
          created_by?: string | null
          updated_at?: string
          updated_by?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["account_role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      assets: {
        Row: {
          account_id: string
          file_extension: string | null
          file_name: string
          file_path: string
          file_size: number | null
          file_type: string | null
          folder_name: string
          has_transcript: boolean | null
          id: string
          original_url: string | null
          processing_completed_at: string | null
          processing_error: string | null
          processing_started_at: string | null
          processing_status: string | null
          transcript_extracted_at: string | null
          transcript_path: string | null
          transcript_url: string | null
          updated_at: string | null
          uploaded_at: string | null
        }
        Insert: {
          account_id: string
          file_extension?: string | null
          file_name: string
          file_path: string
          file_size?: number | null
          file_type?: string | null
          folder_name: string
          has_transcript?: boolean | null
          id?: string
          original_url?: string | null
          processing_completed_at?: string | null
          processing_error?: string | null
          processing_started_at?: string | null
          processing_status?: string | null
          transcript_extracted_at?: string | null
          transcript_path?: string | null
          transcript_url?: string | null
          updated_at?: string | null
          uploaded_at?: string | null
        }
        Update: {
          account_id?: string
          file_extension?: string | null
          file_name?: string
          file_path?: string
          file_size?: number | null
          file_type?: string | null
          folder_name?: string
          has_transcript?: boolean | null
          id?: string
          original_url?: string | null
          processing_completed_at?: string | null
          processing_error?: string | null
          processing_started_at?: string | null
          processing_status?: string | null
          transcript_extracted_at?: string | null
          transcript_path?: string | null
          transcript_url?: string | null
          updated_at?: string | null
          uploaded_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "assets_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assets_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "assets_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      ayrshare_social_profiles: {
        Row: {
          company_id: string | null
          connected_at: string | null
          created_at: string
          display_name: string | null
          headline: string | null
          id: string
          is_connected: boolean | null
          is_shared: boolean | null
          platform: string
          post_history: Json | null
          profile_key: string | null
          profile_url: string | null
          refId: string | null
          refresh_days_remaining: number | null
          refresh_required: string | null
          subscription_type: string | null
          updated_at: string | null
          user_id: string | null
          user_image: string | null
          username: string | null
          verified_type: string | null
        }
        Insert: {
          company_id?: string | null
          connected_at?: string | null
          created_at?: string
          display_name?: string | null
          headline?: string | null
          id?: string
          is_connected?: boolean | null
          is_shared?: boolean | null
          platform: string
          post_history?: Json | null
          profile_key?: string | null
          profile_url?: string | null
          refId?: string | null
          refresh_days_remaining?: number | null
          refresh_required?: string | null
          subscription_type?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_image?: string | null
          username?: string | null
          verified_type?: string | null
        }
        Update: {
          company_id?: string | null
          connected_at?: string | null
          created_at?: string
          display_name?: string | null
          headline?: string | null
          id?: string
          is_connected?: boolean | null
          is_shared?: boolean | null
          platform?: string
          post_history?: Json | null
          profile_key?: string | null
          profile_url?: string | null
          refId?: string | null
          refresh_days_remaining?: number | null
          refresh_required?: string | null
          subscription_type?: string | null
          updated_at?: string | null
          user_id?: string | null
          user_image?: string | null
          username?: string | null
          verified_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "ayrshare_social_profiles_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_social_profiles_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_social_profiles_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_social_profiles_profile_key_fkey"
            columns: ["profile_key"]
            isOneToOne: false
            referencedRelation: "ayrshare_user_profile"
            referencedColumns: ["profileKey"]
          },
        ]
      }
      ayrshare_user_profile: {
        Row: {
          company_id: string
          created_at: string
          description: string | null
          id: string
          is_active: boolean
          is_shared: boolean | null
          messagingActive: boolean | null
          permissions: Json | null
          profile_name: string
          profileKey: string
          refId: string
          title: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          company_id: string
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          is_shared?: boolean | null
          messagingActive?: boolean | null
          permissions?: Json | null
          profile_name?: string
          profileKey: string
          refId: string
          title?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          company_id?: string
          created_at?: string
          description?: string | null
          id?: string
          is_active?: boolean
          is_shared?: boolean | null
          messagingActive?: boolean | null
          permissions?: Json | null
          profile_name?: string
          profileKey?: string
          refId?: string
          title?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ayrshare_user_profile_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_user_profile_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_user_profile_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_user_profile_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_user_profile_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ayrshare_user_profile_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      billing_customers: {
        Row: {
          account_id: string
          customer_id: string
          email: string | null
          id: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Insert: {
          account_id: string
          customer_id: string
          email?: string | null
          id?: number
          provider: Database["public"]["Enums"]["billing_provider"]
        }
        Update: {
          account_id?: string
          customer_id?: string
          email?: string | null
          id?: number
          provider?: Database["public"]["Enums"]["billing_provider"]
        }
        Relationships: [
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_customers_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_ideas: {
        Row: {
          brief: Json | null
          brief_blocks: Json | null
          campaign_id: string
          channels: Json | null
          company_id: string
          content: string
          content_blocks: Json | null
          content_types: Json | null
          created_at: string
          id: string
          is_selected: boolean | null
          languages: Json | null
          metadata: Json | null
          title: string | null
        }
        Insert: {
          brief?: Json | null
          brief_blocks?: Json | null
          campaign_id: string
          channels?: Json | null
          company_id: string
          content: string
          content_blocks?: Json | null
          content_types?: Json | null
          created_at?: string
          id?: string
          is_selected?: boolean | null
          languages?: Json | null
          metadata?: Json | null
          title?: string | null
        }
        Update: {
          brief?: Json | null
          brief_blocks?: Json | null
          campaign_id?: string
          channels?: Json | null
          company_id?: string
          content?: string
          content_blocks?: Json | null
          content_types?: Json | null
          created_at?: string
          id?: string
          is_selected?: boolean | null
          languages?: Json | null
          metadata?: Json | null
          title?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "campaign_ideas_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "company_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_ideas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_ideas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_ideas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_personas: {
        Row: {
          campaign_id: string
          created_at: string | null
          id: string
          is_primary: boolean | null
          persona_id: string
        }
        Insert: {
          campaign_id: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id: string
        }
        Update: {
          campaign_id?: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "campaign_personas_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "company_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "campaign_personas_persona_id_fkey"
            columns: ["persona_id"]
            isOneToOne: false
            referencedRelation: "personas"
            referencedColumns: ["id"]
          },
        ]
      }
      campaign_templates: {
        Row: {
          created_at: string
          description: string | null
          duration_weeks: number | null
          goal: string | null
          id: string
          image_url: string | null
          style: Json | null
          title: string | null
        }
        Insert: {
          created_at?: string
          description?: string | null
          duration_weeks?: number | null
          goal?: string | null
          id?: string
          image_url?: string | null
          style?: Json | null
          title?: string | null
        }
        Update: {
          created_at?: string
          description?: string | null
          duration_weeks?: number | null
          goal?: string | null
          id?: string
          image_url?: string | null
          style?: Json | null
          title?: string | null
        }
        Relationships: []
      }
      company_brand: {
        Row: {
          brand_name: string | null
          brand_profile: Json | null
          company_id: string
          created_at: string
          error_generating: boolean | null
          has_brand_setup: boolean | null
          id: string
          is_generating: boolean | null
          messaging_strategy: Json | null
          product_catalog: Json | null
          prompt_library: Json | null
          updated_at: string | null
          visual_identity: Json | null
        }
        Insert: {
          brand_name?: string | null
          brand_profile?: Json | null
          company_id: string
          created_at?: string
          error_generating?: boolean | null
          has_brand_setup?: boolean | null
          id?: string
          is_generating?: boolean | null
          messaging_strategy?: Json | null
          product_catalog?: Json | null
          prompt_library?: Json | null
          updated_at?: string | null
          visual_identity?: Json | null
        }
        Update: {
          brand_name?: string | null
          brand_profile?: Json | null
          company_id?: string
          created_at?: string
          error_generating?: boolean | null
          has_brand_setup?: boolean | null
          id?: string
          is_generating?: boolean | null
          messaging_strategy?: Json | null
          product_catalog?: Json | null
          prompt_library?: Json | null
          updated_at?: string | null
          visual_identity?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "company_brand_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_brand_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_brand_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      company_campaigns: {
        Row: {
          ayrshare_post_id: string | null
          channels: Json | null
          color_tag: string | null
          company_id: string
          created_at: string
          documents: Json | null
          end_date: string | null
          error_generating: boolean | null
          external_research: Json | null
          guidelines: string | null
          has_reached_summary: boolean | null
          id: string
          identity: string | null
          is_generating: boolean | null
          kpis: string | null
          messaging: string | null
          metadata: Json | null
          name: string
          objective: string | null
          objectives: string | null
          personality: string | null
          personas: string | null
          posts_per_week: number | null
          products: Json | null
          slug: string
          start_date: string | null
          status: Database["public"]["Enums"]["campaign_status"] | null
          target_icps: Json | null
          target_personas: Json | null
          targetAudience: string | null
          tone: string | null
          updated_at: string | null
          user_id: string
          value_prop: string | null
          visualStyle: string | null
          voice: string | null
        }
        Insert: {
          ayrshare_post_id?: string | null
          channels?: Json | null
          color_tag?: string | null
          company_id: string
          created_at?: string
          documents?: Json | null
          end_date?: string | null
          error_generating?: boolean | null
          external_research?: Json | null
          guidelines?: string | null
          has_reached_summary?: boolean | null
          id?: string
          identity?: string | null
          is_generating?: boolean | null
          kpis?: string | null
          messaging?: string | null
          metadata?: Json | null
          name: string
          objective?: string | null
          objectives?: string | null
          personality?: string | null
          personas?: string | null
          posts_per_week?: number | null
          products?: Json | null
          slug: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_icps?: Json | null
          target_personas?: Json | null
          targetAudience?: string | null
          tone?: string | null
          updated_at?: string | null
          user_id: string
          value_prop?: string | null
          visualStyle?: string | null
          voice?: string | null
        }
        Update: {
          ayrshare_post_id?: string | null
          channels?: Json | null
          color_tag?: string | null
          company_id?: string
          created_at?: string
          documents?: Json | null
          end_date?: string | null
          error_generating?: boolean | null
          external_research?: Json | null
          guidelines?: string | null
          has_reached_summary?: boolean | null
          id?: string
          identity?: string | null
          is_generating?: boolean | null
          kpis?: string | null
          messaging?: string | null
          metadata?: Json | null
          name?: string
          objective?: string | null
          objectives?: string | null
          personality?: string | null
          personas?: string | null
          posts_per_week?: number | null
          products?: Json | null
          slug?: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["campaign_status"] | null
          target_icps?: Json | null
          target_personas?: Json | null
          targetAudience?: string | null
          tone?: string | null
          updated_at?: string | null
          user_id?: string
          value_prop?: string | null
          visualStyle?: string | null
          voice?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "company_campaigns_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_campaigns_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_campaigns_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      company_content: {
        Row: {
          archived: boolean | null
          assigned_to: string | null
          avatar_presenter_id: string | null
          avatar_script: string | null
          avatar_video_id: string | null
          avatar_video_url: string | null
          avatar_voice_id: string | null
          ayrshare_post_id: string | null
          ayrshare_post_status: string | null
          campaign_id: string | null
          channel: string | null
          company_id: string | null
          content: string | null
          content_editor_template: Json | null
          content_template: string | null
          content_type: string | null
          created_at: string | null
          error_generating: boolean | null
          has_avatar: boolean | null
          has_image: boolean | null
          has_video_presentation: boolean | null
          id: string
          idea_id: string | null
          image_path: string | null
          image_url: string | null
          image_urls: Json | null
          is_avatar_ready: boolean | null
          is_draft: boolean | null
          is_generating: boolean | null
          is_long_form: boolean | null
          is_paused: boolean | null
          is_posted: boolean | null
          is_published: boolean | null
          is_scheduled: boolean | null
          kanban_order: number | null
          language: string | null
          published_by: string | null
          published_url: string | null
          schedule_date: string | null
          scheduled_at: string | null
          scheduled_publishing_time: string | null
          seo_keywords: Json | null
          status: string | null
          task_description: string | null
          task_id: string | null
          task_title: string | null
          trend_keywords: Json | null
          updated_at: string | null
          video_editor_aspect_ratio: Json | null
          video_editor_overlays: Json | null
          video_editor_player_dimensions: Json | null
          video_presentation_render_params: Json | null
          video_presentation_script: string | null
          video_presentation_url: string | null
          visual_description: string | null
          visual_description_group: Json | null
        }
        Insert: {
          archived?: boolean | null
          assigned_to?: string | null
          avatar_presenter_id?: string | null
          avatar_script?: string | null
          avatar_video_id?: string | null
          avatar_video_url?: string | null
          avatar_voice_id?: string | null
          ayrshare_post_id?: string | null
          ayrshare_post_status?: string | null
          campaign_id?: string | null
          channel?: string | null
          company_id?: string | null
          content?: string | null
          content_editor_template?: Json | null
          content_template?: string | null
          content_type?: string | null
          created_at?: string | null
          error_generating?: boolean | null
          has_avatar?: boolean | null
          has_image?: boolean | null
          has_video_presentation?: boolean | null
          id?: string
          idea_id?: string | null
          image_path?: string | null
          image_url?: string | null
          image_urls?: Json | null
          is_avatar_ready?: boolean | null
          is_draft?: boolean | null
          is_generating?: boolean | null
          is_long_form?: boolean | null
          is_paused?: boolean | null
          is_posted?: boolean | null
          is_published?: boolean | null
          is_scheduled?: boolean | null
          kanban_order?: number | null
          language?: string | null
          published_by?: string | null
          published_url?: string | null
          schedule_date?: string | null
          scheduled_at?: string | null
          scheduled_publishing_time?: string | null
          seo_keywords?: Json | null
          status?: string | null
          task_description?: string | null
          task_id?: string | null
          task_title?: string | null
          trend_keywords?: Json | null
          updated_at?: string | null
          video_editor_aspect_ratio?: Json | null
          video_editor_overlays?: Json | null
          video_editor_player_dimensions?: Json | null
          video_presentation_render_params?: Json | null
          video_presentation_script?: string | null
          video_presentation_url?: string | null
          visual_description?: string | null
          visual_description_group?: Json | null
        }
        Update: {
          archived?: boolean | null
          assigned_to?: string | null
          avatar_presenter_id?: string | null
          avatar_script?: string | null
          avatar_video_id?: string | null
          avatar_video_url?: string | null
          avatar_voice_id?: string | null
          ayrshare_post_id?: string | null
          ayrshare_post_status?: string | null
          campaign_id?: string | null
          channel?: string | null
          company_id?: string | null
          content?: string | null
          content_editor_template?: Json | null
          content_template?: string | null
          content_type?: string | null
          created_at?: string | null
          error_generating?: boolean | null
          has_avatar?: boolean | null
          has_image?: boolean | null
          has_video_presentation?: boolean | null
          id?: string
          idea_id?: string | null
          image_path?: string | null
          image_url?: string | null
          image_urls?: Json | null
          is_avatar_ready?: boolean | null
          is_draft?: boolean | null
          is_generating?: boolean | null
          is_long_form?: boolean | null
          is_paused?: boolean | null
          is_posted?: boolean | null
          is_published?: boolean | null
          is_scheduled?: boolean | null
          kanban_order?: number | null
          language?: string | null
          published_by?: string | null
          published_url?: string | null
          schedule_date?: string | null
          scheduled_at?: string | null
          scheduled_publishing_time?: string | null
          seo_keywords?: Json | null
          status?: string | null
          task_description?: string | null
          task_id?: string | null
          task_title?: string | null
          trend_keywords?: Json | null
          updated_at?: string | null
          video_editor_aspect_ratio?: Json | null
          video_editor_overlays?: Json | null
          video_editor_player_dimensions?: Json | null
          video_presentation_render_params?: Json | null
          video_presentation_script?: string | null
          video_presentation_url?: string | null
          visual_description?: string | null
          visual_description_group?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "company_content_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "company_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_content_idea_id_fkey"
            columns: ["idea_id"]
            isOneToOne: false
            referencedRelation: "campaign_ideas"
            referencedColumns: ["id"]
          },
        ]
      }
      company_task_statuses: {
        Row: {
          color: string | null
          company_id: string
          created_at: string | null
          display_name: string
          icon: string | null
          id: string
          name: string
          status_order: number
        }
        Insert: {
          color?: string | null
          company_id: string
          created_at?: string | null
          display_name: string
          icon?: string | null
          id?: string
          name: string
          status_order: number
        }
        Update: {
          color?: string | null
          company_id?: string
          created_at?: string | null
          display_name?: string
          icon?: string | null
          id?: string
          name?: string
          status_order?: number
        }
        Relationships: [
          {
            foreignKeyName: "company_task_statuses_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_task_statuses_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "company_task_statuses_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      config: {
        Row: {
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing: boolean
          enable_team_account_billing: boolean
          enable_team_accounts: boolean
          id: string
        }
        Insert: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
          id?: string
        }
        Update: {
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          enable_account_billing?: boolean
          enable_team_account_billing?: boolean
          enable_team_accounts?: boolean
          id?: string
        }
        Relationships: []
      }
      content_personas: {
        Row: {
          content_id: string
          created_at: string | null
          id: string
          is_primary: boolean | null
          persona_id: string
        }
        Insert: {
          content_id: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id: string
        }
        Update: {
          content_id?: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          persona_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_personas_content_id_fkey"
            columns: ["content_id"]
            isOneToOne: false
            referencedRelation: "company_content"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "content_personas_persona_id_fkey"
            columns: ["persona_id"]
            isOneToOne: false
            referencedRelation: "personas"
            referencedColumns: ["id"]
          },
        ]
      }
      feature_usage: {
        Row: {
          account_id: string
          created_at: string | null
          feature: string
          id: string
          updated_at: string | null
          usage: Json
        }
        Insert: {
          account_id: string
          created_at?: string | null
          feature: string
          id?: string
          updated_at?: string | null
          usage?: Json
        }
        Update: {
          account_id?: string
          created_at?: string | null
          feature?: string
          id?: string
          updated_at?: string | null
          usage?: Json
        }
        Relationships: [
          {
            foreignKeyName: "feature_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "feature_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "feature_usage_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      generated_research: {
        Row: {
          account_id: string
          content_suggestions: Json | null
          created_at: string | null
          created_by: string | null
          icp_id: string
          id: string
          is_generating: boolean | null
          persona_id: string | null
          research_type: string | null
          results: Json | null
          time_filter: string | null
          title: string | null
          topic: string | null
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          account_id: string
          content_suggestions?: Json | null
          created_at?: string | null
          created_by?: string | null
          icp_id: string
          id?: string
          is_generating?: boolean | null
          persona_id?: string | null
          research_type?: string | null
          results?: Json | null
          time_filter?: string | null
          title?: string | null
          topic?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          account_id?: string
          content_suggestions?: Json | null
          created_at?: string | null
          created_by?: string | null
          icp_id?: string
          id?: string
          is_generating?: boolean | null
          persona_id?: string | null
          research_type?: string | null
          results?: Json | null
          time_filter?: string | null
          title?: string | null
          topic?: string | null
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "generated_research_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "generated_research_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "generated_research_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      icps: {
        Row: {
          company_id: string
          created_at: string | null
          data: Json
          error_generating: boolean | null
          id: string
          is_generating: boolean | null
          linkedInUrls: Json | null
          name: string | null
          reference_description: string | null
          reference_material: Json | null
          reference_products: Json | null
          updated_at: string | null
          withAi: boolean | null
          withLinkedIn: boolean | null
        }
        Insert: {
          company_id: string
          created_at?: string | null
          data?: Json
          error_generating?: boolean | null
          id?: string
          is_generating?: boolean | null
          linkedInUrls?: Json | null
          name?: string | null
          reference_description?: string | null
          reference_material?: Json | null
          reference_products?: Json | null
          updated_at?: string | null
          withAi?: boolean | null
          withLinkedIn?: boolean | null
        }
        Update: {
          company_id?: string
          created_at?: string | null
          data?: Json
          error_generating?: boolean | null
          id?: string
          is_generating?: boolean | null
          linkedInUrls?: Json | null
          name?: string | null
          reference_description?: string | null
          reference_material?: Json | null
          reference_products?: Json | null
          updated_at?: string | null
          withAi?: boolean | null
          withLinkedIn?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "icps_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "icps_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "icps_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      image_conversations: {
        Row: {
          company_id: string
          created_at: string
          id: string
          image_path: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          company_id: string
          created_at?: string
          id?: string
          image_path?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          company_id?: string
          created_at?: string
          id?: string
          image_path?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "image_conversations_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "image_conversations_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "image_conversations_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      image_messages: {
        Row: {
          company_id: string
          content: string
          conversation_id: string
          created_at: string
          id: string
          image_path: string | null
          reference_images: Json | null
          role: string
          user_id: string
        }
        Insert: {
          company_id: string
          content: string
          conversation_id: string
          created_at?: string
          id?: string
          image_path?: string | null
          reference_images?: Json | null
          role: string
          user_id: string
        }
        Update: {
          company_id?: string
          content?: string
          conversation_id?: string
          created_at?: string
          id?: string
          image_path?: string | null
          reference_images?: Json | null
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "image_messages_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "image_messages_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "image_messages_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "image_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "image_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
        Insert: {
          account_id: string
          created_at?: string
          email: string
          expires_at?: string
          id?: number
          invite_token: string
          invited_by: string
          role: string
          updated_at?: string
        }
        Update: {
          account_id?: string
          created_at?: string
          email?: string
          expires_at?: string
          id?: number
          invite_token?: string
          invited_by?: string
          role?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      linkedin_uris: {
        Row: {
          created_at: string
          id: string
          profile: Json | null
          uri: string | null
          url: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          profile?: Json | null
          uri?: string | null
          url?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          profile?: Json | null
          uri?: string | null
          url?: string | null
        }
        Relationships: []
      }
      nonces: {
        Row: {
          client_token: string
          created_at: string
          expires_at: string
          id: string
          last_verification_at: string | null
          last_verification_ip: unknown | null
          last_verification_user_agent: string | null
          metadata: Json | null
          nonce: string
          purpose: string
          revoked: boolean
          revoked_reason: string | null
          scopes: string[] | null
          used_at: string | null
          user_id: string | null
          verification_attempts: number
        }
        Insert: {
          client_token: string
          created_at?: string
          expires_at: string
          id?: string
          last_verification_at?: string | null
          last_verification_ip?: unknown | null
          last_verification_user_agent?: string | null
          metadata?: Json | null
          nonce: string
          purpose: string
          revoked?: boolean
          revoked_reason?: string | null
          scopes?: string[] | null
          used_at?: string | null
          user_id?: string | null
          verification_attempts?: number
        }
        Update: {
          client_token?: string
          created_at?: string
          expires_at?: string
          id?: string
          last_verification_at?: string | null
          last_verification_ip?: unknown | null
          last_verification_user_agent?: string | null
          metadata?: Json | null
          nonce?: string
          purpose?: string
          revoked?: boolean
          revoked_reason?: string | null
          scopes?: string[] | null
          used_at?: string | null
          user_id?: string | null
          verification_attempts?: number
        }
        Relationships: []
      }
      notifications: {
        Row: {
          account_id: string
          body: string
          channel: Database["public"]["Enums"]["notification_channel"]
          created_at: string
          dismissed: boolean
          expires_at: string | null
          id: number
          link: string | null
          type: Database["public"]["Enums"]["notification_type"]
        }
        Insert: {
          account_id: string
          body: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Update: {
          account_id?: string
          body?: string
          channel?: Database["public"]["Enums"]["notification_channel"]
          created_at?: string
          dismissed?: boolean
          expires_at?: string | null
          id?: never
          link?: string | null
          type?: Database["public"]["Enums"]["notification_type"]
        }
        Relationships: [
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      onboarding: {
        Row: {
          account_id: string
          completed: boolean | null
          created_at: string | null
          data: Json | null
          id: string
          updated_at: string | null
        }
        Insert: {
          account_id: string
          completed?: boolean | null
          created_at?: string | null
          data?: Json | null
          id?: string
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          completed?: boolean | null
          created_at?: string | null
          data?: Json | null
          id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "onboarding_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "onboarding_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "onboarding_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: true
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      order_items: {
        Row: {
          created_at: string
          id: string
          order_id: string
          price_amount: number | null
          product_id: string
          quantity: number
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          order_id: string
          price_amount?: number | null
          product_id: string
          quantity?: number
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          order_id?: string
          price_amount?: number | null
          product_id?: string
          quantity?: number
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "order_items_order_id_fkey"
            columns: ["order_id"]
            isOneToOne: false
            referencedRelation: "orders"
            referencedColumns: ["id"]
          },
        ]
      }
      orders: {
        Row: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
        Insert: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at?: string
        }
        Update: {
          account_id?: string
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          created_at?: string
          currency?: string
          id?: string
          status?: Database["public"]["Enums"]["payment_status"]
          total_amount?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "orders_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      personas: {
        Row: {
          avatar_url: string | null
          budget_range: string | null
          buying_stage: string | null
          challenges: Json | null
          channels: Json | null
          communication_style: string | null
          company_id: string
          company_size: string | null
          content_count: number | null
          content_formats: Json | null
          content_length: string | null
          created_at: string | null
          data: Json | null
          decision_authority: string | null
          department: string | null
          error_generating: boolean | null
          goals: Json | null
          icp_id: string | null
          id: string
          industries: Json | null
          info_preferences: Json | null
          is_generating: boolean | null
          last_used: string | null
          location: string | null
          management_level: string | null
          name: string | null
          role: string | null
          status: string | null
          tech_stack: Json | null
          topics: Json | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          budget_range?: string | null
          buying_stage?: string | null
          challenges?: Json | null
          channels?: Json | null
          communication_style?: string | null
          company_id: string
          company_size?: string | null
          content_count?: number | null
          content_formats?: Json | null
          content_length?: string | null
          created_at?: string | null
          data?: Json | null
          decision_authority?: string | null
          department?: string | null
          error_generating?: boolean | null
          goals?: Json | null
          icp_id?: string | null
          id?: string
          industries?: Json | null
          info_preferences?: Json | null
          is_generating?: boolean | null
          last_used?: string | null
          location?: string | null
          management_level?: string | null
          name?: string | null
          role?: string | null
          status?: string | null
          tech_stack?: Json | null
          topics?: Json | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          budget_range?: string | null
          buying_stage?: string | null
          challenges?: Json | null
          channels?: Json | null
          communication_style?: string | null
          company_id?: string
          company_size?: string | null
          content_count?: number | null
          content_formats?: Json | null
          content_length?: string | null
          created_at?: string | null
          data?: Json | null
          decision_authority?: string | null
          department?: string | null
          error_generating?: boolean | null
          goals?: Json | null
          icp_id?: string | null
          id?: string
          industries?: Json | null
          info_preferences?: Json | null
          is_generating?: boolean | null
          last_used?: string | null
          location?: string | null
          management_level?: string | null
          name?: string | null
          role?: string | null
          status?: string | null
          tech_stack?: Json | null
          topics?: Json | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "personas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "personas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "personas_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "personas_icp_id_fkey"
            columns: ["icp_id"]
            isOneToOne: false
            referencedRelation: "icps"
            referencedColumns: ["id"]
          },
        ]
      }
      post_engagement_details: {
        Row: {
          company_id: string
          engaged_users: Json | null
          engagement_info: Json | null
          error_generating: boolean | null
          id: string
          is_generating: boolean | null
          platform_post_id: string
          profile_id: string
          user_id: string | null
        }
        Insert: {
          company_id?: string
          engaged_users?: Json | null
          engagement_info?: Json | null
          error_generating?: boolean | null
          id?: string
          is_generating?: boolean | null
          platform_post_id: string
          profile_id: string
          user_id?: string | null
        }
        Update: {
          company_id?: string
          engaged_users?: Json | null
          engagement_info?: Json | null
          error_generating?: boolean | null
          id?: string
          is_generating?: boolean | null
          platform_post_id?: string
          profile_id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "post_engagement_details_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "ayrshare_user_profile"
            referencedColumns: ["id"]
          },
        ]
      }
      post_templates: {
        Row: {
          channel: string | null
          content_type: string | null
          created_at: string
          description: string | null
          id: string
          image_url: string | null
          style: Json | null
          title: string
        }
        Insert: {
          channel?: string | null
          content_type?: string | null
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          style?: Json | null
          title: string
        }
        Update: {
          channel?: string | null
          content_type?: string | null
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string | null
          style?: Json | null
          title?: string
        }
        Relationships: []
      }
      product_documents: {
        Row: {
          company_id: string
          content: string | null
          created_at: string
          file_path: string
          file_type: string
          id: string
          product_id: string | null
          title: string
        }
        Insert: {
          company_id: string
          content?: string | null
          created_at?: string
          file_path: string
          file_type: string
          id?: string
          product_id?: string | null
          title: string
        }
        Update: {
          company_id?: string
          content?: string | null
          created_at?: string
          file_path?: string
          file_type?: string
          id?: string
          product_id?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "product_documents_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          company_id: string
          created_at: string
          custom_fields: Json | null
          description: string | null
          id: string
          key_features: Json | null
          name: string
          target_audience: Json | null
          updated_at: string
        }
        Insert: {
          company_id: string
          created_at?: string
          custom_fields?: Json | null
          description?: string | null
          id?: string
          key_features?: Json | null
          name: string
          target_audience?: Json | null
          updated_at?: string
        }
        Update: {
          company_id?: string
          created_at?: string
          custom_fields?: Json | null
          description?: string | null
          id?: string
          key_features?: Json | null
          name?: string
          target_audience?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "products_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "products_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      role_permissions: {
        Row: {
          id: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Insert: {
          id?: number
          permission: Database["public"]["Enums"]["app_permissions"]
          role: string
        }
        Update: {
          id?: number
          permission?: Database["public"]["Enums"]["app_permissions"]
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
      roles: {
        Row: {
          hierarchy_level: number
          name: string
        }
        Insert: {
          hierarchy_level: number
          name: string
        }
        Update: {
          hierarchy_level?: number
          name?: string
        }
        Relationships: []
      }
      saved_research: {
        Row: {
          account_id: string
          archived: boolean | null
          created_at: string | null
          description: string
          icp_id: string
          id: string
          persona_id: string | null
          relevance_score: number | null
          research_type: string | null
          source: string | null
          source_content: string | null
          source_url: string | null
          time_filter: string | null
          title: string
          topic: string | null
          updated_at: string | null
        }
        Insert: {
          account_id: string
          archived?: boolean | null
          created_at?: string | null
          description: string
          icp_id: string
          id?: string
          persona_id?: string | null
          relevance_score?: number | null
          research_type?: string | null
          source?: string | null
          source_content?: string | null
          source_url?: string | null
          time_filter?: string | null
          title: string
          topic?: string | null
          updated_at?: string | null
        }
        Update: {
          account_id?: string
          archived?: boolean | null
          created_at?: string | null
          description?: string
          icp_id?: string
          id?: string
          persona_id?: string | null
          relevance_score?: number | null
          research_type?: string | null
          source?: string | null
          source_content?: string | null
          source_url?: string | null
          time_filter?: string | null
          title?: string
          topic?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "saved_research_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "saved_research_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "saved_research_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      site_research: {
        Row: {
          agent_mode: boolean | null
          company_id: string
          created_at: string
          enable_web_search: boolean | null
          error_generating: boolean | null
          icps: Json | null
          id: string
          instruction: string | null
          is_generating: boolean | null
          personal: Json | null
          results: Json | null
          schema: Json | null
          urls: Json | null
        }
        Insert: {
          agent_mode?: boolean | null
          company_id: string
          created_at?: string
          enable_web_search?: boolean | null
          error_generating?: boolean | null
          icps?: Json | null
          id?: string
          instruction?: string | null
          is_generating?: boolean | null
          personal?: Json | null
          results?: Json | null
          schema?: Json | null
          urls?: Json | null
        }
        Update: {
          agent_mode?: boolean | null
          company_id?: string
          created_at?: string
          enable_web_search?: boolean | null
          error_generating?: boolean | null
          icps?: Json | null
          id?: string
          instruction?: string | null
          is_generating?: boolean | null
          personal?: Json | null
          results?: Json | null
          schema?: Json | null
          urls?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "site_research_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "site_research_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "site_research_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      socials_research: {
        Row: {
          company_id: string | null
          created_at: string
          error_generating: boolean | null
          id: string
          is_generating: boolean | null
          keywords: Json | null
          platform: string | null
          results: Json | null
        }
        Insert: {
          company_id?: string | null
          created_at?: string
          error_generating?: boolean | null
          id?: string
          is_generating?: boolean | null
          keywords?: Json | null
          platform?: string | null
          results?: Json | null
        }
        Update: {
          company_id?: string | null
          created_at?: string
          error_generating?: boolean | null
          id?: string
          is_generating?: boolean | null
          keywords?: Json | null
          platform?: string | null
          results?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "socials_research_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "socials_research_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "socials_research_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_items: {
        Row: {
          created_at: string
          id: string
          interval: string
          interval_count: number
          price_amount: number | null
          product_id: string
          quantity: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at: string
          variant_id: string
        }
        Insert: {
          created_at?: string
          id: string
          interval: string
          interval_count: number
          price_amount?: number | null
          product_id: string
          quantity?: number
          subscription_id: string
          type: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id: string
        }
        Update: {
          created_at?: string
          id?: string
          interval?: string
          interval_count?: number
          price_amount?: number | null
          product_id?: string
          quantity?: number
          subscription_id?: string
          type?: Database["public"]["Enums"]["subscription_item_type"]
          updated_at?: string
          variant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_items_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
        Insert: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at?: string
          currency: string
          id: string
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Update: {
          account_id?: string
          active?: boolean
          billing_customer_id?: number
          billing_provider?: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end?: boolean
          created_at?: string
          currency?: string
          id?: string
          period_ends_at?: string
          period_starts_at?: string
          status?: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at?: string | null
          trial_starts_at?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_billing_customer_id_fkey"
            columns: ["billing_customer_id"]
            isOneToOne: false
            referencedRelation: "billing_customers"
            referencedColumns: ["id"]
          },
        ]
      }
      twitterState: {
        Row: {
          access_token: string | null
          code_verifier: string | null
          created_at: string
          description: string | null
          expires_in: string | null
          id: number
          name: string | null
          profile_image_url: string | null
          refresh_token: string | null
          refresh_token_expires_in: string | null
          scope: string | null
          screen_name: string | null
          state: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          access_token?: string | null
          code_verifier?: string | null
          created_at?: string
          description?: string | null
          expires_in?: string | null
          id?: number
          name?: string | null
          profile_image_url?: string | null
          refresh_token?: string | null
          refresh_token_expires_in?: string | null
          scope?: string | null
          screen_name?: string | null
          state?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Update: {
          access_token?: string | null
          code_verifier?: string | null
          created_at?: string
          description?: string | null
          expires_in?: string | null
          id?: number
          name?: string | null
          profile_image_url?: string | null
          refresh_token?: string | null
          refresh_token_expires_in?: string | null
          scope?: string | null
          screen_name?: string | null
          state?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_cache: {
        Row: {
          created_at: string
          selected_campaign: string | null
          selected_scene_id: string | null
          selected_video_project: string | null
          task_list_columns: Json | null
          task_sorting_state: Json | null
          user_id: string
        }
        Insert: {
          created_at?: string
          selected_campaign?: string | null
          selected_scene_id?: string | null
          selected_video_project?: string | null
          task_list_columns?: Json | null
          task_sorting_state?: Json | null
          user_id: string
        }
        Update: {
          created_at?: string
          selected_campaign?: string | null
          selected_scene_id?: string | null
          selected_video_project?: string | null
          task_list_columns?: Json | null
          task_sorting_state?: Json | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "cache_selected_campaign_fkey"
            columns: ["selected_campaign"]
            isOneToOne: false
            referencedRelation: "company_campaigns"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cache_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cache_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cache_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_cache_selected_scene_id_fkey"
            columns: ["selected_scene_id"]
            isOneToOne: false
            referencedRelation: "video_project_scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_cache_selected_video_project_fkey"
            columns: ["selected_video_project"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      video_conversations: {
        Row: {
          company_id: string
          created_at: string
          id: string
          title: string | null
          updated_at: string
          user_id: string
          video_project_id: string | null
        }
        Insert: {
          company_id: string
          created_at?: string
          id?: string
          title?: string | null
          updated_at?: string
          user_id: string
          video_project_id?: string | null
        }
        Update: {
          company_id?: string
          created_at?: string
          id?: string
          title?: string | null
          updated_at?: string
          user_id?: string
          video_project_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "video_conversations_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_conversations_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_conversations_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_conversations_video_project_id_fkey"
            columns: ["video_project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      video_generation_jobs: {
        Row: {
          account_id: string
          completed_at: string | null
          created_at: string
          error_message: string | null
          external_operation_id: string | null
          external_operation_name: string | null
          generated_video_url: string | null
          generation_config: Json | null
          id: string
          improved_prompt: string | null
          message_id: string | null
          model: Database["public"]["Enums"]["video_generation_model"]
          overlay_id: string | null
          project_id: string | null
          prompt: string
          scene_id: string | null
          started_at: string | null
          status: Database["public"]["Enums"]["video_generation_status"]
          updated_at: string
          user_id: string
          video_storage_path: string | null
        }
        Insert: {
          account_id: string
          completed_at?: string | null
          created_at?: string
          error_message?: string | null
          external_operation_id?: string | null
          external_operation_name?: string | null
          generated_video_url?: string | null
          generation_config?: Json | null
          id?: string
          improved_prompt?: string | null
          message_id?: string | null
          model?: Database["public"]["Enums"]["video_generation_model"]
          overlay_id?: string | null
          project_id?: string | null
          prompt: string
          scene_id?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["video_generation_status"]
          updated_at?: string
          user_id: string
          video_storage_path?: string | null
        }
        Update: {
          account_id?: string
          completed_at?: string | null
          created_at?: string
          error_message?: string | null
          external_operation_id?: string | null
          external_operation_name?: string | null
          generated_video_url?: string | null
          generation_config?: Json | null
          id?: string
          improved_prompt?: string | null
          message_id?: string | null
          model?: Database["public"]["Enums"]["video_generation_model"]
          overlay_id?: string | null
          project_id?: string | null
          prompt?: string
          scene_id?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["video_generation_status"]
          updated_at?: string
          user_id?: string
          video_storage_path?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_video_generation_jobs_scene_id"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "video_project_scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_generation_jobs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_generation_jobs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_generation_jobs_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_generation_jobs_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "video_messages"
            referencedColumns: ["id"]
          },
        ]
      }
      video_messages: {
        Row: {
          company_id: string
          content: string
          conversation_id: string
          created_at: string
          generated_video_url: string | null
          generation_completed_at: string | null
          generation_error_message: string | null
          generation_started_at: string | null
          id: string
          is_error: boolean | null
          is_generating: boolean | null
          role: string
          user_id: string
          video_generation_id: string | null
          video_generation_model:
            | Database["public"]["Enums"]["video_generation_model"]
            | null
          video_generation_prompt: string | null
          video_generation_status:
            | Database["public"]["Enums"]["video_generation_status"]
            | null
          video_project_id: string | null
        }
        Insert: {
          company_id: string
          content: string
          conversation_id: string
          created_at?: string
          generated_video_url?: string | null
          generation_completed_at?: string | null
          generation_error_message?: string | null
          generation_started_at?: string | null
          id?: string
          is_error?: boolean | null
          is_generating?: boolean | null
          role: string
          user_id: string
          video_generation_id?: string | null
          video_generation_model?:
            | Database["public"]["Enums"]["video_generation_model"]
            | null
          video_generation_prompt?: string | null
          video_generation_status?:
            | Database["public"]["Enums"]["video_generation_status"]
            | null
          video_project_id?: string | null
        }
        Update: {
          company_id?: string
          content?: string
          conversation_id?: string
          created_at?: string
          generated_video_url?: string | null
          generation_completed_at?: string | null
          generation_error_message?: string | null
          generation_started_at?: string | null
          id?: string
          is_error?: boolean | null
          is_generating?: boolean | null
          role?: string
          user_id?: string
          video_generation_id?: string | null
          video_generation_model?:
            | Database["public"]["Enums"]["video_generation_model"]
            | null
          video_generation_prompt?: string | null
          video_generation_status?:
            | Database["public"]["Enums"]["video_generation_status"]
            | null
          video_project_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "video_messages_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_messages_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_messages_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "video_conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_messages_video_project_id_fkey"
            columns: ["video_project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      video_project_assets: {
        Row: {
          account_id: string
          asset_type: string
          created_at: string | null
          duration_seconds: number | null
          external_id: string | null
          external_source: string | null
          file_path: string
          file_size: number | null
          id: string
          is_external: boolean | null
          metadata: Json | null
          mime_type: string | null
          original_filename: string | null
          project_id: string
          thumbnail_path: string | null
        }
        Insert: {
          account_id: string
          asset_type: string
          created_at?: string | null
          duration_seconds?: number | null
          external_id?: string | null
          external_source?: string | null
          file_path: string
          file_size?: number | null
          id?: string
          is_external?: boolean | null
          metadata?: Json | null
          mime_type?: string | null
          original_filename?: string | null
          project_id: string
          thumbnail_path?: string | null
        }
        Update: {
          account_id?: string
          asset_type?: string
          created_at?: string | null
          duration_seconds?: number | null
          external_id?: string | null
          external_source?: string | null
          file_path?: string
          file_size?: number | null
          id?: string
          is_external?: boolean | null
          metadata?: Json | null
          mime_type?: string | null
          original_filename?: string | null
          project_id?: string
          thumbnail_path?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "video_project_assets_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_project_assets_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_project_assets_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_project_assets_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      video_project_autosaves: {
        Row: {
          created_at: string | null
          editor_state: Json
          id: string
          project_id: string
          save_type: string | null
          scene_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          editor_state: Json
          id?: string
          project_id: string
          save_type?: string | null
          scene_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          editor_state?: Json
          id?: string
          project_id?: string
          save_type?: string | null
          scene_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_video_project_autosaves_scene_id"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "video_project_scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_project_autosaves_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      video_project_overlays: {
        Row: {
          animation_config: Json | null
          content: string | null
          created_at: string | null
          duration_frames: number
          from_frame: number
          height: number | null
          id: string
          layer_order: number
          metadata: Json | null
          overlay_id: number
          overlay_type: string
          position_x: number | null
          position_y: number | null
          project_id: string
          rotation: number | null
          row_position: number
          scene_id: string
          src: string | null
          styles: Json | null
          updated_at: string | null
          width: number | null
        }
        Insert: {
          animation_config?: Json | null
          content?: string | null
          created_at?: string | null
          duration_frames?: number
          from_frame?: number
          height?: number | null
          id?: string
          layer_order?: number
          metadata?: Json | null
          overlay_id: number
          overlay_type: string
          position_x?: number | null
          position_y?: number | null
          project_id: string
          rotation?: number | null
          row_position?: number
          scene_id: string
          src?: string | null
          styles?: Json | null
          updated_at?: string | null
          width?: number | null
        }
        Update: {
          animation_config?: Json | null
          content?: string | null
          created_at?: string | null
          duration_frames?: number
          from_frame?: number
          height?: number | null
          id?: string
          layer_order?: number
          metadata?: Json | null
          overlay_id?: number
          overlay_type?: string
          position_x?: number | null
          position_y?: number | null
          project_id?: string
          rotation?: number | null
          row_position?: number
          scene_id?: string
          src?: string | null
          styles?: Json | null
          updated_at?: string | null
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_video_project_overlays_scene_id"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "video_project_scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_project_overlays_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      video_project_scene_selections: {
        Row: {
          id: string
          project_id: string
          selected_scene_id: string
          updated_at: number | null
          user_id: string
        }
        Insert: {
          id: string
          project_id: string
          selected_scene_id: string
          updated_at?: number | null
          user_id: string
        }
        Update: {
          id?: string
          project_id?: string
          selected_scene_id?: string
          updated_at?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "video_project_scene_selections_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_project_scene_selections_selected_scene_id_fkey"
            columns: ["selected_scene_id"]
            isOneToOne: false
            referencedRelation: "video_project_scenes"
            referencedColumns: ["id"]
          },
        ]
      }
      video_project_scenes: {
        Row: {
          aspect_ratio: string | null
          created_at: number | null
          description: string | null
          duration_frames: number | null
          id: string
          name: string
          order_index: number | null
          project_id: string
          updated_at: number | null
          user_id: string
        }
        Insert: {
          aspect_ratio?: string | null
          created_at?: number | null
          description?: string | null
          duration_frames?: number | null
          id: string
          name: string
          order_index?: number | null
          project_id: string
          updated_at?: number | null
          user_id: string
        }
        Update: {
          aspect_ratio?: string | null
          created_at?: number | null
          description?: string | null
          duration_frames?: number | null
          id?: string
          name?: string
          order_index?: number | null
          project_id?: string
          updated_at?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "video_project_scenes_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
      video_projects: {
        Row: {
          account_id: string
          aspect_ratio: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          duration_frames: number | null
          fps: number | null
          height: number | null
          id: string
          is_template: boolean | null
          name: string
          status: string | null
          template_category: string | null
          template_tags: string[] | null
          thumbnail_url: string | null
          updated_at: string | null
          updated_by: string | null
          user_id: string
          width: number | null
        }
        Insert: {
          account_id: string
          aspect_ratio?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          duration_frames?: number | null
          fps?: number | null
          height?: number | null
          id?: string
          is_template?: boolean | null
          name: string
          status?: string | null
          template_category?: string | null
          template_tags?: string[] | null
          thumbnail_url?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id: string
          width?: number | null
        }
        Update: {
          account_id?: string
          aspect_ratio?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          duration_frames?: number | null
          fps?: number | null
          height?: number | null
          id?: string
          is_template?: boolean | null
          name?: string
          status?: string | null
          template_category?: string | null
          template_tags?: string[] | null
          thumbnail_url?: string | null
          updated_at?: string | null
          updated_by?: string | null
          user_id?: string
          width?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "video_projects_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_projects_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_projects_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      video_renders: {
        Row: {
          account_id: string
          completed_at: string | null
          created_at: string | null
          error_message: string | null
          id: string
          output_size_bytes: number | null
          output_url: string | null
          progress: number | null
          project_id: string
          render_id: string | null
          render_settings: Json | null
          started_at: string | null
          status: string | null
          user_id: string
        }
        Insert: {
          account_id: string
          completed_at?: string | null
          created_at?: string | null
          error_message?: string | null
          id?: string
          output_size_bytes?: number | null
          output_url?: string | null
          progress?: number | null
          project_id: string
          render_id?: string | null
          render_settings?: Json | null
          started_at?: string | null
          status?: string | null
          user_id: string
        }
        Update: {
          account_id?: string
          completed_at?: string | null
          created_at?: string | null
          error_message?: string | null
          id?: string
          output_size_bytes?: number | null
          output_url?: string | null
          progress?: number | null
          project_id?: string
          render_id?: string | null
          render_settings?: Json | null
          started_at?: string | null
          status?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "video_renders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_renders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_account_workspace"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_renders_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "user_accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "video_renders_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "video_projects"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      user_account_workspace: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          subscription_status:
            | Database["public"]["Enums"]["subscription_status"]
            | null
        }
        Relationships: []
      }
      user_accounts: {
        Row: {
          id: string | null
          name: string | null
          picture_url: string | null
          role: string | null
          slug: string | null
        }
        Relationships: [
          {
            foreignKeyName: "accounts_memberships_account_role_fkey"
            columns: ["role"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["name"]
          },
        ]
      }
    }
    Functions: {
      accept_invitation: {
        Args: { token: string; user_id: string }
        Returns: string
      }
      add_company_task_status: {
        Args: {
          p_color?: string
          p_company_id: string
          p_display_name: string
          p_icon?: string
          p_name: string
        }
        Returns: string
      }
      add_invitations_to_account: {
        Args: {
          account_slug: string
          invitations: Database["public"]["CompositeTypes"]["invitation"][]
        }
        Returns: Database["public"]["Tables"]["invitations"]["Row"][]
      }
      can_action_account_member: {
        Args: { target_team_account_id: string; target_user_id: string }
        Returns: boolean
      }
      create_invitation: {
        Args: { account_id: string; email: string; role: string }
        Returns: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invite_token: string
          invited_by: string
          role: string
          updated_at: string
        }
      }
      create_nonce: {
        Args: {
          p_expires_in_seconds?: number
          p_metadata?: Json
          p_purpose?: string
          p_revoke_previous?: boolean
          p_scopes?: string[]
          p_user_id?: string
        }
        Returns: Json
      }
      create_team_account: {
        Args:
          | { account_name: string }
          | { account_name: string; public_data: string; website: string }
          | { account_name: string; website: string }
        Returns: {
          created_at: string | null
          created_by: string | null
          email: string | null
          id: string
          is_personal_account: boolean
          name: string
          picture_url: string | null
          primary_owner_user_id: string
          public_data: Json
          slug: string | null
          updated_at: string | null
          updated_by: string | null
          website: string | null
        }
      }
      delete_company_task_status: {
        Args: {
          p_company_id: string
          p_replacement_status?: string
          p_status_id: string
        }
        Returns: boolean
      }
      get_account_invitations: {
        Args: { account_slug: string }
        Returns: {
          account_id: string
          created_at: string
          email: string
          expires_at: string
          id: number
          invited_by: string
          inviter_email: string
          inviter_name: string
          role: string
          updated_at: string
        }[]
      }
      get_account_members: {
        Args: { account_slug: string }
        Returns: {
          account_id: string
          created_at: string
          email: string
          id: string
          name: string
          picture_url: string
          primary_owner_user_id: string
          role: string
          role_hierarchy_level: number
          updated_at: string
          user_id: string
        }[]
      }
      get_active_video_generation_jobs: {
        Args: { target_account_id: string }
        Returns: {
          created_at: string
          external_operation_id: string
          external_operation_name: string
          id: string
          message_id: string
          status: Database["public"]["Enums"]["video_generation_status"]
        }[]
      }
      get_company_task_statuses: {
        Args: { p_company_id: string }
        Returns: {
          color: string
          display_name: string
          icon: string
          id: string
          name: string
          status_order: number
        }[]
      }
      get_config: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_nonce_status: {
        Args: { p_id: string }
        Returns: Json
      }
      get_upper_system_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_user_social_profiles: {
        Args: { target_account_id: string; target_user_id?: string }
        Returns: {
          created_at: string
          id: string
          is_shared: boolean
          messagingActive: boolean
          permissions: Json
          profileKey: string
          refId: string
          title: string
          updated_at: string
        }[]
      }
      has_active_subscription: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      has_more_elevated_role: {
        Args: {
          role_name: string
          target_account_id: string
          target_user_id: string
        }
        Returns: boolean
      }
      has_permission: {
        Args: {
          account_id: string
          permission_name: Database["public"]["Enums"]["app_permissions"]
          user_id: string
        }
        Returns: boolean
      }
      has_role_on_account: {
        Args: { account_id: string; account_role?: string }
        Returns: boolean
      }
      has_same_role_hierarchy_level: {
        Args: {
          role_name: string
          target_account_id: string
          target_user_id: string
        }
        Returns: boolean
      }
      initialize_company_task_statuses: {
        Args: { p_company_id: string }
        Returns: undefined
      }
      is_aal2: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_account_owner: {
        Args: { account_id: string }
        Returns: boolean
      }
      is_account_team_member: {
        Args: { target_account_id: string }
        Returns: boolean
      }
      is_mfa_compliant: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_set: {
        Args: { field_name: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_team_member: {
        Args: { account_id: string; user_id: string }
        Returns: boolean
      }
      is_valid_task_status: {
        Args: { p_company_id: string; p_status: string }
        Returns: boolean
      }
      reorder_company_task_statuses: {
        Args: { p_company_id: string; p_status_ids: string[] }
        Returns: boolean
      }
      revoke_nonce: {
        Args: { p_id: string; p_reason?: string }
        Returns: boolean
      }
      team_account_workspace: {
        Args: { account_slug: string }
        Returns: {
          id: string
          name: string
          permissions: Database["public"]["Enums"]["app_permissions"][]
          picture_url: string
          primary_owner_user_id: string
          role: string
          role_hierarchy_level: number
          slug: string
          subscription_status: Database["public"]["Enums"]["subscription_status"]
        }[]
      }
      toggle_social_profile_sharing: {
        Args: { profile_id: string; share_with_team?: boolean }
        Returns: boolean
      }
      transfer_team_account_ownership: {
        Args: { new_owner_id: string; target_account_id: string }
        Returns: undefined
      }
      update_company_task_status: {
        Args: {
          p_color?: string
          p_display_name?: string
          p_icon?: string
          p_status_id: string
          p_status_order?: number
        }
        Returns: boolean
      }
      upsert_order: {
        Args: {
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          currency: string
          line_items: Json
          status: Database["public"]["Enums"]["payment_status"]
          target_account_id: string
          target_customer_id: string
          target_order_id: string
          total_amount: number
        }
        Returns: {
          account_id: string
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          created_at: string
          currency: string
          id: string
          status: Database["public"]["Enums"]["payment_status"]
          total_amount: number
          updated_at: string
        }
      }
      upsert_subscription: {
        Args: {
          active: boolean
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          currency: string
          line_items: Json
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          target_account_id: string
          target_customer_id: string
          target_subscription_id: string
          trial_ends_at?: string
          trial_starts_at?: string
        }
        Returns: {
          account_id: string
          active: boolean
          billing_customer_id: number
          billing_provider: Database["public"]["Enums"]["billing_provider"]
          cancel_at_period_end: boolean
          created_at: string
          currency: string
          id: string
          period_ends_at: string
          period_starts_at: string
          status: Database["public"]["Enums"]["subscription_status"]
          trial_ends_at: string | null
          trial_starts_at: string | null
          updated_at: string
        }
      }
      verify_nonce: {
        Args: {
          p_ip?: unknown
          p_max_verification_attempts?: number
          p_purpose: string
          p_required_scopes?: string[]
          p_token: string
          p_user_agent?: string
          p_user_id?: string
        }
        Returns: Json
      }
    }
    Enums: {
      app_permissions:
        | "roles.manage"
        | "billing.manage"
        | "settings.manage"
        | "members.manage"
        | "invites.manage"
      billing_provider: "stripe" | "lemon-squeezy" | "paddle"
      campaign_status:
        | "Draft"
        | "Ready"
        | "In Progress"
        | "Completed"
        | "Archived"
      lifecycle_stage: "Startup" | "Scale-up" | "Mature Enterprise"
      notification_channel: "in_app" | "email"
      notification_type: "info" | "warning" | "error"
      payment_status: "pending" | "succeeded" | "failed"
      subscription_item_type: "flat" | "per_seat" | "metered"
      subscription_status:
        | "active"
        | "trialing"
        | "past_due"
        | "canceled"
        | "unpaid"
        | "incomplete"
        | "incomplete_expired"
        | "paused"
      video_generation_model: "veo-3-fast" | "veo-3-standard"
      video_generation_status:
        | "pending"
        | "processing"
        | "completed"
        | "failed"
        | "cancelled"
    }
    CompositeTypes: {
      invitation: {
        email: string | null
        role: string | null
      }
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          type: Database["storage"]["Enums"]["buckettype"]
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          type?: Database["storage"]["Enums"]["buckettype"]
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          type?: Database["storage"]["Enums"]["buckettype"]
          updated_at?: string | null
        }
        Relationships: []
      }
      buckets_analytics: {
        Row: {
          created_at: string
          format: string
          id: string
          type: Database["storage"]["Enums"]["buckettype"]
          updated_at: string
        }
        Insert: {
          created_at?: string
          format?: string
          id: string
          type?: Database["storage"]["Enums"]["buckettype"]
          updated_at?: string
        }
        Update: {
          created_at?: string
          format?: string
          id?: string
          type?: Database["storage"]["Enums"]["buckettype"]
          updated_at?: string
        }
        Relationships: []
      }
      iceberg_namespaces: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          name: string
          updated_at: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id?: string
          name: string
          updated_at?: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          name?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "iceberg_namespaces_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets_analytics"
            referencedColumns: ["id"]
          },
        ]
      }
      iceberg_tables: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          location: string
          name: string
          namespace_id: string
          updated_at: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id?: string
          location: string
          name: string
          namespace_id: string
          updated_at?: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          location?: string
          name?: string
          namespace_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "iceberg_tables_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets_analytics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "iceberg_tables_namespace_id_fkey"
            columns: ["namespace_id"]
            isOneToOne: false
            referencedRelation: "iceberg_namespaces"
            referencedColumns: ["id"]
          },
        ]
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          level: number | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          level?: number | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      prefixes: {
        Row: {
          bucket_id: string
          created_at: string | null
          level: number
          name: string
          updated_at: string | null
        }
        Insert: {
          bucket_id: string
          created_at?: string | null
          level?: number
          name: string
          updated_at?: string | null
        }
        Update: {
          bucket_id?: string
          created_at?: string | null
          level?: number
          name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "prefixes_bucketId_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            isOneToOne: false
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            isOneToOne: false
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_prefixes: {
        Args: { _bucket_id: string; _name: string }
        Returns: undefined
      }
      can_insert_object: {
        Args: { bucketid: string; metadata: Json; name: string; owner: string }
        Returns: undefined
      }
      delete_prefix: {
        Args: { _bucket_id: string; _name: string }
        Returns: boolean
      }
      extension: {
        Args: { name: string }
        Returns: string
      }
      filename: {
        Args: { name: string }
        Returns: string
      }
      foldername: {
        Args: { name: string }
        Returns: string[]
      }
      get_level: {
        Args: { name: string }
        Returns: number
      }
      get_prefix: {
        Args: { name: string }
        Returns: string
      }
      get_prefixes: {
        Args: { name: string }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          bucket_id: string
          size: number
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
          prefix_param: string
        }
        Returns: {
          created_at: string
          id: string
          key: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          delimiter_param: string
          max_keys?: number
          next_token?: string
          prefix_param: string
          start_after?: string
        }
        Returns: {
          id: string
          metadata: Json
          name: string
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          bucketname: string
          levels?: number
          limits?: number
          offsets?: number
          prefix: string
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          created_at: string
          id: string
          last_accessed_at: string
          metadata: Json
          name: string
          updated_at: string
        }[]
      }
      search_legacy_v1: {
        Args: {
          bucketname: string
          levels?: number
          limits?: number
          offsets?: number
          prefix: string
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          created_at: string
          id: string
          last_accessed_at: string
          metadata: Json
          name: string
          updated_at: string
        }[]
      }
      search_v1_optimised: {
        Args: {
          bucketname: string
          levels?: number
          limits?: number
          offsets?: number
          prefix: string
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          created_at: string
          id: string
          last_accessed_at: string
          metadata: Json
          name: string
          updated_at: string
        }[]
      }
      search_v2: {
        Args: {
          bucket_name: string
          levels?: number
          limits?: number
          prefix: string
          start_after?: string
        }
        Returns: {
          created_at: string
          id: string
          key: string
          metadata: Json
          name: string
          updated_at: string
        }[]
      }
    }
    Enums: {
      buckettype: "STANDARD" | "ANALYTICS"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      app_permissions: [
        "roles.manage",
        "billing.manage",
        "settings.manage",
        "members.manage",
        "invites.manage",
      ],
      billing_provider: ["stripe", "lemon-squeezy", "paddle"],
      campaign_status: [
        "Draft",
        "Ready",
        "In Progress",
        "Completed",
        "Archived",
      ],
      lifecycle_stage: ["Startup", "Scale-up", "Mature Enterprise"],
      notification_channel: ["in_app", "email"],
      notification_type: ["info", "warning", "error"],
      payment_status: ["pending", "succeeded", "failed"],
      subscription_item_type: ["flat", "per_seat", "metered"],
      subscription_status: [
        "active",
        "trialing",
        "past_due",
        "canceled",
        "unpaid",
        "incomplete",
        "incomplete_expired",
        "paused",
      ],
      video_generation_model: ["veo-3-fast", "veo-3-standard"],
      video_generation_status: [
        "pending",
        "processing",
        "completed",
        "failed",
        "cancelled",
      ],
    },
  },
  storage: {
    Enums: {
      buckettype: ["STANDARD", "ANALYTICS"],
    },
  },
} as const

