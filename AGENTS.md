# AI Agent Development Guide

> **Comprehensive guide for AI agents working with this content marketing SaaS monorepo**

## 🏗️ Architecture Overview

This is a **real-time, multi-tenant SaaS platform** for AI-powered content marketing. The system uses **Zero sync** for real-time collaboration and **Supabase** for persistent storage, auth, and RLS.

### Core Applications
- **`apps/web/`** - Next.js 15 frontend (main SaaS platform)
- **`apps/sb-server/`** - Express.js backend (AI services & data processing) 
- **`apps/zero-server/`** - Zero sync real-time server
- **`apps/e2e/`** - Playwright testing suite

### Key Packages
- **`packages/zero-schema/`** - Shared database schema & mutations
- **`packages/ui/`** - Reusable React components (shadcn/ui)
- **`packages/supabase/`** - Database client & auth utilities

## 🔌 Runtime Topology & Data Flow

### High-level Topology
- **Next.js Web (`apps/web`)**: UI, Server Components for data fetching, Client Components for interactivity. Uses Supabase client on server; React Query on client.
- **Zero Server (`apps/zero-server`)**: Real-time sync backbone. The web app uses Zero queries/mutations; server-side work is funneled via a `/push` mutation endpoint on sb-server.
- **SB Server (`apps/sb-server`)**: Express API for AI workflows, scraping, content/image/video generation, and Zero background processing. It also runs the Zero PushProcessor for server-driven mutations.
- **Supabase (Postgres + Storage)**: Persistent store with RLS; storage buckets (e.g., `generated`) for assets.

### Data Flow Patterns
- **Real-time CRUD**: Client calls `z.query.*` to subscribe and `z.mutate.*` to optimistically update. Server-side durable effects are scheduled via `/push` which executes server mutators and then background async tasks.
- **AI Workflows**: Web calls sb-server endpoints (e.g., `/generate-images`, conversation chat endpoints). sb-server orchestrates providers (OpenRouter/Gemini, Vertex AI), stores outputs in Supabase Storage, and returns stable URLs.
- **Scraping/ETL**: Web triggers sb-server scraping endpoints; results are returned immediately or polled via status endpoints (e.g., `extract-status`).

### Tenancy & Permissions
- All app data is scoped by `account_id` (company/team or personal account). RLS in Supabase enforces access. Client-side queries must always include tenant scope.

---

## 🗄️ Database Changes Workflow

### 1. **Schema Updates**

When modifying database structure, you must update **3 places**:

#### A. Supabase Migration
```bash
# Create new migration
cd apps/web
pnpm run supabase:migration:new "add_new_field_to_campaigns"
```

Edit the generated SQL file in `apps/web/supabase/migrations/`:
```sql
-- Add new column
ALTER TABLE company_campaigns 
ADD COLUMN new_field TEXT;

-- Update RLS policy if needed
CREATE POLICY "Users can view campaigns" ON company_campaigns
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM accounts_memberships 
    WHERE account_id = company_campaigns.company_id 
    AND user_id = auth.uid()
  )
);
```

#### B. Zero Schema Definition
Update `packages/zero-schema/src/schema.ts`:
```typescript
export const company_campaigns = table('company_campaigns').columns({
  // existing fields...
  new_field: string().optional(), // Add new field
}).primaryKey('id');

// Update permissions if needed
company_campaigns: {
  row: {
    select: [allowIfCompanyCampaignIsCompany],
    // add insert/update permissions as needed
  },
},
```

#### C. TypeScript Types (Auto-generated)
```bash
# Regenerate types after migration
cd apps/web
pnpm run supabase:typegen
```

### 2. **Deploy Database Changes**
```bash
# Push to preview environment
git add . && git commit -m "feat: add new_field to campaigns"
git push origin feature-branch

# GitHub Actions will automatically:
# 1. Run Supabase migrations via deploy-supabase.yml
# 2. Deploy updated zero-schema via deploy-zero-server.yml
```

---

## 🚀 Backend Development (AI Services)

### Where to Add New AI Services

All LLM/AI services go in **`apps/sb-server/src/lib/services/`**:

```
apps/sb-server/src/lib/services/
├── generate-content.ts      # Content generation
├── generate-persona.ts      # ICP/persona creation
├── generate-schedule.ts     # Campaign planning  
├── scrape-website.ts        # Web scraping
├── your-new-service.ts      # 👈 Add new services here
```

### Service Pattern
```typescript
// apps/sb-server/src/lib/services/your-new-service.ts
import { openai } from '../clients/openai.js';

interface ServiceInput {
  companyId: string;
  prompt: string;
  // other inputs
}

interface ServiceOutput {
  result: string;
  metadata?: any;
}

export async function yourNewService(input: ServiceInput): Promise<ServiceOutput> {
  try {
    // AI processing logic
    const response = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [{ role: 'user', content: input.prompt }],
    });

    return {
      result: response.choices[0]?.message?.content || '',
      metadata: { tokens: response.usage?.total_tokens },
    };
  } catch (error) {
    console.error('Service error:', error);
    throw new Error('Failed to process request');
  }
}
```

### Adding API Endpoints
Add to **`apps/sb-server/src/index.ts`**:
```typescript
import { yourNewService } from './lib/services/your-new-service.js';

app.post('/your-endpoint', async (req: Request, res: Response) => {
  try {
    const { companyId, prompt } = req.body;
    
    if (!companyId || !prompt) {
      res.status(400).json({ error: 'Missing required fields' });
      return;
    }

    const result = await yourNewService({ companyId, prompt });
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Service temporarily unavailable',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});
```

### Server Mutators (Database Operations)

For operations that trigger background work, use server mutators in **`apps/sb-server/src/lib/server-mutators.ts`**:

```typescript
export function createServerMutators(authData: AuthData, asyncTasks: Array<() => Promise<void>>) {
  return {
    // existing mutators...
    your_table: {
      insert: async (tx: any, { id, values }: { id: string; values: any }) => {
        // Insert record immediately
        await tx.mutate.your_table.insert({
          id,
          ...values,
          is_generating: true, // Show loading state
        });

        // Queue background work
        asyncTasks.push(async () => {
          try {
            const result = await yourNewService(values);
            
            // Update with results
            await sql`
              UPDATE your_table 
              SET 
                result = ${sql.json(result)},
                is_generating = false,
                updated_at = ${Date.now()}
              WHERE id = ${id}
            `;
          } catch (error) {
            // Mark as failed
            await sql`
              UPDATE your_table 
              SET 
                is_generating = false,
                error_generating = true
              WHERE id = ${id}
            `;
          }
        });
      },
    },
  };
}
```

---

## 📡 SB Server API Reference (apps/sb-server)

Base: `http://<sb-server-host>:<port>` (PORT defaults to 8080)

### Content & Planning
- `POST /generate-schedule`
  - Body: `{ productDocumentation, campaignGoal, startDate, endDate, externalResearch? }`
  - 200: `ContentItem[]`
- `POST /generate-persona`
  - Body: `{ icp_data: string }`
  - 200: `{ ...persona }`
- `POST /generate-campaign-name`
  - Body: `{ campaignInformation: string }`
  - 200: `{ success: true, campaignName, timestamp }`

### Content Creation
- `POST /generate-task-content`
  - Body: `{ taskTitle, taskDescription, contentType, channel, campaignGoal, productInformation?, targetICPs?, targetPersonas?, companyBrand?, externalResearch? }`
  - 200: `{ success: true, data, timestamp }`
- `POST /generate-images`
  - Body: `{ image_prompt: string, aspect_ratio?: string, company_id: string, image_gen_styles?: object, brand_context?: string }`
  - 200: `{ success: true, url, path, timestamp }` where `url` is publicly accessible (Supabase in prod, ImgBB in dev)

### Conversation Agents
- `POST /image-conversation-chat`
  - Body: `{ messages: Array<{ role: 'user'|'assistant', content: string }>, userId: string, companyId: string, conversationId: string }`
  - 200: Agent response payload; 400 on validation errors
- `POST /video-conversation-chat`
  - Body: `{ messages: Array<{ role, content }>, userId, companyId, conversationId, messageId? }`
  - 200: Agent response payload
- `POST /image-editing-chat`
  - Body: similar to image conversation, tailored for editing flows

### Web & Social Scraping
- `GET /scrape?url=...` → Scrape a single page
- `POST /scrape-by-element` → Body: `{ url, element_prompts: string[], json_output?: object, instructions?: string }`
- `POST /extract-from-website` → Body: `{ urls: string[], prompt: string, schema: Array<SchemaItem>, enableWebSearch?: boolean }`
- `GET /extract-status?extractId=...` → Poll extraction status
- `GET /crawl?url=...&limit=...` and `GET /crawl-status?crawlId=...`
- Social:
  - `POST /scrape/reddit`
  - `POST /scrape/tiktok`
  - `POST /scrape/twitter`
  - All accept provider-specific filter params; 200: `{ success, data, timestamp }`

### Zero Sync
- `POST /push`
  - Accepts Zero mutation payload. Internally runs `PushProcessor` with `createServerMutators(...)`, commits DB changes, then executes queued async tasks.

Notes
- All endpoints validate required fields and return structured error responses on failure. Add auth as needed (see Security section).

---

## 📑 Core Request/Response Contracts

### Generate Images
Request
```json
{
  "image_prompt": "realistic portrait of a runner at sunrise",
  "aspect_ratio": "16:9",
  "company_id": "<uuid>",
  "image_gen_styles": { "image_styles": "photorealistic" },
  "brand_context": "premium athletic apparel"
}
```
Response
```json
{
  "success": true,
  "url": "https://.../public/generated/<company_id>/generated/generated-...png",
  "path": "<company_id>/generated/generated-...png",
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

### Image Conversation Chat
Request
```json
{
  "messages": [
    { "role": "user", "content": "Create a hero image in our brand style." }
  ],
  "userId": "<uuid>",
  "companyId": "<uuid>",
  "conversationId": "<uuid>"
}
```
Response
```json
{ "success": true, "data": { /* agent response */ } }
```

### Video Conversation Chat
Request
```json
{
  "messages": [
    { "role": "user", "content": "Plan a 10s product clip in 16:9" }
  ],
  "userId": "<uuid>",
  "companyId": "<uuid>",
  "conversationId": "<uuid>",
  "messageId": "<uuid>"
}
```
Response
```json
{ "success": true, "data": { /* agent response */ } }
```

---

## 🎨 Frontend Development (UI Components)

### Component Structure
```
apps/web/app/home/<USER>/
├── campaigns/
│   ├── _components/          # Feature-specific components
│   │   ├── campaign-form.tsx
│   │   ├── campaign-list.tsx
│   │   └── index.ts         # Export barrel
│   ├── _lib/                # Feature utilities
│   │   ├── actions.ts       # Server actions
│   │   ├── queries.ts       # Data queries
│   │   └── validations.ts   # Zod schemas
│   └── page.tsx             # Route page
```

### Shared UI Components
Add reusable components to **`packages/ui/src/`**:
```typescript
// packages/ui/src/your-component.tsx
import * as React from 'react';
import { cn } from './lib/utils';

interface YourComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export const YourComponent = React.forwardRef<
  HTMLDivElement,
  YourComponentProps
>(({ className, children, ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={cn("base-styles", className)}
      {...props}
    >
      {children}
    </div>
  );
});
YourComponent.displayName = "YourComponent";
```

Export in **`packages/ui/src/index.ts`**:
```typescript
export { YourComponent } from './your-component';
```

### Real-Time Data Patterns
Use Zero sync for real-time updates:
```typescript
'use client';

import { useQuery, useMutation } from '@kit/zero/react';
import { useZero } from '@kit/zero/zero-provider';

export function CampaignList({ companyId }: { companyId: string }) {
  const z = useZero();
  
  // Real-time query
  const [campaigns] = useQuery(
    z.query.company_campaigns
      .where('company_id', companyId)
      .orderBy('created_at', 'desc')
  );

  // Optimistic mutation
  const createCampaign = useMutation(z.mutate.company_campaigns.insert);

  const handleCreate = async (data: CampaignData) => {
    // Optimistic update (immediate UI response)
    await createCampaign({
      id: generateId(),
      company_id: companyId,
      ...data,
      is_generating: true,
    });
  };

  return (
    <div>
      {campaigns.map(campaign => (
        <CampaignCard 
          key={campaign.id} 
          campaign={campaign}
          isGenerating={campaign.is_generating}
        />
      ))}
    </div>
  );
}
```

### Server Actions (Data Mutations)
```typescript
// apps/web/app/home/<USER>/campaigns/_lib/actions.ts
'use server';

import { requireUserInServerComponent } from '@kit/supabase/require-user-in-server-component';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function createCampaign(data: CreateCampaignData) {
  const user = await requireUserInServerComponent();
  const client = getSupabaseServerClient();

  // Validate permissions
  const { data: membership } = await client
    .from('accounts_memberships')
    .select('account_role')
    .eq('user_id', user.id)
    .eq('account_id', data.companyId)
    .single();

  if (!membership) {
    throw new Error('Unauthorized');
  }

  // Perform server-side operation
  const { data: campaign, error } = await client
    .from('company_campaigns')
    .insert(data)
    .select()
    .single();

  if (error) throw error;
  return campaign;
}
```

---

## 🔄 Zero Sync & Server Mutators

- Client uses `@kit/zero` to subscribe (`z.query.*`) and optimistically mutate (`z.mutate.*`).
- sb-server exposes `/push` to process server-side mutations. It wraps mutations with `createServerMutators(authData, asyncTasks)` and executes queued async tasks after the transaction commits.
- Use this pattern for long-running AI jobs: write placeholder rows with `is_generating: true`, then update results asynchronously.

---

## 📋 Development Checklist

### ✅ Database Changes
- [ ] Create Supabase migration
- [ ] Update Zero schema definition
- [ ] Update permissions in Zero schema
- [ ] Regenerate TypeScript types
- [ ] Test migration locally
- [ ] Deploy via GitHub Actions

### ✅ Backend Development
- [ ] Add service to `apps/sb-server/src/lib/services/`
- [ ] Add API endpoint to `apps/sb-server/src/index.ts`
- [ ] Add server mutator if needed
- [ ] Add async task handler if needed
- [ ] Test API endpoints locally
- [ ] Deploy via GitHub Actions

### ✅ Frontend Development
- [ ] Create feature components in `_components/`
- [ ] Add server actions in `_lib/actions.ts`
- [ ] Add data queries in `_lib/queries.ts`
- [ ] Add form validations in `_lib/validations.ts`
- [ ] Use Zero sync for real-time updates
- [ ] Add loading and error states
- [ ] Test user flows

---

## 🚨 Important Patterns & Conventions

### 1. **Always Use Optimistic Updates**
```typescript
// ✅ Good - Immediate UI feedback
const createItem = useMutation(z.mutate.items.insert);
await createItem({ id: generateId(), ...data });

// ❌ Bad - Slow server round-trip
await fetch('/api/items', { method: 'POST', body: JSON.stringify(data) });
```

### 2. **Multi-Tenant Data Isolation**
```typescript
// ✅ Always scope by company_id
const campaigns = z.query.company_campaigns
  .where('company_id', companyId);

// ❌ Never query without tenant scope
const campaigns = z.query.company_campaigns.related.all;
```

### 3. **Async Work Pattern**
```typescript
// ✅ Good - Immediate insert + background processing
await tx.mutate.items.insert({ ...data, is_generating: true });
asyncTasks.push(() => processItem(data));

// ❌ Bad - Slow synchronous processing
const result = await longRunningAIProcess(data);
await tx.mutate.items.insert({ ...data, result });
```

### 4. **Error Handling**
```typescript
// ✅ Always handle loading and error states
{isGenerating && <LoadingSpinner />}
{error && <ErrorMessage message={error.message} />}
{data && <Content data={data} />}
```

### 5. **Permission Checks**
```typescript
// ✅ Always verify user permissions
const user = await requireUserInServerComponent();
const membership = await verifyMembership(user.id, companyId);
if (!membership) throw new Error('Unauthorized');
```

---

## 🔧 Development Commands

### Database
```bash
# Local development
pnpm run supabase:web:start        # Start local Supabase
pnpm run supabase:web:reset        # Reset local database
pnpm run supabase:typegen          # Generate TypeScript types

# Migrations
pnpm run supabase:migration:new "description"  # Create migration
pnpm run supabase:db:push          # Push to remote
```

### Development
```bash
# Start all services
pnpm run dev                       # Web app + Zero server
pnpm run dev:sb-server            # Backend API server
pnpm run dev:zero-server          # Real-time sync server

# Testing  
pnpm run test                      # Run all tests
pnpm run test:e2e                  # E2E tests only
pnpm run typecheck                 # Type checking
pnpm run lint                      # Linting
```

### Deployment
```bash
# Automatic via GitHub Actions
git push origin main               # Production deploy
git push origin staging            # Staging deploy  
git push origin feature-branch     # Preview deploy

# Manual deployment (emergency)
pnpm run deploy:preview            # Manual preview deploy
```

---

## 🧠 AI/LLM Best Practices

### 1. **Provider Flexibility**
```typescript
// ✅ Support multiple AI providers
const providers = {
  openai: new OpenAI({ apiKey: process.env.OPENAI_API_KEY }),
  anthropic: new Anthropic({ apiKey: process.env.CLAUDE_API_KEY }),
  google: new GoogleAI({ apiKey: process.env.GOOGLE_AI_KEY }),
};

const provider = providers[preferredProvider] || providers.openai;
```

### 2. **Prompt Engineering**
```typescript
// ✅ Use structured prompts with context
const systemPrompt = `
You are a content marketing expert. Generate social media content based on:
- Brand Voice: ${brandVoice}
- Target Audience: ${targetAudience}  
- Campaign Goal: ${campaignGoal}

Format: JSON with title, content, hashtags, call_to_action
`;
```

### 3. **Error Recovery**
```typescript
// ✅ Graceful fallbacks
try {
  return await primaryAIProvider.generate(prompt);
} catch (error) {
  console.warn('Primary AI failed, trying fallback:', error);
  return await fallbackAIProvider.generate(prompt);
}
```

### 4. **Content Validation**
```typescript
// ✅ Validate AI output
const contentSchema = z.object({
  title: z.string().min(1).max(100),
  content: z.string().min(1).max(2000),
  hashtags: z.array(z.string()).max(10),
});

const validatedContent = contentSchema.parse(aiResponse);
```

---

## 🖼️ Image Studio Flows

### Reference Image Uploads
- Client component `AIChatInput` supports attaching reference images by posting to `/api/upload-image` (web app route) with `companyId`. The agent endpoints will then detect URLs inside chat messages.
- Image conversation/editing endpoints validate `{ messages[], userId, companyId, conversationId }` and ensure the last message is from the user.

### Generation
- `POST /generate-images` returns `{ url, path }` of the generated asset. In prod, URLs are from Supabase Storage; in dev, from ImgBB.

### URL Detection
- Endpoints normalize/detect image URLs in messages (supports `imgbb.com` and Supabase `generated` bucket URLs).

---

## ⚙️ Environment Variables

### apps/sb-server
- `PORT` (default 8080)
- `BASE_URL`
- `ZERO_UPSTREAM_DB` (Postgres connection for Zero)
- `OPENROUTER_API_KEY`, `OPENROUTER_REFERER`
- `LANGFUSE_SECRET_KEY`, `LANGFUSE_PUBLIC_KEY`, `LANGFUSE_BASE_URL`
- `NEXT_PUBLIC_SUPABASE_URL` (only used server-side here for Storage)
- `SUPABASE_SERVICE_ROLE_KEY` (server-only)
- `IMGBB_API_KEY` (dev image hosting)
- Google/Vertex auth variables as required by `generate-video.ts`

### apps/zero-server
- Typically configured via Fly or environment; consumes `@kit/zero-schema` and connects to the same Postgres for replication.

### apps/web
- Supabase public URL and anon keys for client; follows Makerkit patterns for server (`getSupabaseServerClient`) and client (`useSupabase`).

Store secrets in deployment provider (Fly/GCP/Vercel). Never commit to source control.

---

## 🧪 Testing & Deployment Guidance

- **E2E**: Use explicit waits for server-side work to complete and data to persist before assertions (poll status endpoints or DB-backed signals).
- **Local**: Start services with `pnpm run dev`, or run sb-server/zero-server separately. Use `pnpm run supabase:web:start` for local DB.
- **CI/CD**: GitHub Actions deploy Supabase migrations and zero schema on push; sb-server deployed per environment YAML.

---

## 🔐 Security Considerations for Agents

- **Do not expose secrets to the client**: `SUPABASE_SERVICE_ROLE_KEY`, `OPENROUTER_API_KEY`, `LANGFUSE_*` must remain server-only.
- **Authentication**: sb-server endpoints currently do not enforce JWT verification by default. If exposing publicly, add auth middleware (e.g., Supabase JWT verification, HMAC signature, or API key) and scope requests by `companyId`.
- **RLS First**: Persist generated data only through Supabase with RLS-enabled tables; avoid bypassing RLS except with vetted Admin operations.
- **Network**: Prefer private networking between web and sb-server. If public, implement rate limiting, input validation, and request signing.

---

## 🤖 AI Providers & Observability

### Providers
- **OpenRouter (Chat & Image)**: Used via OpenAI-compatible client for chat completions and image generation.
  - Models: `google/gemini-2.5-pro` (chat), `google/gemini-2.5-flash-image-preview` (images; requires `modalities: ["image","text"]`).
- **Google Vertex AI (Video)**: Veo 3 models for video generation in `generate-video.ts`.

### Observability
- **Langfuse**: Instrumentation via `observeOpenAI` and prompts via Langfuse Prompt Management (e.g., `generate_image_v1`).

### Storage Strategy
- **Production**: Upload images to Supabase Storage bucket `generated` under `<company_id>/generated/<file>.png` and return public URL.
- **Development**: Upload to ImgBB for public URL; optionally mirror to Supabase for asset library.

---

## 📚 Additional Resources

- **Zero Sync Docs**: Understanding real-time synchronization
- **Supabase Docs**: Database, auth, and RLS patterns
- **Next.js 15 Docs**: App Router and Server Components
- **shadcn/ui**: Component library patterns
- **Cursor Rules**: `.cursorrules` file for AI-assisted development

---

**Remember**: This is a real-time, collaborative platform. Every change should consider multi-user scenarios, data consistency, and user experience. Always test with multiple browser tabs to verify real-time behavior works correctly.