# Google App Engine configuration for sb-server with FFmpeg support
runtime: nodejs18

# Automatic scaling configuration
automatic_scaling:
  min_instances: 0
  max_instances: 10
  target_cpu_utilization: 0.6
  target_throughput_utilization: 0.6

# Resources for video processing
resources:
  cpu: 2
  memory_gb: 4
  disk_size_gb: 10

# Environment variables
env_variables:
  NODE_ENV: production
  # Add your environment variables here:
  # NEXT_PUBLIC_SUPABASE_URL: "your_supabase_url"
  # SUPABASE_SERVICE_ROLE_KEY: "your_service_role_key"
  # OPENAI_API_KEY: "your_openai_api_key"

# Install FFmpeg for video-to-audio processing
# Note: This requires a custom runtime or using a container approach
# For App Engine Standard, you may need to use App Engine Flexible
# or deploy as a container with FFmpeg pre-installed

# For App Engine Flexible with custom runtime:
# runtime: custom
# env: flex
# 
# And include a Dockerfile:
# FROM node:18
# RUN apt-get update && apt-get install -y ffmpeg
# WORKDIR /app
# COPY package*.json ./
# RUN npm ci --only=production
# COPY . .
# EXPOSE 8080
# CMD ["npm", "start"]
