# Video Transcript Extraction Feature

This feature automatically extracts transcripts from uploaded video files in the asset library using Google Cloud Video Intelligence API.

## Google Cloud Architecture

This implementation uses **Google Cloud Video Intelligence API** - the perfect solution for Google App Engine deployments with no additional dependencies required!

## How It Works

1. **Downloads video from Supabase Storage** - Loads video into memory
2. **Uploads to Google Cloud Storage temporarily** - Video Intelligence API requires GCS input
3. **Processes with Video Intelligence API** - Extracts transcript with speaker diarization and word timestamps
4. **Creates comprehensive transcript** - Includes full transcript, confidence scores, and detailed word-level data
5. **Uploads transcript to Supabase** - Stores transcript metadata as JSON file
6. **Cleans up temporary files** - Removes temporary GCS file

## Dependencies

### Primary: Google Cloud Video Intelligence API
- **Video Intelligence API**: For direct video-to-transcript conversion
- **Google Cloud Storage**: For temporary video file storage (required by API)
- **No FFmpeg needed** - Google handles all video processing
- **Perfect for App Engine** - uses existing Google Cloud infrastructure

### Node.js Dependencies
The following packages are included in package.json:
- `@google-cloud/video-intelligence` - Video Intelligence API client
- `@google-cloud/storage` - Google Cloud Storage client
- `uuid` - for generating unique file names

## Environment Variables

Make sure these environment variables are set in your sb-server deployment:

### Required:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
GCS_TEMP_BUCKET=your_temp_processing_bucket
```

### Google Cloud Setup:
- **Video Intelligence API** must be enabled in your GCP project
- **Service account** must have Video Intelligence and Storage permissions
- **GCS bucket** for temporary video processing (auto-created if needed)

## Workflow

1. **User uploads video** - Any supported video format to the asset library
2. **System detects video** - Automatically triggered by file extension
3. **Download video** - Video file is streamed from Supabase Storage into memory
4. **Extract audio** - FFmpeg processes video via stdin/stdout to extract MP3
5. **Transcribe audio** - Extracted MP3 is sent to OpenAI Whisper API
6. **Upload files** - Both MP3 audio and transcription metadata are stored
7. **UI updates** - User sees green audio icon and can access both MP3 and transcription

## Supported Video Formats

- MP4 (.mp4)
- AVI (.avi)
- MOV (.mov)
- WMV (.wmv)
- FLV (.flv)
- WebM (.webm)
- MKV (.mkv)

## API Endpoints

### POST /extract-video-audio
Extracts audio from a video file.

**Request Body:**
```json
{
  "videoUrl": "https://example.com/video.mp4",
  "fileName": "video.mp4",
  "folderName": "uploads",
  "companyId": "uuid-string"
}
```

**Response:**
```json
{
  "success": true,
  "audioFileName": "generated-audio-file.mp3",
  "audioUrl": "https://storage.url/path/to/audio.mp3",
  "audioPath": "company-id/assets/folder/audio.mp3",
  "timestamp": "2025-01-01T00:00:00.000Z"
}
```

## Benefits

### For Users
- **Instant transcriptions** - Get searchable text content from video files
- **No waiting** - Processing happens in the background
- **Universal compatibility** - Works with all major video formats
- **Searchable content** - Find specific moments in videos via text search

### For Developers
- **Simple deployment** - No system dependencies or external services
- **Cost effective** - Only pay for OpenAI API usage
- **Reliable** - Leverages OpenAI's robust infrastructure
- **Serverless friendly** - Perfect for Google App Engine

## Troubleshooting

### OpenAI API Issues
If you see "OpenAI API key not configured" errors:
- Verify `OPENAI_API_KEY` environment variable is set
- Check that your OpenAI account has API access enabled
- Ensure you have sufficient API credits

### File Upload Errors
- Ensure the Supabase Storage bucket `brand-assets` exists
- Check that the service role key has proper permissions for storage operations
- Verify the video file URL is accessible and not behind authentication

### Performance Considerations
- OpenAI Whisper processing time depends on video length
- Large video files (>25MB) may hit OpenAI file size limits
- Consider implementing progress indicators for user feedback
- Monitor OpenAI API usage and billing

## File Naming Convention

Generated audio metadata files follow this pattern:
```
{uuid}-{sanitized-original-name}.audio-metadata.json
```

Example: `a1b2c3d4-my-video-file.audio-metadata.json`

### Metadata File Structure
```json
{
  "originalVideo": "https://storage.url/video.mp4",
  "originalFileName": "my-video.mp4",
  "transcription": "Hello world, this is the audio content...",
  "extractedAt": "2025-01-01T00:00:00.000Z",
  "audioFileName": "a1b2c3d4-my-video-file.mp3",
  "note": "Audio extracted via OpenAI Whisper API - transcription available"
}
```
