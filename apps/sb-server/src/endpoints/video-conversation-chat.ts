import { Request, Response } from 'express';
import { run } from '@openai/agents';
import { streamVideoConversationChat, ChatMessage, saveOverlaysToAutosave, OverlayResponse, VideoOverlay } from '../lib/utils/videoConversationAgent.js';
import { generateVideo } from '../lib/services/generate-video.js';

interface VideoConversationChatRequest {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  projectId?: string; // Keep for backwards compatibility
  sceneId?: string; // New scene-based architecture
  currentOverlay?: string;
}

export async function videoConversationChatHandler(req: Request, res: Response): Promise<void> {
  try {
    console.log(`🎬 [ENDPOINT] ===== VIDEO CONVERSATION CHAT START =====`);
    console.log(`🎬 [ENDPOINT] Request body keys:`, Object.keys(req.body));
    console.log(`🎬 [ENDPOINT] Request body:`, JSON.stringify(req.body, null, 2));
    
    const { messages, userId, companyId, conversationId, messageId, projectId, sceneId, currentOverlay }: VideoConversationChatRequest = req.body;
    console.log(`🎬 [ENDPOINT] Extracted parameters:`);
    console.log(`  - userId: ${userId}`);
    console.log(`  - companyId: ${companyId}`);
    console.log(`  - conversationId: ${conversationId}`);
    console.log(`  - messageId: ${messageId}`);
    console.log(`  - projectId: ${projectId}`);
    console.log(`  - sceneId: ${sceneId}`);
    console.log(`  - currentOverlay: ${currentOverlay}`);
    console.log(`  - messages count: ${messages?.length || 0}`);
    // Validate required fields
    console.log(`🔍 [ENDPOINT] Validating required fields...`);
    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      console.error(`❌ [ENDPOINT] Validation failed: Messages array is invalid`);
      res.status(400).json({ 
        error: 'Messages array is required and cannot be empty' 
      });
      return;
    }

    if (!userId || !companyId || !conversationId) {
      console.error(`❌ [ENDPOINT] Validation failed: Missing required fields`);
      console.error(`  - userId: ${userId ? '✓' : '✗'}`);
      console.error(`  - companyId: ${companyId ? '✓' : '✗'}`);
      console.error(`  - conversationId: ${conversationId ? '✓' : '✗'}`);
      res.status(400).json({ 
        error: 'userId, companyId, and conversationId are required' 
      });
      return;
    }
    
    console.log(`✅ [ENDPOINT] Required fields validation passed`);

    // Validate message structure
    console.log(`🔍 [ENDPOINT] Validating message structure for ${messages.length} messages...`);
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      console.log(`  - Message ${i + 1}: role="${msg?.role}", content length=${msg?.content?.length || 0}`);
      
      if (!msg.role) {
        console.error(`❌ [ENDPOINT] Message ${i + 1} validation failed: missing role or content`);
        res.status(400).json({ 
          error: 'Each message must have role and content' 
        });
        return;
      }
      if (!['user', 'assistant'].includes(msg.role)) {
        console.error(`❌ [ENDPOINT] Message ${i + 1} validation failed: invalid role "${msg.role}"`);
        res.status(400).json({ 
          error: 'Message role must be either "user" or "assistant"' 
        });
        return;
      }
    }

    // Ensure the latest message is from the user
    const latestMessage = messages[messages.length - 1];
    console.log(`🔍 [ENDPOINT] Latest message role: ${latestMessage.role}`);
    if (latestMessage.role !== 'user') {
      console.error(`❌ [ENDPOINT] Latest message validation failed: role is "${latestMessage.role}", expected "user"`);
      res.status(400).json({ 
        error: 'Latest message must be from user' 
      });
      return;
    }
    
    console.log(`✅ [ENDPOINT] Message structure validation passed`);

    console.log(`🚀 [ENDPOINT] Starting video conversation processing...`);
    
    // Create context for the video conversation agent
    const context = {
      userId,
      companyId,
      conversationId,
      messageId,
      projectId,
      sceneId,
      currentOverlay,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        created_at: Date.now(),
      })),
    };
    
    console.log(`📋 [ENDPOINT] Created context:`, {
      userId: context.userId,
      companyId: context.companyId,
      conversationId: context.conversationId,
      messageId: context.messageId,
      projectId: context.projectId,
      sceneId: context.sceneId,
      currentOverlay: context.currentOverlay,
      messagesCount: context.messages.length
    });

    // Get the agent and conversation details
    console.log(`🤖 [ENDPOINT] Creating video conversation agent...`);
    const { agent, userMessage } = await streamVideoConversationChat({
      messages,
      context,
    });

    console.log(`📨 [ENDPOINT] User message extracted: "${userMessage}"`);
    console.log(`🏃 [ENDPOINT] Running agent with context...`);

    // Run the agent with the user's message
    const result = await run(agent, userMessage, {
      context: context,
    });

    console.log(`🎯 [ENDPOINT] Agent run completed. Result:`, {
      hasFinalOutput: !!result.finalOutput,
      finalOutputLength: result.finalOutput?.length || 0,
      finalOutputPreview: result.finalOutput?.substring(0, 100) + (result.finalOutput?.length > 100 ? '...' : '')
    });

    // Get the response
    let responseContent = result.finalOutput || '';
    console.log(`🔍 [ENDPOINT] Response content:`, responseContent);
    if (!responseContent) {
      console.log(`⚠️ [ENDPOINT] No response content, using default message`);
      responseContent = "I'm here to help you create amazing videos! Please describe what kind of video you'd like to generate, and I'll help you create the perfect prompt.";
      
      // Return early for default response
      console.log(`📤 [ENDPOINT] Sending default response`);
      res.json({
        content: responseContent,
        role: 'assistant',
        timestamp: new Date().toISOString(),
      });
      return;
    }

    console.log(`✅ [ENDPOINT] Video conversation response generated (${responseContent.length} chars)`);
    console.log(`📝 [ENDPOINT] Response preview: ${responseContent.substring(0, 200)}${responseContent.length > 200 ? '...' : ''}`);

    // Try to process as overlay JSON if sceneId or projectId is provided
    console.log(`🎬 [ENDPOINT] Checking for overlay processing - Scene ID: ${sceneId}, Project ID: ${projectId}`);
    if (sceneId || projectId) {
      console.log(`🔄 [ENDPOINT] Starting overlay processing...`);
      try {
        console.log(`🎬 [ENDPOINT] Processing overlay JSON for scene ${sceneId} or project ${projectId}`);
        
        // Try to parse the response as JSON
        let overlayResponse: OverlayResponse;
        
        // Extract JSON from response (handle markdown code blocks)
        console.log(`🔍 [ENDPOINT] Searching for JSON in response...`);
        const jsonMatch = responseContent.match(/```json\n([\s\S]*?)\n```/) || 
                         responseContent.match(/```\n([\s\S]*?)\n```/) ||
                         responseContent.match(/\{[\s\S]*\}/) ||
                         [null, responseContent];
        
        const jsonString = jsonMatch[1] || jsonMatch[0] || responseContent;
        console.log(`🔍 [ENDPOINT] JSON extraction result:`, {
          foundMatch: !!jsonMatch[1] || !!jsonMatch[0],
          jsonStringLength: jsonString.length,
          jsonPreview: jsonString.substring(0, 200) + (jsonString.length > 200 ? '...' : '')
        });
        
        try {
          console.log(`🔧 [ENDPOINT] Attempting to parse JSON...`);
          overlayResponse = JSON.parse(jsonString);
          console.log(`✅ [ENDPOINT] JSON parsed successfully:`, {
            hasOverlays: !!overlayResponse.overlays,
            overlaysCount: overlayResponse.overlays?.length || 0,
            hasAspectRatio: !!overlayResponse.aspectRatio,
            hasPlayerDimensions: !!overlayResponse.playerDimensions
          });
        } catch (parseError) {
          console.log(`⚠️ [ENDPOINT] JSON parsing failed:`, parseError.message);
          console.log(`📤 [ENDPOINT] Treating as regular text response`);
          // Not JSON, treat as regular text response
          res.json({
            content: responseContent,
            role: 'assistant',
            timestamp: new Date().toISOString(),
          });
          return;
        }

        // Validate overlay structure
        console.log(`🔍 [ENDPOINT] Validating overlay structure...`);
        if (!overlayResponse.overlays || !Array.isArray(overlayResponse.overlays)) {
          console.log(`⚠️ [ENDPOINT] Invalid overlay structure:`, {
            hasOverlays: !!overlayResponse.overlays,
            overlaysType: typeof overlayResponse.overlays,
            isArray: Array.isArray(overlayResponse.overlays)
          });
          console.log(`📤 [ENDPOINT] Treating as regular text response`);
          res.json({
            content: responseContent,
            role: 'assistant',
            timestamp: new Date().toISOString(),
          });
          return;
        }
        
        console.log(`✅ [ENDPOINT] Overlay structure is valid`);

        // Add defaults if missing
        if (!overlayResponse.aspectRatio) {
          overlayResponse.aspectRatio = "9:16";
        }
        if (!overlayResponse.playerDimensions) {
          overlayResponse.playerDimensions = { width: 564.8642578125, height: 1004.203125 };
        }

        // Replace LLM-generated UUIDs with proper server-generated UUIDs
        console.log(`🔄 [ENDPOINT] Replacing LLM-generated overlay IDs with proper UUIDs...`);
        const { v4: uuidv4 } = await import('uuid');
        const idMapping: Record<string, string> = {};
        
        overlayResponse.overlays = overlayResponse.overlays.map((overlay, index) => {
          const oldId = overlay.id;
          const newId = uuidv4();
          idMapping[oldId] = newId;
          
          console.log(`  - Overlay ${index + 1}: ${oldId} → ${newId}`);
          
          return {
            ...overlay,
            id: newId
          };
        });

        console.log(`📋 [ENDPOINT] Generated ${overlayResponse.overlays.length} overlays with proper UUIDs`);
        console.log(`📋 [ENDPOINT] ID mapping:`, idMapping);
        console.log(`📋 [ENDPOINT] Overlay data:`, JSON.stringify(overlayResponse, null, 2));

        // Save overlays to database

        // console.log(`💾 [ENDPOINT] Saving overlays to ${targetType} ${targetId} for user ${userId}`);
        await saveOverlaysToAutosave(
          projectId!,
          sceneId!,
          userId,
          overlayResponse.overlays,
          overlayResponse.aspectRatio,
          overlayResponse.playerDimensions
        );
        console.log(`✅ [ENDPOINT] Overlays saved successfully`);

        // Check if any overlays require video generation
        const videoOverlays = overlayResponse.overlays.filter(overlay => 
          overlay.type === 'video' && (overlay as any).loading
        );

        // Trigger video generation for video overlays
        for (const videoOverlay of videoOverlays) {
          console.log(`🎥 [ENDPOINT] Starting generation for overlay ${videoOverlay.id}`);
          
          try {
            await generateVideo({
              video_prompt: (videoOverlay as VideoOverlay).video_prompt || '',
              duration: (videoOverlay as VideoOverlay).duration || 8,
              model: 'veo-3-fast',
              aspect_ratio: overlayResponse.aspectRatio === '9:16' ? '9:16' : '16:9',
              company_id: companyId,
              user_id: userId,
              message_id: messageId,
              conversation_id: conversationId,
              overlay_id: videoOverlay.id, // This is already the proper UUID after ID mapping
              scene_id: sceneId, // Use scene_id for new architecture
              project_id: projectId, // Keep project_id for backwards compatibility
            });
          } catch (videoError) {
            console.error(`❌ [ENDPOINT] Failed to start generation for overlay ${videoOverlay.id}:`, videoError);
            // Continue with other overlays even if one fails
          }
        }

        // Generate user-friendly response
        const overlayCount = overlayResponse.overlays.length;
        const videoCount = videoOverlays.length;
        const textCount = overlayResponse.overlays.filter(o => o.type === 'text').length;
        const shapeCount = overlayResponse.overlays.filter(o => o.type === 'shape').length;

        let userResponse = `✅ Created ${overlayCount} overlay${overlayCount !== 1 ? 's' : ''}`;
        
        if (textCount > 0) userResponse += ` (${textCount} text`;
        if (shapeCount > 0) userResponse += `${textCount > 0 ? ', ' : ' ('}${shapeCount} shape`;
        if (videoCount > 0) userResponse += `${textCount > 0 || shapeCount > 0 ? ', ' : ' ('}${videoCount} video`;
        if (textCount > 0 || shapeCount > 0 || videoCount > 0) userResponse += ')';

        if (videoCount > 0) {
          userResponse += `. Video${videoCount !== 1 ? 's' : ''} will appear on the timeline once generation completes.`;
        }

        console.log(`✅ [ENDPOINT] Processed overlay message successfully`);

        // Return the user-friendly response
        res.json({
          content: userResponse,
          role: 'assistant',
          timestamp: new Date().toISOString(),
        });

      } catch (error) {
        console.error('❌ [ENDPOINT] Error processing overlay JSON:', {
          error: error.message,
          stack: error.stack,
          errorType: error.constructor.name
        });
        
        // Fallback to regular text response
        console.log(`📤 [ENDPOINT] Falling back to regular text response due to error`);
        res.json({
          content: responseContent,
          role: 'assistant',
          timestamp: new Date().toISOString(),
        });
      }
    } else {
      // No sceneId or projectId, return regular response
      console.log(`📤 [ENDPOINT] No scene/project ID provided, returning regular response`);
      res.json({
        content: responseContent,
        role: 'assistant',
        timestamp: new Date().toISOString(),
      });
    }

    console.log(`🎬 [ENDPOINT] ===== VIDEO CONVERSATION CHAT END =====`);

  } catch (error) {
    console.error('💥 [ENDPOINT] FATAL ERROR in video conversation chat:', {
      error: error.message,
      stack: error.stack,
      errorType: error.constructor.name,
      timestamp: new Date().toISOString()
    });
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const statusCode = errorMessage.includes('validation') || errorMessage.includes('required') ? 400 : 500;
    
    console.log(`📤 [ENDPOINT] Sending error response with status ${statusCode}`);
    res.status(statusCode).json({
      error: 'Failed to process video conversation',
      message: errorMessage,
      timestamp: new Date().toISOString(),
    });
    
    console.log(`🎬 [ENDPOINT] ===== VIDEO CONVERSATION CHAT END (ERROR) =====`);
  }
}
