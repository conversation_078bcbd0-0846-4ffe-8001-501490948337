import { Request, Response } from "express";
import { ElevenLabsClient } from "elevenlabs";

const ELEVEN_LABS_API_KEY = "***************************************************";

// Voice IDs for different languages
const VOICE_IDS = {
  English: "RiK8PTtVIeKKoFFTk9fg", // Josh voice
};

export async function textToSpeech(req: Request, res: Response) {
  try {
    const { text, language = "English" } = req.body;

    if (!text) {
      return res.status(400).json({ error: "Text is required" });
    }

    const client = new ElevenLabsClient({
      apiKey: ELEVEN_LABS_API_KEY,
    });

    // Select the appropriate voice based on language
    const voiceId = VOICE_IDS[language as keyof typeof VOICE_IDS];
    if (!voiceId) {
      return res.status(400).json({ error: `Unsupported language: ${language}` });
    }

    // Arrays to store audio chunks and alignment data
    const audioChunks: Uint8Array[] = [];

    // Get the streaming response with timestamps
    const streamResponse = await client.textToSpeech.streamWithTimestamps(voiceId, {
      text,
      model_id: "eleven_multilingual_v2",
      output_format: "mp3_44100_128",
    });

    // Process each chunk from the stream
    let chunkCount = 0;
    const alignmentData = {
      characters: [],
      character_start_times_seconds: [],
      character_end_times_seconds: []
    };

    for await (const chunk of streamResponse) {
      chunkCount++;
      // Get alignment data from the first chunk
      if (chunk.normalized_alignment) {
        alignmentData.characters.push(...(chunk.normalized_alignment.characters || []));
        alignmentData.character_start_times_seconds.push(...(chunk.normalized_alignment.character_start_times_seconds || []));
        alignmentData.character_end_times_seconds.push(...(chunk.normalized_alignment.character_end_times_seconds || []));
      }

      if (chunk.audio_base64) {
        audioChunks.push(Buffer.from(chunk.audio_base64, 'base64'));
      }
    }

    if (!alignmentData.characters.length) {
      throw new Error("No alignment data received");
    }

    // Combine all audio chunks
    const audioData = Buffer.concat(audioChunks);
    const audioBase64 = audioData.toString('base64');

    // Process the timestamp data to get word-level timestamps
    const words = text.split(/\s+/);
    const { characters, character_start_times_seconds, character_end_times_seconds } = alignmentData;

    if (!characters || !character_start_times_seconds || !character_end_times_seconds) {
      throw new Error("Incomplete alignment data received");
    }

    // Create word-level timestamps by tracking word boundaries
    const timestamps = [];
    let currentWord = '';
    let wordStartIndex = 0;

    for (let i = 0; i < characters.length; i++) {
      const char = characters[i];

      // If we're at a word boundary (space or punctuation)
      if (char === ' ' || char === '.' || char === ',' || char === '!' || char === '?' || i === characters.length - 1) {
        // If we have accumulated a word, add it to timestamps
        if (currentWord.trim()) {
          timestamps.push({
            word: currentWord.trim(),
            start: character_start_times_seconds[wordStartIndex],
            end: character_end_times_seconds[i]
          });
        }
        // Reset for next word
        currentWord = '';
        wordStartIndex = i + 1;
      } else {
        currentWord += char;
      }
    }

    // Get the total duration from the last timestamp
    const duration = character_end_times_seconds[character_end_times_seconds.length - 1] || 0;

    // Create properly segmented transcript using the word timestamps
    const detailedTranscript = [];

    // Group words into 8-word segments for optimal caption display
    const segments = [];
    let currentSegment = [];

    for (let i = 0; i < timestamps.length; i++) {
      const word = timestamps[i];
      currentSegment.push(word);

      // Break segment when we have 8 words or reach the end
      const isLastWord = i === timestamps.length - 1;
      const hasEightWords = currentSegment.length >= 8;

      if (hasEightWords || isLastWord) {
        if (currentSegment.length > 0) {
          segments.push([...currentSegment]);
          currentSegment = [];
        }
      }
    }

    // Convert segments to detailed transcript format
    for (const segment of segments) {
      if (segment.length === 0) continue;

      const segmentText = segment.map(w => w.word).join(' ');
      const startTime = segment[0].start;
      const endTime = segment[segment.length - 1].end;

      detailedTranscript.push({
        transcript: segmentText,
        confidence: 0.95,
        words: segment.map(w => ({
          word: w.word,
          start: w.start,
          end: w.end
        })),
        startTime,
        endTime
      });
    }

    // Create robust transcript object
    const robustTranscript = {
      originalText: text,
      fullTranscript: text,
      detailedTranscript,
      duration,
      wordCount: text.split(/\s+/).length,
      averageConfidence: 0.95, // TTS typically has high confidence
      generatedAt: new Date().toISOString(),
      language,
      voiceId
    };

    return res.json({
      audioBase64,
      timestamps,
      duration,
      transcript: robustTranscript
    });
  } catch (error: any) {
    console.error("Text-to-speech error:", error);
    return res.status(500).json({ error: error.message });
  }
}