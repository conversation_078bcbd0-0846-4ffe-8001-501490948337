import { Request, Response } from 'express';
import { run } from '@openai/agents';
import { streamImageEditingChat, ChatMessage } from '../lib/utils/imageEditingAgent.js';
import postgres from 'postgres';
import crypto from 'crypto';

const sql = postgres(process.env.ZERO_UPSTREAM_DB! as string);

interface EditChatRequestBody {
  messages: ChatMessage[];
  userId: string;
  companyId: string;
  conversationId: string;
  originalImageUrl: string;
  referenceImages?: string[];
}

interface EditChatRequest extends Request {
  body: EditChatRequestBody;
}

export async function imageEditingChatHandler(req: EditChatRequest, res: Response): Promise<void> {
    try {
      console.log('Image editing chat request received:', {
        messagesCount: req.body.messages?.length,
        userId: req.body.userId,
        companyId: req.body.companyId,
        conversationId: req.body.conversationId,
        originalImageUrl: req.body.originalImageUrl?.substring(0, 50) + '...',
        hasReferenceImages: req.body.referenceImages?.length > 0,
        referenceImagesCount: req.body.referenceImages?.length || 0
      });

      const { messages, userId, companyId, conversationId, originalImageUrl, referenceImages } = req.body;

      // Validate required fields
      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        console.error('Validation failed: Messages array is required and cannot be empty');
        // return res.status(400).json({ error: 'Messages array is required and cannot be empty' });
        throw new Error('Messages array is required and cannot be empty');
      }

      if (!userId || !companyId || !conversationId) {
        console.error('Validation failed: Missing required fields', { userId, companyId, conversationId });
        // return res.status(400).json({ error: 'userId, companyId, and conversationId are required' });
        throw new Error('userId, companyId, and conversationId are required');
      }

      if (!originalImageUrl) {
        console.error('Validation failed: Missing originalImageUrl');
        // return res.status(400).json({ error: 'originalImageUrl is required for image editing' });
        throw new Error('originalImageUrl is required for image editing');
      }

      // Validate message format
      for (const message of messages) {
        if (!message.role || !message.content) {
          console.error('Validation failed: Invalid message format', message);
          // return res.status(400).json({ error: 'Each message must have role and content' });
          throw new Error('Each message must have role and content');
        }
        if (!['user', 'assistant'].includes(message.role)) {
          console.error('Validation failed: Invalid message role', message.role);
          // return res.status(400).json({ error: 'Message role must be either "user" or "assistant"' });
          throw new Error('Message role must be either "user" or "assistant"');
        }
      }

      const context = {
        userId,
        companyId,
        conversationId,
        originalImageUrl,
        referenceImages: referenceImages || [],
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          created_at: Date.now(),
        })),
      };

      console.log('Creating image editing agent and setting up conversation...');
      console.log('Context for agent:', {
        originalImageUrl: context.originalImageUrl,
        referenceImagesCount: context.referenceImages?.length || 0,
        messagesCount: context.messages.length
      });
      
      // Create the agent and get conversation setup
      const { agent, userMessage } = await streamImageEditingChat({
        messages,
        context,
      });

      console.log('Image editing agent created successfully, user message:', userMessage);
      console.log('Starting agent run...');

      try {
        // Run the agent with context to enable tool access
        const result = await run(agent, userMessage, {
          context: context,
        });
        
        console.log('Agent run completed, final output:', result.finalOutput);

        // Check if any images were generated by looking for URLs in the response
        let imageUrl = null;
        if (result.finalOutput && typeof result.finalOutput === 'string') {
          // Extract image URL from the response (look for URLs that end with image extensions or contain image hosting domains)
          const urlRegex = /(https?:\/\/[^\s\)]+(?:\.(?:png|jpg|jpeg|gif|webp)|imgbb\.com\/[^\s\)]+|supabase[^\s\)]*generated[^\s\)]*\.png))/gi;
          const matches = result.finalOutput.match(urlRegex);
          if (matches && matches.length > 0) {
            imageUrl = matches[0];
            console.log('Edited image URL found in response:', imageUrl);
          }
        }

        // Save the AI response directly to the database
        if (result.finalOutput) {
          const assistantMessageId = crypto.randomUUID();
          await sql`
            INSERT INTO image_messages (id, content, role, user_id, company_id, conversation_id, image_path, reference_images, created_at)
            VALUES (
              ${assistantMessageId},
              ${result.finalOutput},
              'assistant',
              ${userId},
              ${companyId},
              ${conversationId},
              ${imageUrl},
              ${referenceImages ? JSON.stringify(referenceImages) : null},
              ${Date.now()}
            )
          `;
          console.log('AI response saved to database with ID:', assistantMessageId, 'Image URL:', imageUrl, 'Reference Images:', referenceImages);
        }

        // Send success response
        res.status(200).json({
          success: true,
          timestamp: new Date().toISOString(),
        });

        console.log('Response sent successfully');
      } catch (runError) {
        console.error('Error during agent run:', runError);
        console.error('Run error stack:', runError.stack);
        res.status(500).json({
          error: 'Failed to generate response',
          message: runError instanceof Error ? runError.message : 'Unknown error'
        });
      }

    } catch (error) {
      console.error('Error in image editing chat:', error);
      console.error('Main error stack:', error instanceof Error ? error.stack : 'No stack trace');
      
      // If headers haven't been sent yet, send JSON error
      if (!res.headersSent) {
        console.log('Sending JSON error response (headers not sent)');
        res.status(500).json({ 
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
        return;
      }
      
      // If streaming has started, send error via SSE
      console.log('Sending SSE error response (headers already sent)');
      res.write(`data: ${JSON.stringify({
        type: 'error',
        error: 'Internal server error',
        done: true,
      })}\n\n`);
      res.end();
    }
}
