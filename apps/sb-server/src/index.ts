import express, { Request, Response } from "express";
import dotenv from "dotenv";
import {
  PushProcessor,
  ZQLDatabase,
  PostgresJSConnection,
} from '@rocicorp/zero/pg';
import postgres from 'postgres';
import {schema} from './lib/zero-schema/schema.js';
import {createServerMutators} from './lib/server-mutators.js';
import { crawlWebsite, getCrawlStatus } from './lib/services/crawl-website.js';
import { generateContentSchedule } from './lib/services/generate-schedule.js';
import { generatePersona } from './lib/services/generate-persona.js';
import { scrapeWebsite } from './lib/services/scrape-website.js';
import { scrapeWebsiteByElement } from './lib/services/scrape-website-by-element.js';
import { generateVisualDescription } from './lib/services/generate-visual-description.js';
import { extractFromWebsite, getExtractStatus } from './lib/services/extract-from-website.js';
import { scrapeReddit } from './lib/services/scrape-reddit.js';
import { scrapeTikTok } from './lib/services/scrape-tiktok.js';
import { scrapeTwitter } from './lib/services/scrape-x.js';
import { generateCampaignName } from './lib/services/generate-campaign-name.js';
import { generateTaskContent } from './lib/services/generate-task-content.js';
import { generateImages } from './lib/services/generate-images.js';
import { imageConversationChatHandler } from './endpoints/image-conversation-chat.js';
import { imageEditingChatHandler } from './endpoints/image-editing-chat.js';
import { videoConversationChatHandler } from './endpoints/video-conversation-chat.js';
import { extractVideoAudio } from './lib/services/extract-video-audio.js';
import { extractAudioTranscript } from './lib/services/extract-audio-transcript.js';
import { textToSpeech } from './endpoints/text-to-speech.js';
dotenv.config();

const app = express();
const PORT = process.env.PORT || 8080;
const BASE_URL = process.env.BASE_URL || `http://localhost:${PORT}`;

app.use(express.json({ limit: "5mb" }));
app.use(express.urlencoded({ extended: true, limit: "5mb" }));

const processor = new PushProcessor(
  new ZQLDatabase(
    new PostgresJSConnection(postgres(process.env.ZERO_UPSTREAM_DB! as string)),
    schema
  )
);

app.get("/", (req: Request, res: Response) => {
  res.json({ message: "Hello World! TypeScript server is running." });
});

app.get("/env-test", (req: Request, res: Response) => {
  res.json({
    message: process.env.TEST_MESSAGE,
    environment: process.env.NODE_ENV,
    dbHost: process.env.DB_HOST,
    dbPort: process.env.DB_PORT,
  });
});

app.post("/push", async (req: Request, res: Response) => {
  
  const asyncTasks: Array<() => Promise<void>> = [];

  // Create a Web API compatible Request object
  const webRequest = new globalThis.Request(`${BASE_URL}${req.url}`, {
    method: "POST",
    headers: req.headers as HeadersInit,
    body: JSON.stringify(req.body),
  });

  // Use server mutators instead of client mutators
  const result = await processor.process(
    createServerMutators({ sub: "" }, asyncTasks),
    webRequest
  );

  // ✅ Execute async tasks AFTER the database transaction commits
  await Promise.all(asyncTasks.map((task) => task()));

  
  res.json(result);
});

app.get("/crawl", async (req: Request, res: Response) => {
  const result = await crawlWebsite(
    req.query.url as string,
    parseInt(req.query.limit as string),
    ["markdown"]
  );
  res.json(result);
});

app.post("/extract-from-website", async (req: Request, res: Response) => {
  const { urls, prompt, schema, enableWebSearch } = req.body;
  console.log(
    "urls",
    urls,
    "prompt",
    prompt,
    "schema",
    schema,
    "enableWebSearch",
    enableWebSearch
  );
  const result = await extractFromWebsite(
    urls,
    prompt,
    schema,
    enableWebSearch
  );
  res.json(result);
});

app.get("/extract-status", async (req: Request, res: Response) => {
  const extractId = req.query.extractId as string;
  
  const result = await getExtractStatus(extractId);
  res.json(result);
});

app.get("/crawl-status", async (req: Request, res: Response) => {
  const result = await getCrawlStatus(req.query.crawlId as string);
  res.json(result);
});

app.post("/generate-schedule", async (req: Request, res: Response) => {
  const {
    productDocumentation,
    campaignGoal,
    endDate,
    startDate,
    externalResearch,
  } = req.body;
  const result = await generateContentSchedule({
    productDocumentation,
    campaignGoal,
    startDate,
    endDate,
    externalResearch,
  });
  res.json(result);
});

app.post("/generate-persona", async (req: Request, res: Response) => {
  const { icp_data } = req.body;
  const result = await generatePersona(icp_data);
  res.json(result);
});

app.get("/scrape", async (req: Request, res: Response) => {
  try {
    const url = req.query.url as string;

    if (!url) {
      res.status(400).json({ error: "URL parameter is required" });
      return;
    }

    const result = await scrapeWebsite(url);
    res.json(result);
  } catch (error) {
    console.error("Error in scrape endpoint:", error);
    res.status(500).json({
      error: "Failed to scrape website",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

app.post("/scrape-by-element", async (req: Request, res: Response) => {
  const { url, element_prompts, json_output, instructions } = req.body;
  console.log(
    "url",
    url,
    "element_prompts",
    element_prompts,
    "json_output",
    json_output,
    "instructions",
    instructions
  );
  const result = await scrapeWebsiteByElement(
    url,
    element_prompts,
    json_output,
    instructions
  );
  res.json(result);
});

app.post("/generate-visual-description", async (req: Request, res: Response) => {
  try {
    const { brand_brief, content } = req.body;
    const result = await generateVisualDescription(brand_brief, content);
    
    res.json(result);
  } catch (error) {
    console.error("Error in generate visual description endpoint:", error);
    res.status(500).json({
      error: "Failed to generate visual description",
      message: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

app.post("/generate-campaign-name", async (req: Request, res: Response) => {
  try {
    const { campaignInformation } = req.body;
    

    if (!campaignInformation) {
      res
        .status(400)
        .json({ error: "campaignInformation parameter is required" });
      return;
    }

    const result = await generateCampaignName(campaignInformation);
    res.json({
      success: true,
      campaignName: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in generate-campaign-name endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate campaign name",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Social Media Scraping Endpoints
app.post("/scrape/reddit", async (req: Request, res: Response) => {
  try {
    const params = req.body;
    

    const result = await scrapeReddit(params);
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in Reddit scrape endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to scrape Reddit",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.post("/scrape/tiktok", async (req: Request, res: Response) => {
  try {
    const params = req.body;
    

    const result = await scrapeTikTok(params);
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in TikTok scrape endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to scrape TikTok",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.post("/scrape/twitter", async (req: Request, res: Response) => {
  try {
    const params = req.body;
    

    const result = await scrapeTwitter(params);
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in Twitter scrape endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to scrape Twitter",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.post("/generate-task-content", async (req: Request, res: Response) => {
  try {
    const {
      taskTitle,
      taskDescription,
      contentType,
      channel,
      campaignGoal,
      productInformation,
      targetICPs,
      targetPersonas,
      companyBrand,
      externalResearch
    } = req.body;

    if (!taskTitle || !taskDescription || !contentType || !channel || !campaignGoal) {
      res.status(400).json({ 
        error: "Missing required fields: taskTitle, taskDescription, contentType, channel, and campaignGoal are required" 
      });
      return;
    }

    const result = await generateTaskContent({
      taskTitle,
      taskDescription,
      contentType,
      channel,
      campaignGoal,
      productInformation: productInformation || "",
      targetICPs: targetICPs || "",
      targetPersonas: targetPersonas || "",
      companyBrand: companyBrand || {},
      externalResearch: externalResearch || ""
    });

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in generate-task-content endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate task content",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

app.post("/generate-images", async (req: Request, res: Response) => {
  try {
    const { image_prompt, aspect_ratio, company_id, image_gen_styles, brand_context } = req.body;

    if (!image_prompt || !company_id) {
      res.status(400).json({ error: 'Missing required fields: image_prompt and company_id are required' });
      return;
    }

    const result = await generateImages({
      image_prompt,
      aspect_ratio: aspect_ratio || "custom",
      company_id,
      image_gen_styles,
      brand_context
    });

    res.json({
      success: true,
      url: result.url,
      path: result.path,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in /generate-images endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to generate image",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Image conversation chat endpoint
app.post("/image-conversation-chat", imageConversationChatHandler);

// Video conversation chat endpoint
app.post("/video-conversation-chat", videoConversationChatHandler);


// Image editing chat endpoint
app.post("/image-editing-chat", imageEditingChatHandler);

// Video-to-audio extraction endpoint
app.post("/extract-video-audio", async (req: Request, res: Response) => {
  try {
    const { videoUrl, fileName, folderName, companyId } = req.body;

    if (!videoUrl || !fileName || !folderName || !companyId) {
      res.status(400).json({ 
        error: "Missing required fields: videoUrl, fileName, folderName, and companyId are required" 
      });
      return;
    }

    const result = await extractVideoAudio({
      videoUrl,
      fileName,
      folderName,
      companyId,
    });

    res.json({
      success: true,
      audioFileName: result.audioFileName,
      audioUrl: result.audioUrl,
      audioPath: result.audioPath,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in /extract-video-audio endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to extract audio from video",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Audio transcript extraction endpoint
app.post("/extract-audio-transcript", async (req: Request, res: Response) => {
  try {
    const { audioUrl, fileName, folderName, companyId, assetId } = req.body;

    if (!audioUrl || !fileName || !folderName || !companyId) {
      res.status(400).json({ 
        error: "Missing required fields: audioUrl, fileName, folderName, and companyId are required" 
      });
      return;
    }

    console.log('🎵 [ENDPOINT] Extract audio transcript request:', {
      audioUrl: audioUrl.substring(0, 50) + '...',
      fileName,
      folderName,
      companyId,
      assetId
    });

    const result = await extractAudioTranscript({
      audioUrl,
      fileName,
      folderName,
      companyId,
      assetId,
    });

    res.json({
      success: true,
      transcriptFileName: result.transcriptFileName,
      transcriptUrl: result.transcriptUrl,
      transcriptPath: result.transcriptPath,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in /extract-audio-transcript endpoint:", error);
    res.status(500).json({
      success: false,
      error: "Failed to extract transcript from audio",
      message: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// Text-to-speech endpoint
app.post("/text-to-speech", async (req, res) => {
  await textToSpeech(req, res);
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
