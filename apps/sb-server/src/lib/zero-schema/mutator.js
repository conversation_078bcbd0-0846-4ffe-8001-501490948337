// Generate a simple unique ID (similar to getUniqueId from utils.ts)
function generateUniqueId(length = 10) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
// Generate slug from campaign name (similar to campaign.ts)
function generateSlug(name) {
    const safeName = name || 'untitled';
    return `${safeName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-') // Replace any non-alphanumeric char with hyphen
        .replace(/^-+|-+$/g, '')}-${generateUniqueId()}`; // Remove leading/trailing hyphens
}
export function createMutators() {
    return {
        accounts: {
            insert: async (tx, { id, name, primary_owner_user_id, is_personal_account, website }) => {
                await tx.mutate.accounts.insert({
                    id,
                    name,
                    primary_owner_user_id,
                    is_personal_account,
                    website,
                    public_data: {},
                });
            },
        },
        company_brand: {
            insert: async (tx, { id, values, isHTML, html, brand_document_blob_obj }) => {
                await tx.mutate.company_brand.insert({
                    id,
                    ...values,
                });
            },
            update: async (tx, { id, values }) => {
                console.log(`values `, {
                    id,
                    ...values,
                });
                await tx.mutate.company_brand.update({
                    id,
                    ...values,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.company_brand.delete({
                    id,
                });
            },
        },
        ayrshare_user_profile: {
            insert: async (tx, { id, values }) => {
                await tx.mutate.ayrshare_user_profile.insert({
                    id,
                    ...values,
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.ayrshare_user_profile.update({
                    id,
                    ...values,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.ayrshare_user_profile.delete({
                    id,
                });
            },
        },
        ayrshare_social_profiles: {
            upsert: async (tx, { id, values }) => {
                await tx.mutate.ayrshare_social_profiles.upsert({
                    id,
                    ...values,
                });
            },
        },
        site_research: {
            insert: async (tx, { id, values }) => {
                await tx.mutate.site_research.insert({
                    id,
                    ...values,
                });
            },
        },
        generated_research: {
            insert: async (tx, { id, account_id, icp_id, persona_id, research_type, time_filter, title, topic, created_by }) => {
                const now = Date.now();
                await tx.mutate.generated_research.insert({
                    id,
                    account_id,
                    icp_id,
                    persona_id,
                    research_type,
                    time_filter,
                    title,
                    topic,
                    results: [],
                    content_suggestions: [],
                    is_generating: true,
                    created_at: now,
                    updated_at: now,
                    created_by,
                    updated_by: created_by,
                });
            },
        },
        saved_research: {
            insert: async (tx, { id, account_id, icp_id, persona_id, research_type, time_filter, title, topic, description, source, source_url }) => {
                await tx.mutate.saved_research.insert({
                    id,
                    account_id,
                    icp_id,
                    persona_id,
                    research_type,
                    time_filter,
                    title,
                    topic,
                    description,
                    source,
                    source_url
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.saved_research.update({
                    id,
                    ...values,
                });
            },
        },
        company_campaigns: {
            insert: async (tx, { id, company_id, user_id, purpose, objective, start_date, end_date, templateId, external_research, products, target_icps, target_personas, channels, posts_per_week, color_tag }) => {
                const now = Date.now();
                // Convert dates to timestamps
                const startTimestamp = new Date(start_date).getTime();
                const endTimestamp = new Date(end_date).getTime();
                console.log('🚀 channels, posts_per_week, color_tag:', { channels, posts_per_week, color_tag });
                await tx.mutate.company_campaigns.insert({
                    id,
                    created_at: now,
                    company_id,
                    user_id,
                    name: purpose || 'Untitled Campaign', // Temporary name, will be replaced by server mutator
                    slug: generateSlug(purpose || 'untitled-campaign'),
                    objective,
                    start_date: startTimestamp,
                    end_date: endTimestamp,
                    is_generating: true,
                    status: 'Draft',
                    external_research: (external_research && external_research.length > 0) ? external_research : null,
                    products: (products && products.length > 0) ? products : null,
                    metadata: { templateId, purpose },
                    target_icps,
                    target_personas,
                    channels: (channels && channels.length > 0) ? channels : null,
                    posts_per_week: posts_per_week || 3,
                    color_tag: color_tag || null
                });
            },
            update: async (tx, { id, values, regenerate = false }) => {
                await tx.mutate.company_campaigns.update({
                    id,
                    ...values,
                });
            },
        },
        user_cache: {
            upsert: async (tx, { user_id, values }) => {
                const now = Date.now();
                await tx.mutate.user_cache.upsert({
                    user_id,
                    ...values,
                    created_at: now,
                });
            },
            update: async (tx, { user_id, values }) => {
                console.log('🚀 User cache update mutator called for user:', user_id, 'with values:', values);
                await tx.mutate.user_cache.update({
                    user_id,
                    ...values,
                });
            },
        },
        icps: {
            update: async (tx, { id, name, data }) => {
                await tx.mutate.icps.update({
                    id,
                    name,
                    data,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.icps.delete({
                    id,
                });
            },
            insert: async (tx, { id, values, }) => {
                const now = Date.now();
                await tx.mutate.icps.insert({
                    id,
                    ...values,
                    created_at: now,
                });
            },
        },
        personas: {
            update: async (tx, { id, name, data }) => {
                await tx.mutate.personas.update({
                    id,
                    name,
                    data,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.personas.delete({
                    id,
                });
            },
            insert: async (tx, { id, company_id, icp_id, withAi, name, data = {}, error_generating = false }) => {
                const now = Date.now();
                await tx.mutate.personas.insert({
                    id,
                    company_id,
                    icp_id,
                    created_at: now,
                    is_generating: withAi ? true : false,
                    name,
                    data,
                    error_generating,
                });
            },
        },
        company_content: {
            update: async (tx, args) => {
                // Extract values if provided, otherwise use all args except id
                const { id, values, ...directValues } = args;
                const updateData = values || directValues;
                const now = Date.now();
                // Automatically set updated_at timestamp for any update
                const updatedValues = {
                    ...updateData,
                    updated_at: now,
                };
                await tx.mutate.company_content.update({
                    id,
                    ...updatedValues,
                });
            },
            insert: async (tx, { id, values }) => {
                await tx.mutate.company_content.insert({
                    id,
                    ...values,
                });
            },
        },
        post_engagement_details: {
            insert: async (tx, { id, values }) => {
                console.log('🚀 post_engagement_details insert mutator called with ID:', id, { values });
                await tx.mutate.post_engagement_details.insert({
                    id,
                    ...values,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.post_engagement_details.delete({
                    id,
                });
            },
        },
        company_task_statuses: {
            insert: async (tx, { id, values }) => {
                await tx.mutate.company_task_statuses.insert({
                    id,
                    ...values,
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.company_task_statuses.update({
                    id,
                    ...values,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.company_task_statuses.delete({
                    id,
                });
            },
        },
        socials_research: {
            insert: async (tx, { id, values }) => {
                await tx.mutate.socials_research.insert({
                    id,
                    ...values,
                });
            },
        },
        products: {
            insert: async (tx, { id, name, description, target_audience, key_features, company_id, custom_fields }) => {
                const now = Date.now();
                await tx.mutate.products.insert({
                    id,
                    name,
                    description,
                    target_audience: target_audience || [],
                    key_features: key_features || [],
                    company_id,
                    custom_fields: custom_fields || {},
                    created_at: now,
                    updated_at: now,
                });
            },
            update: async (tx, { id, values }) => {
                const now = Date.now();
                await tx.mutate.products.update({
                    id,
                    ...values,
                    updated_at: now,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.products.delete({
                    id,
                });
            },
        },
        image_conversations: {
            insert: async (tx, { id, values }) => {
                const { image_path, user_id, company_id } = values;
                await tx.mutate.image_conversations.insert({
                    id,
                    image_path,
                    user_id,
                    company_id,
                    created_at: Date.now(),
                    updated_at: Date.now(),
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.image_conversations.update({
                    id,
                    ...values,
                    updated_at: Date.now(),
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.image_conversations.delete({
                    id,
                });
            },
        },
        image_messages: {
            insert: async (tx, { id, values }) => {
                const { content, role, user_id, company_id, conversation_id, image_path } = values;
                await tx.mutate.image_messages.insert({
                    id,
                    content,
                    role,
                    user_id,
                    company_id,
                    conversation_id,
                    image_path,
                    created_at: Date.now(),
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.image_messages.update({
                    id,
                    ...values,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.image_messages.delete({
                    id,
                });
            },
        },
        // Video Chat Mutators
        video_conversations: {
            insert: async (tx, { id, values }) => {
                const { title, user_id, company_id, video_project_id } = values;
                await tx.mutate.video_conversations.insert({
                    id,
                    title,
                    user_id,
                    company_id,
                    video_project_id,
                    created_at: Date.now(),
                    updated_at: Date.now(),
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.video_conversations.update({
                    id,
                    ...values,
                    updated_at: Date.now(),
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.video_conversations.delete({
                    id,
                });
            },
        },
        video_messages: {
            insert: async (tx, { id, values }) => {
                const { content, role, user_id, company_id, conversation_id, video_project_id, is_generating, is_error, video_generation_id, video_generation_model, video_generation_status, video_generation_prompt, generated_video_url, generation_error_message, generation_started_at, generation_completed_at } = values;
                await tx.mutate.video_messages.insert({
                    id,
                    content,
                    role,
                    user_id,
                    company_id,
                    conversation_id,
                    video_project_id,
                    is_generating,
                    is_error,
                    video_generation_id,
                    video_generation_model,
                    video_generation_status,
                    video_generation_prompt,
                    generated_video_url,
                    generation_error_message,
                    generation_started_at,
                    generation_completed_at,
                    created_at: Date.now(),
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.video_messages.update({
                    id,
                    ...values,
                });
            },
            delete: async (tx, { id, }) => {
                await tx.mutate.video_messages.delete({
                    id,
                });
            },
        },
        video_generation_jobs: {
            insert: async (tx, { id, message_id, account_id, user_id, model, prompt, improved_prompt, external_operation_id, external_operation_name, status, error_message, generated_video_url, video_storage_path, generation_config, overlay_id, project_id, scene_id, started_at, completed_at }) => {
                const now = Date.now();
                await tx.mutate.video_generation_jobs.insert({
                    id,
                    message_id,
                    account_id,
                    user_id,
                    model,
                    prompt,
                    improved_prompt,
                    external_operation_id,
                    external_operation_name,
                    status: status || 'pending',
                    error_message,
                    generated_video_url,
                    video_storage_path,
                    generation_config: generation_config || {},
                    overlay_id,
                    project_id,
                    scene_id,
                    created_at: now,
                    updated_at: now,
                    started_at,
                    completed_at,
                });
            },
            update: async (tx, { id, values }) => {
                const now = Date.now();
                await tx.mutate.video_generation_jobs.update({
                    id,
                    ...values,
                    updated_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_generation_jobs.delete({
                    id,
                });
            },
        },
        // Video Editor Mutators
        video_projects: {
            insert: async (tx, { id, account_id, user_id, name, description, aspect_ratio, fps, width, height, status, is_template, template_category, template_tags, created_by, updated_by }) => {
                const now = Date.now();
                await tx.mutate.video_projects.insert({
                    id,
                    account_id,
                    user_id,
                    name,
                    description: description || '',
                    aspect_ratio: aspect_ratio || '16:9',
                    fps: fps || 30,
                    width: width || 1920,
                    height: height || 1080,
                    status: status || 'draft',
                    is_template: is_template || false,
                    template_category,
                    template_tags,
                    created_at: now,
                    updated_at: now,
                    created_by: created_by || user_id,
                    updated_by: updated_by || user_id,
                });
            },
            update: async (tx, { id, values }) => {
                const now = Date.now();
                await tx.mutate.video_projects.update({
                    id,
                    ...values,
                    updated_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_projects.delete({
                    id,
                });
            },
        },
        video_project_scenes: {
            insert: async (tx, { id, project_id, user_id, name, description, order_index, duration_frames, aspect_ratio }) => {
                const now = Date.now();
                console.log("inserting scene", id, project_id, user_id, name, description, order_index, duration_frames, aspect_ratio);
                await tx.mutate.video_project_scenes.insert({
                    id,
                    project_id,
                    user_id,
                    name,
                    description: description || '',
                    order_index: order_index || 1,
                    duration_frames,
                    aspect_ratio
                    // created_at: now,
                    // updated_at: now,
                });
            },
            update: async (tx, { id, values }) => {
                const now = Date.now();
                await tx.mutate.video_project_scenes.update({
                    id,
                    ...values,
                    updated_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_project_scenes.delete({
                    id,
                });
            },
        },
        video_project_scene_selections: {
            insert: async (tx, { id, project_id, user_id, selected_scene_id }) => {
                const now = Date.now();
                await tx.mutate.video_project_scene_selections.insert({
                    id,
                    project_id,
                    user_id,
                    selected_scene_id,
                    updated_at: now,
                });
            },
            upsert: async (tx, { id, project_id, user_id, selected_scene_id }) => {
                const now = Date.now();
                await tx.mutate.video_project_scene_selections.upsert({
                    id,
                    project_id,
                    user_id,
                    selected_scene_id,
                    updated_at: now,
                });
            },
            update: async (tx, { id, values }) => {
                const now = Date.now();
                await tx.mutate.video_project_scene_selections.update({
                    id,
                    ...values,
                    updated_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_project_scene_selections.delete({
                    id,
                });
            },
        },
        video_project_overlays: {
            insert: async (tx, { id, scene_id, overlay_id, overlay_type, layer_order, row_position, from_frame, duration_frames, position_x, position_y, width, height, rotation, content, src, styles, animation_config, metadata }) => {
                const now = Date.now();
                await tx.mutate.video_project_overlays.insert({
                    id,
                    scene_id,
                    overlay_id,
                    overlay_type,
                    layer_order: layer_order || 0,
                    row_position: row_position || 0,
                    from_frame: from_frame || 0,
                    duration_frames: duration_frames || 30,
                    position_x: position_x || 0,
                    position_y: position_y || 0,
                    width: width || 100,
                    height: height || 100,
                    rotation: rotation || 0,
                    content: content || '',
                    src: src || '',
                    styles: styles || {},
                    animation_config: animation_config || {},
                    metadata: metadata || {},
                    created_at: now,
                    updated_at: now,
                });
            },
            update: async (tx, { id, values }) => {
                const now = Date.now();
                await tx.mutate.video_project_overlays.update({
                    id,
                    ...values,
                    updated_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_project_overlays.delete({
                    id,
                });
            },
        },
        video_project_assets: {
            insert: async (tx, { id, project_id, account_id, asset_type, original_filename, file_path, file_size, mime_type, duration_seconds, thumbnail_path, metadata, is_external, external_source, external_id }) => {
                const now = Date.now();
                await tx.mutate.video_project_assets.insert({
                    id,
                    project_id,
                    account_id,
                    asset_type,
                    original_filename,
                    file_path,
                    file_size,
                    mime_type,
                    duration_seconds,
                    thumbnail_path,
                    metadata: metadata || {},
                    is_external: is_external || false,
                    external_source,
                    external_id,
                    created_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_project_assets.delete({
                    id,
                });
            },
        },
        video_project_autosaves: {
            insert: async (tx, { id, scene_id, project_id, user_id, editor_state, save_type }) => {
                const now = Date.now();
                await tx.mutate.video_project_autosaves.insert({
                    id,
                    scene_id,
                    project_id,
                    user_id,
                    editor_state,
                    save_type: save_type || 'auto',
                    created_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_project_autosaves.delete({
                    id,
                });
            },
        },
        video_renders: {
            insert: async (tx, { id, project_id, account_id, user_id, render_id, status, progress, output_url, output_size_bytes, error_message, render_settings, started_at, completed_at }) => {
                const now = Date.now();
                await tx.mutate.video_renders.insert({
                    id,
                    project_id,
                    account_id,
                    user_id,
                    render_id,
                    status: status || 'queued',
                    progress: progress || 0,
                    output_url,
                    output_size_bytes,
                    error_message,
                    render_settings: render_settings || {},
                    started_at,
                    completed_at,
                    created_at: now,
                });
            },
            update: async (tx, { id, values }) => {
                await tx.mutate.video_renders.update({
                    id,
                    ...values,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.video_renders.delete({
                    id,
                });
            },
        },
        // Assets mutators
        assets: {
            insert: async (tx, { id, account_id, file_name, file_path, file_size, file_type, folder_name, original_url, file_extension, transcript_path, transcript_url, has_transcript, transcript_extracted_at, processing_status, processing_started_at, processing_completed_at, processing_error }) => {
                const now = Date.now();
                console.log('Inserting asset record into Zero:', {
                    id,
                    account_id,
                    file_name,
                    file_path,
                    file_size,
                    file_type,
                    folder_name,
                    original_url,
                    file_extension,
                    transcript_path,
                    transcript_url,
                    has_transcript: has_transcript || false,
                    transcript_extracted_at,
                    processing_status: processing_status || 'completed',
                    processing_started_at,
                    processing_completed_at,
                    processing_error,
                    uploaded_at: now,
                    updated_at: now,
                });
                await tx.mutate.assets.insert({
                    id,
                    account_id,
                    file_name,
                    file_path,
                    file_size,
                    file_type,
                    folder_name,
                    original_url,
                    file_extension,
                    transcript_path,
                    transcript_url,
                    has_transcript: has_transcript || false,
                    transcript_extracted_at,
                    processing_status: processing_status || 'completed',
                    processing_started_at,
                    processing_completed_at,
                    processing_error,
                    // uploaded_at: now,
                    // updated_at: now,
                });
            },
            update: async (tx, { id, values }) => {
                const now = Date.now();
                await tx.mutate.assets.update({
                    id,
                    ...values,
                    updated_at: now,
                });
            },
            delete: async (tx, { id }) => {
                await tx.mutate.assets.delete({
                    id,
                });
            },
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
    };
}
//# sourceMappingURL=mutator.js.map