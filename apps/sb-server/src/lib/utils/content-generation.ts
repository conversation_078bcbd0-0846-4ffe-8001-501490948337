/**
 * Shared utilities for content generation (backend)
 */

import { 
  ContentGenerationParams, 
  PromptVariables, 
} from '../types/content-generation.js';

/**
 * Creates prompt variables from content generation parameters
 */
export function createPromptVariables(params: ContentGenerationParams): PromptVariables {
  return {
    channel: params.channel,
    content_type: params.contentType,
    task_title: params.taskTitle,
    task_description: params.taskDescription,
    personas_block: params.targetPersonas || "",
    icps_block: params.targetICPs || "",
    research_block: params.externalResearch || "",
    documents_block: params.productInformation || "",
    seo_keywords: "",
    trend_keywords: "",
    brand_guidelines: JSON.stringify(params.companyBrand) || "",
  };
}

/**
 * Formats personas data for prompt inclusion
 */
export function formatPersonasForPrompt(personasData: any[]): string {
  return personasData
    .map(persona => JSON.stringify(persona.data))
    .join('\n\n');
}

/**
 * Formats ICPs data for prompt inclusion
 */
export function formatICPsForPrompt(icpsData: any[]): string {
  return icpsData
    .map(icp => JSON.stringify(icp.data))
    .join('\n\n');
}

/**
 * Error handling utility for content generation
 */
export class ContentGenerationError extends Error {
  constructor(
    message: string,
    public taskId?: string,
    public taskTitle?: string,
    public originalError?: Error
  ) {
    super(message);
    this.name = 'ContentGenerationError';
  }
}

/**
 * Validates content generation parameters
 */
export function validateContentGenerationParams(params: ContentGenerationParams): void {
  if (!params.taskTitle?.trim()) {
    throw new ContentGenerationError('Task title is required');
  }
  if (!params.taskDescription?.trim()) {
    throw new ContentGenerationError('Task description is required');
  }
  if (!params.contentType?.trim()) {
    throw new ContentGenerationError('Content type is required');
  }
  if (!params.channel?.trim()) {
    throw new ContentGenerationError('Channel is required');
  }
}
