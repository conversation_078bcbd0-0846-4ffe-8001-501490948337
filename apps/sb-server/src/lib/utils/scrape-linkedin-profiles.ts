import postgres from "postgres";

/**
 * Scrapes LinkedIn profile data using the Proxycurl API
 * @param linkedinUrl - The LinkedIn profile URL to scrape
 * @returns Promise containing the scraped profile data
 */
export async function scrapeLinkedInProfile(linkedinUri: string, sql: any, isUrl : boolean = false, passedLinkedinUrl : string = '') {

  console.log('scrapeLinkedInProfile', linkedinUri, isUrl, passedLinkedinUrl);
  // before we can call the nubela api, we need to get the linkedin profile url
  // we receive a format urn:li:person:6rMflAKt5m and need to call the ayrshare api to get the url

  try {
    let linkedinUrl = '';
    if (!isUrl) {
      //first check if the linkedInURI exists in the database, linked_in_URIS table 
    
      const linkedinUriResult = await sql`
        SELECT * FROM linkedin_uris WHERE uri = ${linkedinUri}
      `;
      if (linkedinUriResult.length > 0) {
        console.log('linkedinUriResultFOUND', linkedinUriResult);
        return linkedinUriResult[0].profile;
      } else {
        // we need to call the ayrshare api to get the url
        const API_KEY = process.env.AYRSHARE_API_KEY;
        const profileKey = process.env.AYRSHARE_PROFILE_KEY;
        console.log('API_KEY', API_KEY);
        const response = await fetch(
          `https://api.ayrshare.com/api/brand/byUser?platforms[0]=linkedin&linkedinUser=${linkedinUri}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${API_KEY}`,
              'Profile-Key': profileKey,
            }
          }
        )
        const data = await response.json();

        if (data?.linkedin?.from?.id === 'private') {
          return null;
        }
        console.log('🚀 data', data, profileKey);
        linkedinUrl = data.linkedin.from.url;
      }
    } else if (isUrl) {
      linkedinUrl = passedLinkedinUrl;
    }

    console.log('linkedinUrl', linkedinUrl);

    const apiUrl = 'https://enrichlayer.com/api/v2/profile';
    // if linkedin.from.id == private, we should return and not attempt to scrape the profile
   
    // Build query parameters
    const params = new URLSearchParams({
      url: linkedinUrl,
      fallback_to_cache: 'on-error',
      use_cache: 'if-present',
      skills: 'include'
    });
  
    try {
      const response = await fetch(`${apiUrl}?${params}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': process.env.ENRICHLAYER_API_KEY
        }
      });
  
      if (!response.ok) {
        // Handle 404 gracefully - profile not found
        if (response.status === 404) {
          console.log(`LinkedIn profile not found (404): ${linkedinUrl}`);
          return null;
        }
        // For other errors, log and return null instead of throwing
        console.error(`Failed to scrape LinkedIn profile: ${response.status} ${response.statusText} for URL: ${linkedinUrl}`);
        return null;
      }
  
      const data = await response.json();
      console.log('data', data);
      // save the data to the linked_in_URIS table
      await sql`
        INSERT INTO 
          linkedin_uris (uri, profile, url) 
        VALUES 
        (
          ${linkedinUri ? linkedinUri : ''},
          ${sql.json(data)},
          ${linkedinUrl}
        )
      `;
      return data;
    } catch (error) {
      console.error('Error scraping LinkedIn profile:', error, 'for URL:', linkedinUrl);
      return null;
    }
  }
 catch (error) {
  console.error('Error scraping LinkedIn profile (outer catch):', error);
  return null;
}
}

/**
 * Type definition for LinkedIn profile data structure
 * This is a basic structure - you may need to extend it based on actual API response
 */
export interface LinkedInProfile {
  public_identifier?: string;
  profile_pic_url?: string;
  background_cover_image_url?: string;
  first_name?: string;
  last_name?: string;
  full_name?: string;
  follower_count?: number;
  occupation?: string;
  headline?: string;
  summary?: string;
  country?: string;
  country_full_name?: string;
  city?: string;
  state?: string;
  experiences?: Experience[];
  education?: Education[];
  languages?: string[];
  accomplishment_organisations?: any[];
  accomplishment_publications?: any[];
  accomplishment_honors_awards?: any[];
  accomplishment_patents?: any[];
  accomplishment_courses?: any[];
  accomplishment_projects?: any[];
  accomplishment_test_scores?: any[];
  volunteer_work?: any[];
  certifications?: any[];
  connections?: number;
  people_also_viewed?: any[];
  recommendations?: string[];
  activities?: any[];
  similarly_named_profiles?: any[];
  articles?: any[];
  groups?: any[];
  skills?: string[];
  inferred_salary?: {
    min?: number;
    max?: number;
  };
  gender?: string;
  birth_date?: any;
  industry?: string;
  extra?: any;
  interests?: string[];
  personal_emails?: string[];
  personal_numbers?: string[];
}

interface Experience {
  starts_at?: {
    day?: number;
    month?: number;
    year?: number;
  };
  ends_at?: {
    day?: number;
    month?: number;
    year?: number;
  };
  company?: string;
  company_linkedin_profile_url?: string;
  company_facebook_profile_url?: string;
  title?: string;
  description?: string;
  location?: string;
  logo_url?: string;
}

interface Education {
  starts_at?: {
    day?: number;
    month?: number;
    year?: number;
  };
  ends_at?: {
    day?: number;
    month?: number;
    year?: number;
  };
  field_of_study?: string;
  degree_name?: string;
  school?: string;
  school_linkedin_profile_url?: string;
  school_facebook_profile_url?: string;
  description?: string;
  logo_url?: string;
  grade?: string;
  activities_and_societies?: string;
}
