import { Agent, tool } from '@openai/agents';
import { z } from 'zod';
import { editImages } from '../services/edit-images.js';
import dotenv from 'dotenv';

dotenv.config();

interface ImageEditingContext {
  userId: string;
  companyId: string;
  conversationId: string;
  originalImageUrl: string;
  referenceImages?: string[];
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at: number;
  }>;
}

// Create the image editing tool
const editImageTool = tool({
  name: 'edit_image',
  description: 'Edit an existing image based on a detailed edit prompt. Call this when you have a complete, specific edit instruction.',
  parameters: z.object({
    edit_prompt: z.string().describe('The detailed image editing instruction'),
    style_notes: z.string().nullable().describe('Additional style or quality notes for the edit')
  }),
  async execute({ edit_prompt, style_notes }, runContext) {
    try {
      console.log('Editing image with prompt:', edit_prompt);
      
      // Get context from the run context
      const context = runContext?.context as ImageEditingContext;
      if (!context) {
        throw new Error('Context not available');
      }

      if (!context.originalImageUrl) {
        throw new Error('Original image URL not available');
      }

      // Edit the image using the existing service
      const result = await editImages({
        edit_prompt,
        original_image_url: context.originalImageUrl,
        company_id: context.companyId,
        brand_context: style_notes || undefined,
        reference_images: context.referenceImages,
      });

      console.log('Image edited successfully:', result.url);

      return `${edit_prompt.charAt(0).toUpperCase() + edit_prompt.slice(1)} - completed successfully! Here's your edited image: ${result.url}

You can continue to make further edits or ask for additional modifications to perfect your image.`;
    } catch (error) {
      console.error('Error editing image:', error);
      return `I apologize, but I encountered an error while editing the image: ${error instanceof Error ? error.message : 'Unknown error'}. Please try rephrasing your edit request or let me know if you'd like to try a different approach.`;
    }
  },
});

export async function createImageEditingAgent(context: ImageEditingContext) {
  const systemInstructions = `You are an AI assistant specialized in image editing for an Image Studio application. You help users edit existing images with precise, detailed instructions.

IMPORTANT: There is already an original image loaded that you will be editing. The image is available at: ${context.originalImageUrl}

You should assume the user wants to edit THIS existing image unless they explicitly mention uploading a different one.

Your workflow:
1. The user has an existing image that needs to be edited
2. If the user's edit request is vague or unclear, ask for clarification to create a specific edit instruction
3. Once you have a clear, specific edit instruction, use the edit_image tool to modify the existing image
4. Always call the edit_image tool when you have a complete edit instruction ready - don't ask the user to provide an image

Guidelines for good edit prompts:
- Be specific about what elements to add, remove, or modify in the EXISTING image
- Include details about placement, size, style, and integration
- Specify how changes should blend with existing elements
- Mention lighting, shadows, and perspective considerations
- Use descriptive language for colors, textures, and materials

Types of edits you can help with:
- Adding or removing objects/elements to/from the existing image
- Changing colors, styles, or materials in the existing image
- Modifying lighting or atmosphere of the existing image
- Style transfer and artistic effects on the existing image
- Inpainting (replacing specific areas) in the existing image
- Object replacement or transformation in the existing image

Context:
- User ID: ${context.userId}
- Company ID: ${context.companyId}
- Conversation ID: ${context.conversationId}
- Original Image URL: ${context.originalImageUrl}
${context.referenceImages && context.referenceImages.length > 0 ? `- Reference Images: ${context.referenceImages.join(', ')}` : ''}

Previous conversation:
${context.messages.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Remember: You are editing an EXISTING image. When you have enough detail about the desired edit, use the edit_image tool to create the modified version of the existing image. Don't ask the user to provide an image - it's already available for editing.`;

  const agent = new Agent({
    name: 'Image Editing Assistant',
    instructions: systemInstructions,
    tools: [editImageTool],
    modelSettings: {
      temperature: 0.7,
      maxTokens: 2000,
    },
  });

  return agent;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface EditChatParams {
  messages: ChatMessage[];
  context: ImageEditingContext;
}

export async function streamImageEditingChat(params: EditChatParams) {
  const { messages, context } = params;
  
  try {
    const agent = await createImageEditingAgent(context);
    
    // Get the latest user message
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage || latestMessage.role !== 'user') {
      throw new Error('Latest message must be from user');
    }

    return {
      agent,
      userMessage: latestMessage.content,
    };
  } catch (error) {
    console.error('Error creating image editing agent:', error);
    throw new Error('Failed to create image editing agent. Please try again.');
  }
}
