import { Agent, tool } from '@openai/agents';
import { z } from 'zod';
import { generateImages } from '../services/generate-images.js';
import dotenv from 'dotenv';

dotenv.config();

interface ImageConversationContext {
  userId: string;
  companyId: string;
  conversationId: string;
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at: number;
  }>;
}

// Create the image generation tool
const generateImageTool = tool({
  name: 'generate_image',
  description: 'Generate an image based on a detailed prompt. Call this when you have a complete, detailed prompt ready.',
  parameters: z.object({
    image_prompt: z.string().describe('The detailed image generation prompt'),
    aspect_ratio: z.string().default('16:9').describe('Image aspect ratio (e.g., 16:9, 1:1, 9:16)'),
    style_notes: z.string().nullable().describe('Additional style or quality notes')
  }),
  async execute({ image_prompt, aspect_ratio, style_notes }, runContext) {
    try {
      console.log('Generating image with prompt:', image_prompt);
      
      // Get context from the run context
      const context = runContext?.context as ImageConversationContext;
      if (!context) {
        throw new Error('Context not available');
      }

      // Generate the image using the existing service
      const result = await generateImages({
        image_prompt,
        aspect_ratio: aspect_ratio || '16:9',
        company_id: context.companyId,
        brand_context: style_notes || undefined,
      });

      console.log('Image generated successfully:', result.url);

      return `Image generated successfully! Here's your image: ${result.url}

The image has been created based on your prompt: "${image_prompt}"

You can now see the generated image, and feel free to ask for modifications or generate additional images with different prompts.`;
    } catch (error) {
      console.error('Error generating image:', error);
      return `I apologize, but I encountered an error while generating the image: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again or let me know if you'd like to adjust the prompt.`;
    }
  },
});

export async function createImageConversationAgent(context: ImageConversationContext) {
  const systemInstructions = `You are an AI assistant for an Image Studio application. You help users create detailed prompts for high-quality image generation.

Your workflow:
1. If the user's request is too short or not detailed enough, ask for clarification to help create a detailed prompt. 
2. Once you have enough information to create a detailed, specific prompt, use the generate_image tool to create the image
3. Always call the generate_image tool when you have a complete prompt ready - don't just provide the prompt text

Guidelines for good prompts:
- Be specific about subjects, style, lighting, composition, and mood
- Include details about colors, textures, and atmosphere
- Specify the setting and background
- Mention artistic style if relevant (photorealistic, cartoon, oil painting, etc.)

Context:
- User ID: ${context.userId}
- Company ID: ${context.companyId}
- Conversation ID: ${context.conversationId}

Previous conversation:
${context.messages.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Remember: When you have enough information, use the generate_image tool to create the image, don't just describe what the prompt should be.`;

  const agent = new Agent({
    name: 'Image Studio Assistant',
    instructions: systemInstructions,
    tools: [generateImageTool],
  });

  return agent;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface StreamChatParams {
  messages: ChatMessage[];
  context: ImageConversationContext;
}

export async function streamImageConversationChat(params: StreamChatParams) {
  const { messages, context } = params;
  
  try {
    const agent = await createImageConversationAgent(context);
    
    // Convert messages to the format expected by the agent
    const conversationHistory = messages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
    }));

    // Get the latest user message
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage || latestMessage.role !== 'user') {
      throw new Error('Latest message must be from user');
    }

    return {
      agent,
      userMessage: latestMessage.content,
      conversationHistory,
    };
  } catch (error) {
    console.error('Error creating image conversation agent:', error);
    throw new Error('Failed to create image conversation agent. Please try again.');
  }
}
