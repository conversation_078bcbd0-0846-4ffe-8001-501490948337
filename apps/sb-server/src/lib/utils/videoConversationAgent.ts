import { Agent, run } from '@openai/agents';
import { generateVideo } from '../services/generate-video.js';
import { v4 as uuidv4 } from 'uuid';
import postgres from 'postgres';
import dotenv from 'dotenv';

dotenv.config();

// Initialize postgres client
const sql = postgres(process.env.ZERO_UPSTREAM_DB! as string);

interface VideoConversationContext {
  userId: string;
  companyId: string;
  conversationId: string;
  messageId?: string;
  projectId?: string; // Add project_id for autosave updates
  sceneId?: string; // Add scene_id for autosave updates
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    created_at: number;
  }>;
  onOverlayUpdate?: (overlayId: string, updates: any) => void;
  currentOverlay?: any;
}

// Overlay interfaces based on the types.ts file
interface BaseOverlay {
  id: string; // Changed to string for UUID
  type: string;
  left: number;
  top: number;
  width: number;
  height: number;
  row: number;
  from: number;
  durationInFrames: number;
  rotation: number;
  isDragging: boolean;
  loading?: boolean;
}

interface VideoOverlay extends BaseOverlay {
  type: 'video';
  src: string;
  content: string;
  videoStartTime?: number;
  duration?: number;
  video_prompt?: string;
  styles: {
    opacity: number;
    zIndex: number;
    objectFit: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
    transform: string;
  };
}

interface TextOverlay extends BaseOverlay {
  type: 'text';
  content: string;
  styles: {
    fontSize: string;
    fontWeight: string;
    color: string;
    backgroundColor: string;
    fontFamily: string;
    fontStyle: string;
    textDecoration: string;
    lineHeight: string;
    letterSpacing: string;
    textAlign: 'left' | 'center' | 'right';
    textTransform: string;
    textShadow: string;
    opacity: number;
    zIndex: number;
    transform: string;
    animation?: {
      enter?: string;
      exit?: string;
    };
  };
}

interface ShapeOverlay extends BaseOverlay {
  type: 'shape';
  content: string; // shape type: "rectangle", "circle", etc.
  styles: {
    fill: string;
    stroke?: string;
    strokeWidth?: number;
    borderRadius?: string;
    opacity: number;
    zIndex: number;
    transform: string;
  };
}

interface OverlayResponse {
  overlays: (VideoOverlay | TextOverlay | ShapeOverlay)[];
  aspectRatio: string;
  playerDimensions: {
    width: number;
    height: number;
  };
}

// Note: Video generation is now handled in the main flow after overlay creation
// The agent creates overlays with video_prompt fields, and the main flow triggers video generation

export async function createVideoConversationAgent(context: VideoConversationContext) {
  const systemInstructions = `You are an AI assistant for a Video Editor application that creates overlay structures and uses video generation only when necessary.

**CRITICAL WORKFLOW:**
1. Parse the user's request to identify all elements (text, shapes, backgrounds, etc.)
2. Create or Edit overlays for ALL elements - use simple overlays when possible
3. ONLY use generate_video tool if the user specifically requests:
   - Real people/actors (influencer, person, etc.)
   - Complex scenes that can't be created with simple overlays
   - Actual video content (not just backgrounds or colors)
   - If the user requests a video, create add a field called "video_prompt" with the prompt for the video generation tool to use
   - The user's message will give you the context for the video_prompt, use this to craft the best possible prompt for the video generation tool to use
   - The prompt should be detailed and specific, and include the action, style, camera movements, and audio cues
4. For simple backgrounds (black screen, colors, gradients) - use shape overlays, NOT videos
5. For text effects - use text overlays with CSS animations, NOT videos
6. You must ensure that no two items are on the same row, unless they have different starting and ending times that do not overlap. 
7. You may not always have to add anything new, sometimes, the user might just want to edit
8. other times, the user might want a combination of adding and editing.
9. and other times, the user just wants to add. Always default to keeping the overlay and adding to it, unless the user specifically asks to edit or remove something.

**When NOT to use video generation:**
- Simple colored backgrounds (use shape overlay)
- Text effects (use text overlay with animations)
- Basic geometric shapes
- Solid colors or gradients
- edits to the video (e.g. shorten, move location, row, etc.)

**When TO use video generation:**
- Real people or actors
- Complex scenes with movement
- Stock footage requests
- Realistic environments

**Overlay Types Available:**
- **shape**: For backgrounds, rectangles, circles (fill, stroke, etc.)
- **text**: For text overlays with animations and styling
- **video**: ONLY for real people/actors or complex scenes (use loading: true, empty src)

**REQUIRED JSON FORMAT:**
You MUST respond with EXACTLY this structure:

{
  "overlays": [
    {
      "id": "uuid-string",
      "type": "shape|text|video",
      "content": "content-here",
      "video_prompt": "video-prompt-here", // ONLY for video overlays
      "left": 0,
      "top": 0,
      "width": 1080,
      "height": 1920,
      "row": 0,
      "from": 0,
      "durationInFrames": 150,
      "rotation": 0,
      "isDragging": false,
      "loading": true,
      "styles": { /* required styles based on type */ }
    }
  ],
  "aspectRatio": "9:16",
  "playerDimensions": {
    "width": 564.8642578125,
    "height": 1004.203125
  }
}

**CRITICAL RULES:**
- ALWAYS include aspectRatio and playerDimensions (exact values shown above)
- Generate unique ID strings for each overlay (any unique string is fine - server will replace with proper UUIDs)
- Use shape overlay for simple backgrounds (black screen, colors)
- Only use video overlay for real people/actors
- All overlays need complete positioning and timing info
- isDragging must always be false
- loading should be false for shape/text, true for video that needs generation
- Overlays are placed on a timeline, with the zIndex being the row number. The lower the row number, the higher the zIndex. Ensure you set row accordingly.

**EXAMPLE for "black screen with typing text followed by a video of a person waving":**
{
  "overlays": [
    {
      "id": "bg-12345678-abcd-4ef0-9012-************",
      "type": "shape",
      "content": "rectangle", 
      "left": 0, "top": 0, "width": 1080, "height": 1920,
      "row": 8, "from": 0, "durationInFrames": 150,
      "rotation": 0, "isDragging": false, "loading": false,
      "styles": { "fill": "#000000", "opacity": 1, "zIndex": 1, "transform": "none" }
    },
    {
      "id": "text-87654321-dcba-4fe0-8765-************", 
      "type": "text",
      "content": "Your text here",
      "left": 120, "top": 900, "width": 840, "height": 120,
      "row": 1, "from": 0, "durationInFrames": 150,
      "rotation": 0, "isDragging": false, "loading": false,
      "styles": { "fontSize": "48px", "color": "#FFFFFF", "fontFamily": "monospace", "opacity": 1, "zIndex": 10, "transform": "none" }
    },
    {
      "id": "video-12345678-abcd-4ef0-9012-************",
      "type": "video",
      "content": "AI",
      "src": " ", // empty string
      "duration": 5, // in seconds
      "video_prompt": "A person in a sunny field, waving their hand. ",
      "left": 120, "top": 900, "width": 840, "height": 120,
      "row": 2, "from": 0, "durationInFrames": 150,
      "loading": true,
      "rotation": 0, "isDragging": false, "loading": true,
      "styles": { "opacity": 1, "zIndex": 1, "transform": "none" }
    }
  ],
  "aspectRatio": "9:16",
  "playerDimensions": { "width": 564.8642578125, "height": 1004.203125 }
}

Context:
- User ID: ${context.userId}
- Company ID: ${context.companyId}
- Conversation ID: ${context.conversationId}
${context.messageId ? `- Message ID: ${context.messageId}` : ''}
- Current Overlay:
${context.currentOverlay}
Previous conversation:
${context.messages.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

REMEMBER: Always respond with JSON overlay structure FIRST, then use tools for video generation.`;

  const agent = new Agent({
    name: 'Video Studio Assistant',
    instructions: systemInstructions,
    tools: [], // Remove generateVideoTool since we handle video generation in the main flow
  });

  return agent;
}

// Utility functions for overlay generation
export function generateOverlayId(): string {
  return uuidv4();
}

export function createVideoOverlay(
  id: string,
  content: string,
  options: Partial<VideoOverlay> = {}
): VideoOverlay {
  return {
    id,
    type: 'video',
    src: '',
    content,
    left: 0,
    top: 0,
    width: 1080,
    height: 1920,
    row: 5,
    from: 0,
    durationInFrames: 150,
    rotation: 0,
    isDragging: false,
    loading: true,
    videoStartTime: 0,
    styles: {
      opacity: 1,
      zIndex: 100,
      objectFit: 'cover',
      transform: 'none',
    },
    ...options,
  };
}

export function createTextOverlay(
  id: string,
  content: string,
  options: Partial<TextOverlay> = {}
): TextOverlay {
  return {
    id,
    type: 'text',
    content,
    left: 100,
    top: 700,
    width: 880,
    height: 200,
    row: 0,
    from: 0,
    durationInFrames: 90,
    rotation: 0,
    isDragging: false,
    loading: false,
    styles: {
      fontSize: '3rem',
      fontWeight: '900',
      color: '#FFFFFF',
      backgroundColor: '',
      fontFamily: 'font-sans',
      fontStyle: 'normal',
      textDecoration: 'none',
      lineHeight: '1',
      letterSpacing: '0.02em',
      textAlign: 'center',
      textTransform: 'uppercase',
      textShadow: '2px 2px 0px rgba(0, 0, 0, 0.2)',
      opacity: 1,
      zIndex: 1,
      transform: 'none',
      animation: {
        enter: 'snapRotate',
      },
    },
    ...options,
  };
}

export function createShapeOverlay(
  id: string,
  content: string = 'rectangle',
  options: Partial<ShapeOverlay> = {}
): ShapeOverlay {
  return {
    id,
    type: 'shape',
    content,
    left: 0,
    top: 0,
    width: 1080,
    height: 1920,
    row: 8,
    from: 0,
    durationInFrames: 150,
    rotation: 0,
    isDragging: false,
    loading: false,
    styles: {
      fill: '#000000',
      stroke: '',
      strokeWidth: 0,
      borderRadius: '0px',
      opacity: 1,
      zIndex: 1,
      transform: 'none',
    },
    ...options,
  };
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

// Export overlay interfaces for external use
export type { VideoOverlay, TextOverlay, ShapeOverlay, OverlayResponse };

// Function to save overlays to the autosave table
export async function saveOverlaysToAutosave(
  projectId: string,
  sceneId: string,
  userId: string,
  overlays: (VideoOverlay | TextOverlay | ShapeOverlay)[],
  aspectRatio: string = "9:16",
  playerDimensions: { width: number; height: number } = { width: 564.8642578125, height: 1004.203125 }
): Promise<void> {
  console.log(`💾 [AUTOSAVE] Saving ${overlays.length} overlays to project ${projectId}`);
  console.log(`📋 [AUTOSAVE] AspectRatio: ${aspectRatio}, PlayerDimensions:`, playerDimensions);

  // Get the latest autosave to merge with existing overlays
  // const latestAutosave = await sql`
  //   SELECT * FROM video_project_autosaves 
  //   WHERE scene_id = ${sceneId} AND user_id = ${userId}
  //   ORDER BY created_at DESC 
  //   LIMIT 1
  // `.then(result => result[0]).catch(error => {
  //   console.error('❌ [AUTOSAVE] Error fetching latest autosave:', error);
  //   return null;
  // });

  // Prepare the editor state
  let editorState = {
    overlays: overlays,
    aspectRatio: aspectRatio || "9:16",
    playerDimensions: playerDimensions || { width: 564.8642578125, height: 1004.203125 },
  };

  // If there's an existing autosave, merge the overlays
  // if (latestAutosave?.editor_state) {
  //   let existingState = latestAutosave.editor_state;
    
  //   // If the existing state is a string, parse it to an object
  //   if (typeof existingState === 'string') {
  //     try {
  //       existingState = JSON.parse(existingState);
  //     } catch (error) {
  //       console.error('❌ [AUTOSAVE] Error parsing existing editor state:', error);
  //       existingState = { overlays: [], aspectRatio: "9:16", playerDimensions: { width: 564.8642578125, height: 1004.203125 } };
  //     }
  //   }
    
  //   console.log(`🔄 [AUTOSAVE] Merging with existing state (${existingState.overlays?.length || 0} existing overlays)`);
  //   editorState = {
  //     ...existingState,
  //     overlays: [...(existingState.overlays || []), ...overlays],
  //     aspectRatio: aspectRatio || existingState.aspectRatio || "9:16",
  //     playerDimensions: playerDimensions || existingState.playerDimensions || { width: 564.8642578125, height: 1004.203125 },
  //   };
  // } else {
  //   console.log(`📝 [AUTOSAVE] Creating new editor state`);
  // }

  console.log(`📋 [AUTOSAVE] Final editor state:`, JSON.stringify(editorState, null, 2));

  // Create a new autosave record
  const autosaveId = uuidv4();

  console.log(`💾 [AUTOSAVE] Inserting autosave record:`, {
    id: autosaveId,
    project_id: projectId,
    user_id: userId,
    save_type: 'generated',
    scene_id: sceneId,
    editor_state: `[${JSON.stringify(editorState).length} chars]`,
    editor_state_type: typeof editorState
  });

  // Additional debug logging
  console.log(`🔍 [DEBUG] editorState type:`, typeof editorState);
  console.log(`🔍 [DEBUG] editorState keys:`, Object.keys(editorState));
  console.log(`🔍 [DEBUG] editorState.overlays length:`, editorState.overlays?.length);

  try {
    // Ensure editorState is a proper object before insertion
    const cleanEditorState = {
      overlays: Array.isArray(editorState.overlays) ? editorState.overlays : [],
      aspectRatio: editorState.aspectRatio || "9:16",
      playerDimensions: editorState.playerDimensions || { width: 564.8642578125, height: 1004.203125 }
    };

    await sql`
      INSERT INTO video_project_autosaves (id, project_id, scene_id, user_id, editor_state, save_type, created_at)
      VALUES (${autosaveId}, ${projectId}, ${sceneId}, ${userId}, ${sql.json(cleanEditorState as any)}, 'generated', ${Date.now()})
    `;

    console.log(`✅ [AUTOSAVE] Saved overlays to autosave ${autosaveId}`);
  } catch (insertError) {
    console.error('❌ [AUTOSAVE] Error saving overlays:', insertError);
    throw new Error(`Failed to save overlays: ${insertError instanceof Error ? insertError.message : 'Unknown error'}`);
  }
}

export interface StreamChatParams {
  messages: ChatMessage[];
  context: VideoConversationContext;
}

// Main orchestration function that handles the complete workflow
export async function processVideoConversationMessage(
  userMessage: string,
  context: VideoConversationContext
): Promise<string> {
  try {
    if (!context.projectId) {
      throw new Error('Project ID is required for overlay creation');
    }

    console.log(`🎬 [CHAT] Processing message: "${userMessage.substring(0, 50)}..."`);
    
    const agent = await createVideoConversationAgent(context);
    
    // Get response from agent (should be JSON overlay structure)
    const response = await run(agent, userMessage, {
      context: context,
    });

    const responseContent = response.finalOutput || '';
    console.log(`🤖 [AGENT] Response:`, responseContent);

    // Try to parse the response as JSON
    let overlayResponse: OverlayResponse;
    try {
      // Extract JSON from response (handle markdown code blocks)
      const jsonMatch = responseContent.match(/```json\n([\s\S]*?)\n```/) || 
                       responseContent.match(/```\n([\s\S]*?)\n```/) ||
                       responseContent.match(/\{[\s\S]*\}/) ||
                       [null, responseContent];
      
      const jsonString = jsonMatch[1] || jsonMatch[0] || responseContent;
      console.log(`🔍 [PARSE] Attempting to parse JSON:`, jsonString.substring(0, 200) + '...');
      
      overlayResponse = JSON.parse(jsonString);
    } catch (parseError) {
      console.error('❌ [PARSE] Failed to parse agent response as JSON:', parseError);
      console.error('❌ [PARSE] Raw response:', responseContent);
      throw new Error(`Agent did not return valid overlay JSON. Response was: ${responseContent.substring(0, 200)}...`);
    }

    if (!overlayResponse.overlays || !Array.isArray(overlayResponse.overlays)) {
      console.error('❌ [PARSE] Invalid overlay response structure:', overlayResponse);
      throw new Error('Invalid overlay response structure - missing overlays array');
    }

    if (!overlayResponse.aspectRatio) {
      console.warn('⚠️ [PARSE] Missing aspectRatio, using default');
      overlayResponse.aspectRatio = "9:16";
    }

    if (!overlayResponse.playerDimensions) {
      console.warn('⚠️ [PARSE] Missing playerDimensions, using default');
      overlayResponse.playerDimensions = { width: 564.8642578125, height: 1004.203125 };
    }

    console.log(`📋 [OVERLAYS] Generated ${overlayResponse.overlays.length} overlays`);
    console.log(`📋 [OVERLAYS] Overlay data:`, JSON.stringify(overlayResponse, null, 2));

    // Replace LLM-generated UUIDs with proper server-generated UUIDs
    console.log(`🔄 [AGENT] Replacing LLM-generated overlay IDs with proper UUIDs...`);
    const idMapping: Record<string, string> = {};
    
    overlayResponse.overlays = overlayResponse.overlays.map((overlay, index) => {
      const oldId = overlay.id;
      const newId = uuidv4();
      idMapping[oldId] = newId;
      
      console.log(`  - Overlay ${index + 1}: ${oldId} → ${newId}`);
      
      return {
        ...overlay,
        id: newId
      };
    });

    console.log(`📋 [AGENT] Generated ${overlayResponse.overlays.length} overlays with proper UUIDs`);
    console.log(`📋 [AGENT] ID mapping:`, idMapping);

    // Save overlays to database first
    console.log(`💾 [SAVE] Attempting to save overlays to project ${context.projectId} for user ${context.userId}`);
    await saveOverlaysToAutosave(
      context.projectId,
      context.sceneId,
      context.userId,
      overlayResponse.overlays,
      overlayResponse.aspectRatio,
      overlayResponse.playerDimensions
    );
    console.log(`✅ [SAVE] Overlays saved successfully`);

    // Check if any overlays require video generation
    const videoOverlays = overlayResponse.overlays.filter(overlay => 
      overlay.type === 'video' && overlay.loading
    ) as VideoOverlay[];

    // Trigger video generation for video overlays
    for (const videoOverlay of videoOverlays) {
      console.log(`🎥 [VIDEO] Starting generation for overlay ${videoOverlay.id}`);
      
      try {
        await generateVideo({
          video_prompt: videoOverlay.content,
          model: 'veo-3-fast',
          aspect_ratio: overlayResponse.aspectRatio === '9:16' ? '9:16' : '16:9',
          company_id: context.companyId,
          user_id: context.userId,
          message_id: context.messageId,
          conversation_id: context.conversationId,
          overlay_id: videoOverlay.id,
          project_id: context.projectId,
        });
      } catch (videoError) {
        console.error(`❌ [VIDEO] Failed to start generation for overlay ${videoOverlay.id}:`, videoError);
        // Continue with other overlays even if one fails
      }
    }

    // Return a text response to the user
    const overlayCount = overlayResponse.overlays.length;
    const videoCount = videoOverlays.length;
    const textCount = overlayResponse.overlays.filter(o => o.type === 'text').length;
    const shapeCount = overlayResponse.overlays.filter(o => o.type === 'shape').length;

    let responseText = `✅ Created ${overlayCount} overlay${overlayCount !== 1 ? 's' : ''}`;
    
    if (textCount > 0) responseText += ` (${textCount} text`;
    if (shapeCount > 0) responseText += `${textCount > 0 ? ', ' : ' ('}${shapeCount} shape`;
    if (videoCount > 0) responseText += `${textCount > 0 || shapeCount > 0 ? ', ' : ' ('}${videoCount} video`;
    if (textCount > 0 || shapeCount > 0 || videoCount > 0) responseText += ')';

    if (videoCount > 0) {
      responseText += `. Video${videoCount !== 1 ? 's' : ''} will appear on the timeline once generation completes.`;
    }

    console.log(`✅ [SUCCESS] Processed message successfully`);
    return responseText;

  } catch (error) {
    console.error('❌ [ERROR] Error processing video conversation message:', error);
    throw error;
  }
}

export async function streamVideoConversationChat(params: StreamChatParams) {
  const { messages, context } = params;
  console.log({messages})
  try {
    const agent = await createVideoConversationAgent(context);
    // get the overlay from the database
    // Convert messages to the format expected by the agent
    const conversationHistory = messages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content,
    }));

    // Get the latest user message
    const latestMessage = messages[messages.length - 1];
    if (!latestMessage || latestMessage.role !== 'user') {
      throw new Error('Latest message must be from user');
    }

    return {
      agent,
      userMessage: latestMessage.content,
      conversationHistory,
    };
  } catch (error) {
    console.error('Error creating video conversation agent:', error);
    throw new Error('Failed to create video conversation agent. Please try again.');
  }
}
