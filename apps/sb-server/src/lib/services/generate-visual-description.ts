import dotenv from "dotenv";
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from "../utils/callLLM.js";

dotenv.config();

// Types for visual description generation
interface VisualDescriptionOption {
  level: string;
  prompt: string;
}

interface EnhancedPromptsResponse {
  enhanced_prompts: VisualDescriptionOption[];
}

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL,
});

export const generateVisualDescription = async (
  brandBrief: string,
  content: string
): Promise<VisualDescriptionOption[]> => {
  try {
    // Step 1: Generate initial visual description
    const step1Prompt = await langfuse.getPrompt(
      "generate_visual_description_step1",
      undefined,
      { label: "production" }
    );

    const step1Messages = step1Prompt.compile({
      brand_brief: brandBrief,
      content: content,
    });

    const step1Response = await callLLM(
      "",
      step1Prompt.config,
      LLM_MODEL_ID,
      { parse: false },
      step1Messages
    );

    if (!step1Response) {
      throw new Error("Step 1 LLM response is null or undefined");
    }

    // Step 2: Generate enhanced prompts with different brand integration levels
    const step2Prompt = await langfuse.getPrompt(
      "generate_visual_description_step2",
      undefined,
      { label: "production" }
    );

    const step2Messages = step2Prompt.compile({
      brand_brief: brandBrief,
      image_gen_prompt: step1Response,
    });

    const step2Response = await callLLM(
      "",
      step2Prompt.config,
      LLM_MODEL_ID,
      { parse: true },
      step2Messages
    ) as EnhancedPromptsResponse;

    if (!step2Response?.enhanced_prompts) {
      throw new Error("Step 2 response does not contain enhanced_prompts property");
    }

    return step2Response.enhanced_prompts;
  } catch (error) {
    console.error("Error generating visual description:", error);
    throw new Error(`Failed to generate visual description: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
