import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});


export async function generateEngagementReport(engaged_user_profiles: any[], icp_data: string, product_documents: string): Promise<any> {
  try {
    // Check if we have valid profiles
    if (!engaged_user_profiles || engaged_user_profiles.length === 0) {
      console.log("No engaged user profiles available for engagement report");
      return {
        error: "No engaged user profiles available",
        message: "Cannot generate engagement report without user profiles"
      };
    }

    console.log(`Generating engagement report for ${engaged_user_profiles.length} profiles`);

    // Get production prompt 
    const prompt = await langfuse.getPrompt("generate_engagement_report", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
        linkedIn_Profiles: JSON.stringify(engaged_user_profiles),
      icp_info: icp_data,
      product_info: product_documents
    });

    console.log(compiledPrompt);
    const response = await callLLM(compiledPrompt, prompt, LLM_MODEL_ID); // Will use prompt.config.llm_model_id if available
    return response;
    
  } catch (error) {
    console.error('Error generating content schedule:', error);
    throw new Error('Failed to generate content schedule.');
  }
}
