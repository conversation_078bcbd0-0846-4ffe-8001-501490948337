import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from '../utils/callLLM.js';
import {
  ContentGenerationParams,
  GeneratedContent
} from '../types/content-generation.js';
import {
  createPromptVariables,
  validateContentGenerationParams,
  ContentGenerationError
} from '../utils/content-generation.js';
import { ServerBlockNoteEditor } from '@blocknote/server-util';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

// Legacy interface for backward compatibility
interface TaskContentParams extends ContentGenerationParams {}

/**
 * Converts text content to BlockNote blocks format using official BlockNote server utilities
 */
async function convertTextToBlocks(text: string): Promise<any[]> {
  if (!text || text.trim() === '') {
    return [];
  }

  try {
    // Create a server-side BlockNote editor instance
    const editor = ServerBlockNoteEditor.create();

    // Use BlockNote's official markdown parser to convert text to blocks
    const blocks = await editor.tryParseMarkdownToBlocks(text);

    return blocks;
  } catch (error) {
    console.error('Error converting text to blocks with BlockNote:', error);

    // Fallback to simple paragraph blocks if BlockNote parsing fails
    const paragraphs = text.split('\n\n').filter(p => p.trim() !== '');

    return paragraphs.map(paragraph => ({
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: paragraph.trim(),
          styles: {},
        },
      ],
    }));
  }
}

/**
 * Compiles a Langfuse prompt with the given variables
 */
async function compilePrompt(params: ContentGenerationParams) {
  const prompt = await langfuse.getPrompt("generate_content_body_v2", undefined, { label: "production" });
  const promptVariables = createPromptVariables(params);
  const compiled = prompt.compile(promptVariables);
  
  // Return both compiled messages and config
  return {
    messages: compiled,
    config: prompt.config || {}
  };
}

/**
 * Generates content for a single task using Langfuse prompt and LLM
 */
export async function generateTaskContent(params: TaskContentParams): Promise<GeneratedContent> {
  try {
    // Validate input parameters
    validateContentGenerationParams(params);

    const { messages: compiledMessages, config: langfuseConfig } = await compilePrompt(params);

    // Merge Langfuse config with any overrides
    const llmConfig = {
      temperature: 0.7,
      max_tokens: 4000,
      ...(typeof langfuseConfig === 'object' && langfuseConfig !== null ? langfuseConfig : {}),
    };

    // Filter out empty messages that might confuse the LLM
    const filteredMessages = Array.isArray(compiledMessages) 
      ? compiledMessages.filter(msg => msg.content && msg.content.trim().length > 0)
      : compiledMessages;



    // Validate we have proper messages
    if (!Array.isArray(filteredMessages) || filteredMessages.length === 0) {
      throw new ContentGenerationError('No valid messages found after filtering');
    }

    // Call LLM with filtered messages
    const response = await callLLM(
      "",
      llmConfig,
      LLM_MODEL_ID, // fallback model
      { parse: false },
      filteredMessages
    );



    // Check if response is empty
    if (!response || response.trim().length === 0) {
      throw new ContentGenerationError('LLM returned empty response. Please try again.');
    }

    // Convert text content to BlockNote blocks using official server utilities
    const contentBlocks = await convertTextToBlocks(response);

    // Return standardized response format
    return {
      content: response, // Keep for backward compatibility
      content_editor_template: contentBlocks,
      visual_description: null,
      seo_keywords: [],
      trend_keywords: []
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    if (error instanceof ContentGenerationError) {
      throw error;
    }

    if (errorMessage.includes('generate_content_body_v2')) {
      throw new ContentGenerationError(`Prompt compilation failed: ${errorMessage}`);
    }

    console.error('Error generating task content:', error);
    throw new ContentGenerationError(
      'Failed to generate task content. Please check your API key and try again.',
      undefined,
      undefined,
      error instanceof Error ? error : new Error(errorMessage)
    );
  }
}
