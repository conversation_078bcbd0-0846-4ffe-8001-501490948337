import { v4 as uuidv4 } from 'uuid';
import { createClient } from '@supabase/supabase-js';
import { SpeechClient } from '@google-cloud/speech';
import { Storage } from '@google-cloud/storage';
import sql from '../clients/postgres.js';

interface ExtractAudioTranscriptParams {
  audioUrl: string;
  fileName: string;
  folderName: string;
  companyId: string;
  assetId?: string; // Optional asset ID for direct database updates
}

interface ExtractAudioTranscriptResult {
  success: boolean;
  transcriptFileName: string;
  transcriptUrl: string;
  transcriptPath: string;
}

// Initialize clients
const supabase = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const speechClient = new SpeechClient();
const storage = new Storage();

/**
 * Update asset record with transcript information
 */
async function updateAssetWithTranscript(
  companyId: string,
  fileName: string,
  transcriptPath: string,
  transcriptUrl: string,
  assetId?: string
): Promise<void> {
  try {
    console.log('📝 [ASSET-UPDATE] Updating asset record with transcript info...', {
      companyId,
      fileName,
      transcriptPath,
      transcriptUrl,
      assetId
    });
    
    let result;
    
    if (assetId) {
      // Use exact asset ID for precise matching
      console.log('🎯 [ASSET-UPDATE] Using exact asset ID for update:', assetId);
      result = await sql`
        UPDATE public.assets 
        SET 
          transcript_path = ${transcriptPath},
          transcript_url = ${transcriptUrl},
          has_transcript = true,
          transcript_extracted_at = ${Date.now()},
          processing_status = 'completed',
          processing_completed_at = ${Date.now()},
          updated_at = ${Date.now()}
        WHERE 
          id = ${assetId}
          AND account_id = ${companyId}
      `;
    } else {
      // Fallback to filename matching (legacy behavior)
      console.log('🔍 [ASSET-UPDATE] Using filename matching (fallback):', fileName);
      const baseFileName = fileName.replace(/^[a-f0-9-]+-/, ''); // Remove UUID prefix if present
      result = await sql`
        UPDATE public.assets 
        SET 
          transcript_path = ${transcriptPath},
          transcript_url = ${transcriptUrl},
          has_transcript = true,
          transcript_extracted_at = ${Date.now()},
          processing_status = 'completed',
          processing_completed_at = ${Date.now()},
          updated_at = ${Date.now()}
        WHERE 
          account_id = ${companyId} 
          AND (
            file_name = ${fileName} 
            OR file_name LIKE '%' || ${baseFileName}
          )
      `;
    }
    
    console.log('✅ [ASSET-UPDATE] Asset record updated successfully. Rows affected:', result.count);
    
    if (result.count === 0) {
      console.warn('⚠️ [ASSET-UPDATE] No rows were updated. Asset may not exist or conditions not met.');
    }
  } catch (error) {
    console.error('❌ [ASSET-UPDATE] Failed to update asset record:', error);
    // Don't throw - transcript extraction succeeded, this is just metadata
  }
}

/**
 * Extract transcript from audio using Google Cloud Speech-to-Text API
 * This approach works perfectly on App Engine and supports multiple audio formats
 */
async function extractTranscriptViaSpeechAPI(
  audioUrl: string,
  fileName: string,
  folderName: string,
  companyId: string,
  assetId?: string
): Promise<{ transcriptUrl: string; transcriptFilePath: string; transcription?: string }> {
  // Generate unique filename for the transcript file
  const baseName = fileName.split('.')[0];
  const transcriptFileName = `${uuidv4()}-${baseName.toLowerCase().replace(/[^a-z0-9]/g, '-')}.transcript.json`;
  const transcriptFilePath = `${companyId}/assets/${folderName}/${transcriptFileName}`;

  // Declare variables outside try block for cleanup access
  const processingFilePath = `${companyId}/assets/${folderName}/${transcriptFileName.replace('.transcript.json', '.processing.json')}`;
  let gcsUri: string | undefined;

  try {
    console.log('🎵 [SPEECH-API] Starting audio transcript extraction...');
    
    // Step 1: Create a "processing" indicator file immediately
    const processingMetadata = {
      originalAudio: audioUrl,
      originalFileName: fileName,
      status: 'processing',
      startedAt: new Date().toISOString(),
      processingMethod: 'Google Cloud Speech-to-Text API',
      note: 'Audio transcript extraction in progress. This file will be replaced when processing completes.',
    };
    
    const processingBuffer = Buffer.from(JSON.stringify(processingMetadata, null, 2), 'utf-8');
    
    await supabase.storage
      .from('brand-assets')
      .upload(processingFilePath, processingBuffer, {
        contentType: 'application/json',
      });
    
    // Step 2: Upload audio to Google Cloud Storage temporarily for processing
    // Note: Speech API works better with GCS URIs for longer audio files
    gcsUri = await uploadAudioToGCS(audioUrl, fileName, companyId);
    
    console.log('🤖 [SPEECH-API] Processing audio with Speech-to-Text API...');
    
    // Step 3: Configure Speech API request
    // Detect file format from filename
    const fileExtension = fileName.split('.').pop()?.toLowerCase();
    let encoding: string;
    let sampleRateHertz: number;
    
    switch (fileExtension) {
      case 'mp3':
        encoding = 'MP3';
        sampleRateHertz = 44100; // Common MP3 sample rate
        break;
      case 'wav':
        encoding = 'LINEAR16';
        sampleRateHertz = 16000;
        break;
      case 'ogg':
        encoding = 'OGG_OPUS';
        sampleRateHertz = 16000;
        break;
      case 'm4a':
        encoding = 'MP3'; // M4A can be treated as MP3 for Speech API
        sampleRateHertz = 44100;
        break;
      default:
        encoding = 'ENCODING_UNSPECIFIED'; // Let API auto-detect
        sampleRateHertz = 16000;
        break;
    }

    console.log(`🎵 [SPEECH-API] Detected format: ${fileExtension} -> encoding: ${encoding}, sampleRate: ${sampleRateHertz}`);

    const request = {
      audio: {
        uri: gcsUri,
      },
      config: {
        encoding: encoding as any,
        sampleRateHertz: sampleRateHertz,
        languageCode: 'en-US',
        enableAutomaticPunctuation: true,
        enableSpeakerDiarization: true,
        diarizationSpeakerCount: 2,
        enableWordTimeOffsets: true, // ✅ Enable word-level timestamps!
        enableWordConfidence: true,  // ✅ Enable word-level confidence scores
        model: 'latest_long', // Best model for longer audio files
        useEnhanced: true, // Enhanced model for better accuracy
      },
    };

    // Step 4: Start the transcription operation
    const [operation] = await speechClient.longRunningRecognize(request);
    console.log('⏳ [SPEECH-API] Waiting for transcription to complete...');
    
    // Step 5: Wait for the operation to complete
    const [response] = await operation.promise();
    
    if (!response.results?.length) {
      throw new Error('No transcription results found in audio');
    }

    // Step 6: Extract transcript and metadata
    let fullTranscript = '';
    const transcriptData = [];
    
    for (const result of response.results) {
      if (result.alternatives?.[0]) {
        const alternative = result.alternatives[0];
        fullTranscript += alternative.transcript + ' ';
        
        // Store detailed transcript data with proper timing calculation
        transcriptData.push({
          transcript: alternative.transcript,
          confidence: alternative.confidence || 0.8,
          startMs: alternative.words?.[0] ?
            ((Number(alternative.words[0].startTime?.seconds || 0)) + (Number(alternative.words[0].startTime?.nanos || 0)) * 1e-9) * 1000 : 0,
          endMs: alternative.words?.[alternative.words.length - 1] ?
            ((Number(alternative.words[alternative.words.length - 1].endTime?.seconds || 0)) + (Number(alternative.words[alternative.words.length - 1].endTime?.nanos || 0)) * 1e-9) * 1000 : 0,
          words: alternative.words?.map(word => ({
            word: word.word || '',
            startMs: ((Number(word.startTime?.seconds || 0)) + (Number(word.startTime?.nanos || 0)) * 1e-9) * 1000,
            endMs: ((Number(word.endTime?.seconds || 0)) + (Number(word.endTime?.nanos || 0)) * 1e-9) * 1000,
            confidence: word.confidence || 0.8,
            speakerTag: word.speakerTag || 0,
          })) || [],
        });
      }
    }

    console.log('💾 [SPEECH-API] Saving transcript to Supabase...');
    
    // Step 7: Create comprehensive transcript metadata
    const transcriptMetadata = {
      originalAudio: audioUrl,
      originalFileName: fileName,
      transcriptFileName: transcriptFileName,
      fullTranscript: fullTranscript.trim(),
      detailedTranscript: transcriptData,
      extractedAt: new Date().toISOString(),
      processingMethod: 'Google Cloud Speech-to-Text API',
      note: 'Transcript extracted from audio using Google Speech-to-Text API with speaker diarization and word timestamps',
    };

    // Step 8: Upload transcript to Supabase Storage
    const transcriptBuffer = Buffer.from(JSON.stringify(transcriptMetadata, null, 2), 'utf-8');
    
    const { error: transcriptUploadError } = await supabase.storage
      .from('brand-assets')
      .upload(transcriptFilePath, transcriptBuffer, {
        contentType: 'application/json',
      });

    if (transcriptUploadError) {
      throw new Error(`Failed to upload transcript: ${transcriptUploadError.message}`);
    }

    const { data: { publicUrl } } = supabase.storage
      .from('brand-assets')
      .getPublicUrl(transcriptFilePath);

    // Step 9: Cleanup temporary GCS file and processing indicator
    await cleanupGCSFile(gcsUri);
    
    // Remove the processing indicator file
    try {
      await supabase.storage
        .from('brand-assets')
        .remove([processingFilePath]);
      console.log('🗑️ [PROCESSING-CLEANUP] Removed processing indicator file');
    } catch (cleanupError) {
      console.warn('⚠️ [PROCESSING-CLEANUP] Failed to remove processing indicator:', cleanupError);
    }

    console.log('✅ [SPEECH-API] Audio transcript extraction completed successfully');

    // Update asset record in database
    await updateAssetWithTranscript(companyId, fileName, transcriptFilePath, publicUrl, assetId);

    return {
      transcriptUrl: publicUrl,
      transcriptFilePath: transcriptFilePath,
      transcription: fullTranscript.trim(),
    };
  } catch (error) {
    console.error('❌ [SPEECH-API] Audio transcript extraction failed:', error);
    
    // Cleanup on error - remove processing indicator and temporary files
    try {
      await supabase.storage
        .from('brand-assets')
        .remove([processingFilePath]);
      console.log('🗑️ [ERROR-CLEANUP] Removed processing indicator file after error');
    } catch (cleanupError) {
      console.warn('⚠️ [ERROR-CLEANUP] Failed to remove processing indicator after error:', cleanupError);
    }
    
    // If gcsUri was created, clean it up too
    if (typeof gcsUri !== 'undefined') {
      try {
        await cleanupGCSFile(gcsUri);
      } catch (gcsCleanupError) {
        console.warn('⚠️ [ERROR-CLEANUP] Failed to cleanup GCS file after error:', gcsCleanupError);
      }
    }
    
    throw error;
  }
}

/**
 * Upload audio to Google Cloud Storage for Speech API processing
 */
async function uploadAudioToGCS(audioUrl: string, fileName: string, companyId: string): Promise<string> {
  const bucketName = process.env.GCS_TEMP_BUCKET || 'video-processing-temp';
  const tempFileName = `${companyId}/audio/${uuidv4()}-${fileName}`;
  
  try {
    // Download audio
    const audioResponse = await fetch(audioUrl);
    if (!audioResponse.ok) {
      throw new Error(`Failed to download audio: ${audioResponse.statusText}`);
    }
    
    const audioBuffer = Buffer.from(await audioResponse.arrayBuffer());
    
    // Upload to GCS
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(tempFileName);
    
    await file.save(audioBuffer, {
      metadata: {
        contentType: 'audio/mpeg', // Default, will be auto-detected
      },
    });
    
    return `gs://${bucketName}/${tempFileName}`;
  } catch (error) {
    console.error('❌ [GCS-UPLOAD] Failed to upload audio to GCS:', error);
    throw error;
  }
}

/**
 * Clean up temporary GCS file after processing
 */
async function cleanupGCSFile(gcsUri: string): Promise<void> {
  try {
    // Parse GCS URI: gs://bucket/path
    const matches = gcsUri.match(/^gs:\/\/([^\/]+)\/(.+)$/);
    if (!matches) return;
    
    const [, bucketName, fileName] = matches;
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(fileName);
    
    await file.delete();
    console.log(`🗑️ [GCS-CLEANUP] Cleaned up temporary audio file: ${gcsUri}`);
  } catch (error) {
    console.warn('⚠️ [GCS-CLEANUP] Failed to cleanup temporary GCS file:', error);
    // Don't throw - cleanup failure shouldn't break the main process
  }
}

/**
 * Fallback: Create a placeholder file if Speech API is not available
 */
async function createTranscriptPlaceholder(
  audioUrl: string,
  fileName: string,
  folderName: string,
  companyId: string
): Promise<{ transcriptUrl: string; transcriptFilePath: string }> {
  const baseName = fileName.split('.')[0];
  const placeholderFileName = `${uuidv4()}-${baseName.toLowerCase().replace(/[^a-z0-9]/g, '-')}.placeholder.json`;
  const placeholderFilePath = `${companyId}/assets/${folderName}/${placeholderFileName}`;

  const placeholderMetadata = {
    originalAudio: audioUrl,
    originalFileName: fileName,
    status: 'failed',
    error: 'Google Cloud Speech-to-Text API not available',
    createdAt: new Date().toISOString(),
    note: 'This is a placeholder file created because audio transcription failed. The original audio file is still available.',
  };

  const placeholderBuffer = Buffer.from(JSON.stringify(placeholderMetadata, null, 2), 'utf-8');
  
  await supabase.storage
    .from('brand-assets')
    .upload(placeholderFilePath, placeholderBuffer, {
      contentType: 'application/json',
    });

  const { data: { publicUrl } } = supabase.storage
    .from('brand-assets')
    .getPublicUrl(placeholderFilePath);

  return {
    transcriptUrl: publicUrl,
    transcriptFilePath: placeholderFilePath,
  };
}

export async function extractAudioTranscript(params: ExtractAudioTranscriptParams): Promise<ExtractAudioTranscriptResult> {
  const { audioUrl, fileName, folderName, companyId, assetId } = params;
  
  console.log('🎵 [AUDIO-TRANSCRIPT] Starting audio transcript extraction:', {
    fileName,
    folderName,
    companyId,
  });

  try {
    let result: { transcriptUrl: string; transcriptFilePath: string; transcription?: string };

    // Use Google Cloud Speech-to-Text API for transcript extraction
    try {
      console.log('🤖 [AUDIO-TRANSCRIPT] Using Google Speech-to-Text API...');
      result = await extractTranscriptViaSpeechAPI(audioUrl, fileName, folderName, companyId, assetId);
    } catch (speechAPIError) {
      console.warn('⚠️ [AUDIO-TRANSCRIPT] Speech API failed, creating placeholder:', speechAPIError);
      
      // Fallback to placeholder if Speech API is not available
      result = await createTranscriptPlaceholder(audioUrl, fileName, folderName, companyId);
    }

    console.log('✅ [AUDIO-TRANSCRIPT] Audio transcript extraction completed successfully');

    return {
      success: true,
      transcriptFileName: result.transcriptFilePath.split('/').pop() || '',
      transcriptUrl: result.transcriptUrl,
      transcriptPath: result.transcriptFilePath,
    };
  } catch (error) {
    console.error('❌ [AUDIO-TRANSCRIPT] Audio transcript extraction failed:', error);
    throw error;
  }
}

