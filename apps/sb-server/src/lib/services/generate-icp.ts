import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

interface ICPData {
  name: string;
  target_industries: string[];
  geography_markets: string[];
  use_cases_problems: string[];
  buying_triggers: string[];
  decision_making_departments: string[];
  technologies_used: string[];
  content_needs: string[];
  red_flags_disqualifiers: string[];
  company_size_employees: {
    min: number | null;
    max: number | null;
  };
  company_revenue_range_usd: {
    min: number | null;
    max: number | null;
  };
  typical_contract_value: {
    min: number | null;
    max: number | null;
  };
  lifecycle_stage: 'Startup' | 'Scale-up' | 'Mature Enterprise' | null;
  notes: string;
}

export const generateICP = async (websiteContent: string, companyName: string, previousICPData = {}, referenceMaterialContent: string = '', referenceDescription: string = ''): Promise<ICPData> => {
  console.log('websiteContent', websiteContent);
  console.log('companyName', companyName);
  console.log('referenceMaterialContent', referenceMaterialContent);
  console.log('referenceDescription', referenceDescription);

  try {
   
    // Get production prompt 
    const prompt: any = await langfuse.getPrompt("generate_icp", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
      websiteContent,
      companyName,
      previousICPData: JSON.stringify(previousICPData),
      reference_material: referenceMaterialContent,
      reference_description: referenceDescription
    });
    console.log(compiledPrompt);
    const response = await callLLM(compiledPrompt, prompt, LLM_MODEL_ID); // Will use prompt.config.llm_model_id if available
    return response;

  } catch (error) {
    console.error('Error generating ICP:', error);
    throw error;
  }
};
