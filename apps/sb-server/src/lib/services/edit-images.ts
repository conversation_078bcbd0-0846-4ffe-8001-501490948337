import dotenv from 'dotenv';
import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

// Types for image editing
interface ImageEditingParams {
  edit_prompt: string;
  original_image_url: string;
  company_id: string;
  image_gen_styles?: any;
  brand_context?: string;
  reference_images?: string[];
}

interface ImageEditingResponse {
  url: string;
  path?: string;
  metadata?: {
    model: string;
    timestamp: string;
    company_id: string;
    prompt_hash: string;
  };
}

// Google Gemini model for image editing
const IMAGE_EDITING_MODEL = "gemini-2.5-flash-image-preview"

// Add this helper function to format image styles
function formatImageStyles(imageGenStyles: any): string {
  if (!imageGenStyles || typeof imageGenStyles !== 'object') {
    return "Professional, clean, modern aesthetic";
  }

  const styleParts: string[] = [];
  
  // Extract key style elements
  if (imageGenStyles.image_styles) {
    styleParts.push(`Style: ${imageGenStyles.image_styles}`);
  }
  
  if (imageGenStyles.image_modes) {
    styleParts.push(`Mode: ${imageGenStyles.image_modes}`);
  }
  
  if (imageGenStyles.image_lighting) {
    styleParts.push(`Lighting: ${imageGenStyles.image_lighting}`);
  }
  
  if (imageGenStyles.image_details) {
    styleParts.push(`Detail Level: ${imageGenStyles.image_details}`);
  }
  
  if (imageGenStyles.image_color_balance) {
    styleParts.push(`Color: ${imageGenStyles.image_color_balance}`);
  }
  
  if (imageGenStyles.image_camera_angle_perspective) {
    styleParts.push(`Camera Angle: ${imageGenStyles.image_camera_angle_perspective}`);
  }
  
  if (imageGenStyles.image_mood_atmosphere) {
    styleParts.push(`Mood: ${imageGenStyles.image_mood_atmosphere}`);
  }
  
  if (imageGenStyles.artistic_influences_references) {
    styleParts.push(`Influences: ${imageGenStyles.artistic_influences_references}`);
  }

  return styleParts.length > 0 
    ? styleParts.join(', ') 
    : "Professional, clean, modern aesthetic";
}

/**
 * Edits an image using Google Gemini's image editing capabilities
 */
export async function editImages(params: ImageEditingParams): Promise<ImageEditingResponse> {
  try {
    const { edit_prompt, original_image_url, company_id, image_gen_styles, brand_context, reference_images } = params;

    // Validate required fields
    if (!edit_prompt) {
      throw new Error('Missing required field: edit_prompt');
    }

    if (!original_image_url) {
      throw new Error('Missing required field: original_image_url');
    }

    if (!company_id) {
      throw new Error('Missing required field: company_id');
    }

    // Download the original image to get base64 data
    const originalImageBase64 = await downloadImageAsBase64(original_image_url);

    // Create enhanced edit prompt with style context
    const enhancedPrompt = createEnhancedEditPrompt(edit_prompt, image_gen_styles, brand_context);

    // Download reference images if provided
    const referenceImageData = [];
    if (reference_images && reference_images.length > 0) {
      for (const refUrl of reference_images) {
        const refBase64 = await downloadImageAsBase64(refUrl);
        referenceImageData.push(refBase64);
      }
    }

    // Edit image using Google Gemini API
    const editedImageData = await editImageWithGemini(enhancedPrompt, originalImageBase64, referenceImageData);

    // Generate metadata
    const promptHash = crypto.createHash('md5').update(enhancedPrompt).digest('hex');
    const metadata = {
      model: IMAGE_EDITING_MODEL,
      timestamp: new Date().toISOString(),
      company_id,
      prompt_hash: promptHash
    };

    // Upload edited image and return response
    const uploadResult = await uploadImage(editedImageData, metadata);

    return {
      ...uploadResult,
      metadata
    };

  } catch (error) {
    console.error('Error in editImages:', error);
    throw new Error(`Failed to edit image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Download an image from URL and convert to base64
 */
async function downloadImageAsBase64(imageUrl: string): Promise<string> {
  try {
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.status} ${response.statusText}`);
    }
    
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    return buffer.toString('base64');
  } catch (error) {
    console.error('Error downloading image:', error);
    throw new Error(`Failed to download image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create enhanced edit prompt with style and brand context
 */
function createEnhancedEditPrompt(editPrompt: string, imageGenStyles?: any, brandContext?: string): string {
  let enhancedPrompt = editPrompt;
  
  // Add style context if provided
  if (imageGenStyles) {
    const styleDescription = formatImageStyles(imageGenStyles);
    enhancedPrompt += ` Maintain the following style characteristics: ${styleDescription}.`;
  }
  
  // Add brand context if provided
  if (brandContext) {
    enhancedPrompt += ` Brand context: ${brandContext}.`;
  }
  
  // Add quality and consistency instructions
  enhancedPrompt += ' Ensure the edited image maintains high quality, proper lighting, and seamless integration of changes with the existing image elements.';
  
  return enhancedPrompt;
}

/**
 * Edit image using Google Gemini API
 */
async function editImageWithGemini(editPrompt: string, originalImageBase64: string, referenceImageData: string[] = []): Promise<Buffer> {
  try {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }

    // Google Gemini API call for image editing
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${IMAGE_EDITING_MODEL}:generateContent`, {
      method: 'POST',
      headers: {
        'x-goog-api-key': process.env.GEMINI_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [
            {
              inline_data: {
                mime_type: 'image/png',
                data: originalImageBase64
              }
            },
            // Add reference images if provided
            ...referenceImageData.map(refData => ({
              inline_data: {
                mime_type: 'image/png',
                data: refData
              }
            })),
            {
              text: editPrompt
            }
          ]
        }]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Gemini API error response:', errorText);
      throw new Error(`Gemini API request failed with status ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('Gemini API response structure:', JSON.stringify(data, null, 2));

    // Extract image data from Gemini response
    const candidate = data.candidates?.[0];
    if (!candidate) {
      throw new Error('No candidate in Gemini response');
    }

    const content = candidate.content;
    if (!content || !content.parts) {
      throw new Error('No content parts in Gemini response');
    }

    // Find the image part in the response (Gemini uses inlineData, not inline_data)
    const imagePart = content.parts.find((part: any) => 
      (part.inline_data && part.inline_data.data) || 
      (part.inlineData && part.inlineData.data)
    );
    if (!imagePart) {
      throw new Error('No image data found in Gemini response');
    }

    const base64Data = imagePart.inline_data?.data || imagePart.inlineData?.data;
    if (!base64Data) {
      throw new Error('No base64 data found in image part');
    }

    // Convert to buffer
    const imageBuffer = Buffer.from(base64Data, 'base64');
    console.log('Successfully converted Gemini response to image buffer, size:', imageBuffer.length);

    return imageBuffer;

  } catch (error) {
    console.error('Error in editImageWithGemini:', error);
    throw new Error(`Failed to edit image with Gemini: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to storage and return URL
 * Dev: Uses ImgBB for public URLs (localhost Supabase URLs don't work externally)
 * Production: Uses Supabase storage for proper asset library organization
 */
async function uploadImage(imageBuffer: Buffer, metadata: any): Promise<ImageEditingResponse> {
  try {
    const isProduction = process.env.NODE_ENV === 'production';
    console.log(`Uploading edited image (${isProduction ? 'production' : 'development'} mode)...`);

    if (isProduction && process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      // Production: Use Supabase storage for proper asset library
      const result = await uploadToSupabase(imageBuffer, metadata.company_id);
      return {
        url: result.url,
        path: result.path,
        metadata
      };
    } else {
      // Development: Use ImgBB for public URLs + Supabase for asset library
      // Also fallback for production if Supabase credentials are missing
      if (!isProduction) {
        console.log('Development mode: Using ImgBB for publicly accessible URLs');
        
        // Upload to ImgBB for public URL
        const imgbbResult = await uploadToImgBB(imageBuffer);
        
        // ALSO upload to Supabase for asset library organization (if available)
        try {
          if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
            console.log('Also saving edited image to Supabase asset library...');
            await uploadToSupabase(imageBuffer, metadata.company_id);
            console.log('Successfully saved to both ImgBB and Supabase asset library');
          } else {
            console.log('Supabase credentials not available, skipping asset library save');
          }
        } catch (supabaseError) {
          console.warn('Failed to save to Supabase asset library (non-critical):', supabaseError);
        }
        
        return {
          url: imgbbResult.url,
          path: imgbbResult.url, // For compatibility
          metadata
        };
      } else {
        console.warn('Production mode but Supabase credentials missing, falling back to ImgBB');
        const result = await uploadToImgBB(imageBuffer);
        return {
          url: result.url,
          path: result.url, // For compatibility
          metadata
        };
      }
    }

  } catch (error) {
    console.error('Error uploading edited image:', error);
    throw new Error(`Failed to upload image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to Supabase Storage with proper asset library organization
 */
async function uploadToSupabase(imageBuffer: Buffer, companyId: string): Promise<{ url: string; path: string }> {
  try {
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required');
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // Generate unique filename with 'edited' prefix for better organization
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueId = crypto.randomUUID();
    const fileName = `edited-${uniqueId}-${timestamp}.png`;
    const filePath = `${companyId}/generated/${fileName}`;



    // Upload to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('generated')
      .upload(filePath, imageBuffer, {
        contentType: 'image/png',
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {

      throw uploadError;
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('generated')
      .getPublicUrl(filePath);


    return { url: publicUrl, path: filePath };

  } catch (error) {

    throw new Error(`Failed to upload to Supabase: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to ImgBB (development environment)
 */
async function uploadToImgBB(imageBuffer: Buffer): Promise<{ url: string }> {
  const imgbbApiKey = process.env.IMGBB_API_KEY;

  if (!imgbbApiKey) {
    throw new Error('IMGBB_API_KEY environment variable is required');
  }

  // Convert buffer to base64 string
  const base64Image = imageBuffer.toString('base64');

  // Create form data using URLSearchParams for Node.js compatibility
  const formData = new URLSearchParams();
  formData.append('image', base64Image);

  const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData,
  });

  if (!imgbbRes.ok) {
    const errorText = await imgbbRes.text();

    throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
  }

  const imgbbData = await imgbbRes.json();


  if (!imgbbData.success) {
    throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
  }

  return { url: imgbbData.data.url };
}

