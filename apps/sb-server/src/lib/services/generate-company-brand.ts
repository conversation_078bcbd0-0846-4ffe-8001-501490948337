import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from '../utils/callLLM.js';
import { CompanyBrand } from '../types/brand.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

export async function generateCompanyBrand(isHTML: boolean, html: string, brand_document: {
  url: string;
  pathname: string;
  downloadUrl: string;
} | null = null): Promise<CompanyBrand> {
  try {

    //download the pdf from the url
    let brand_document_base64 = '';
    if (!isHTML) {
      const pdfResponse = await fetch(brand_document.downloadUrl);
      const pdfBuffer = await pdfResponse.arrayBuffer();
      const base64PDF = Buffer.from(pdfBuffer).toString('base64');
      brand_document_base64 = `data:application/pdf;base64,${base64PDF}`;
    }
    // Get production prompt 
    const prompt = await langfuse.getPrompt("generate_brand_details", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
      brand_document: isHTML ? `Parse the following html to obtain the content scraped from the company's homepage: ${html}` : `Parse the attached  pdf to obtain the brand guide document information`
    });
    console.log(compiledPrompt);
    console.log(prompt);
    const response = await callLLM(compiledPrompt, prompt, LLM_MODEL_ID, { // Will use prompt.config.llm_model_id if available
      parse: true
    },
    isHTML ? [] : [
      {
        type: 'file',
        file: {
          filename: 'brand_document.pdf',
          content: brand_document_base64
        }
      }
    ]
  );
    return response;
    
  } catch (error) {
    console.error('Error generating company brand:', error);
    throw new Error('Failed to generate company brand.');
  }
}
