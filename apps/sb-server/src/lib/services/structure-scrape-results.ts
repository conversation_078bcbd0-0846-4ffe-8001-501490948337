import dotenv from 'dotenv';
import { Langfuse } from "langfuse";
import { callLLM, LLM_MODEL_ID } from '../utils/callLLM.js';

dotenv.config();

const langfuse = new Langfuse({
  secretKey: process.env.LANGFUSE_SECRET_KEY,
  publicKey: process.env.LANGFUSE_PUBLIC_KEY,
  baseUrl: process.env.LANGFUSE_BASE_URL
});

export async function structureScrapeResults(scraped_data: any, required_json_structure: Object, instructions: string ): Promise<any> {
  try {

    console.log("params",  scraped_data , required_json_structure);
   
    // Get production prompt 
    const prompt = await langfuse.getPrompt("structure_scrape_results", undefined, { label: "production" })
    const compiledPrompt = prompt.compile({
        scraped_data: JSON.stringify(scraped_data),
        required_json_structure: JSON.stringify(required_json_structure),
        instructions
    });
    console.log(compiledPrompt);
    const response = await callLLM(compiledPrompt, prompt, LLM_MODEL_ID); // Will use prompt.config.llm_model_id if available
    return response;
    
  } catch (error) {
    console.error('Error generating content schedule:', error);
    throw new Error('Failed to generate content schedule.');
  }
}
