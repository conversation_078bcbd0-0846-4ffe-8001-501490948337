'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import type { Database } from '~/lib/database.types';
import { CompanyContent } from '~/types/company-content';

const server = getSupabaseServerClient();

export async function createCompanyContent(
  content: Partial<CompanyContent> & {
    company_id: string;
    campaign_id: string;
    idea_id: string;
    content_type: string;
    language: string;
    content?: string; // Made optional since content_editor_template is now primary
  },
) {
  const processedContent = { ...content };
  if (processedContent.content_editor_template) {
    processedContent.content_editor_template = JSON.parse(
      JSON.stringify(processedContent.content_editor_template),
    );
  }

  const { data: companyContent, error } = await server
    .from('company_content')
    .insert(
      processedContent as Database['public']['Tables']['company_content']['Insert'],
    )
    .select()
    .single();

  if (error) throw error;

  return companyContent;
}

export async function getCompanyContent(campaignId: string) {
  console.log('GETTING COMPANY CONTENT', { campaignId });

  const { data, error } = await server
    .from('company_content')
    .select('*')
    .eq('campaign_id', campaignId)
    .order('created_at', { ascending: false });

  if (error) {
    throw error;
  }

  return (data || []).map(item => ({
    ...item,
    scheduled_at: item.scheduled_at ? new Date(item.scheduled_at).getTime() : undefined,
    published_at: item.published_at ? new Date(item.published_at).getTime() : undefined,
    visual_description_group: item.visual_description_group as any,
    seo_keywords: item.seo_keywords as any,
    video_editor_overlays: item.video_editor_overlays as any[],
    video_editor_aspect_ratio: item.video_editor_aspect_ratio as any,
    video_editor_player_dimensions: item.video_editor_player_dimensions as any,
    video_presentation_render_params: item.video_presentation_render_params as any,
    content_editor_template: item.content_editor_template as Block[] | null
  })) as CompanyContent[];
}

export async function getCompanyContentById(contentId: string) {
  console.log('GETTING COMPANY CONTENT BY ID', { contentId });

  const { data, error } = await server
    .from('company_content')
    .select('*')
    .eq('id', contentId)
    .order('created_at', { ascending: false })
    .single();

  if (error) {
    throw error;
  }

  return {
    ...data,
    scheduled_at: data.scheduled_at ? new Date(data.scheduled_at).getTime() : undefined,
    published_at: data.published_at ? new Date(data.published_at).getTime() : undefined,
    visual_description_group: data.visual_description_group as any,
    seo_keywords: data.seo_keywords as any,
    video_editor_overlays: data.video_editor_overlays as any[],
    video_editor_aspect_ratio: data.video_editor_aspect_ratio as any,
    video_editor_player_dimensions: data.video_editor_player_dimensions as any,
    video_presentation_render_params: data.video_presentation_render_params as any,
    content_editor_template: data.content_editor_template as Block[] | null
  } as CompanyContent;
}

/**
 * Saves content tasks to the company_content table
 */
export async function saveContentTasks(
  tasks: any[],
  campaignId: string,
  ideaId: string,
  companyId: string,
): Promise<any[]> {
  const tasksToInsert = tasks.map((task) => ({
    campaign_id: campaignId,
    idea_id: ideaId, // Using campaign ID as idea ID since they're 1:1 in this case
    content_type: task.content_type,
    language: task.language,
    task_id: task.id,
    task_title: task.title,
    task_description: task.content.description,
    channel: task.channel,
    status: task.status ? task.status.toLowerCase() : 'to do',
    has_image: task.hasVisualContent,
    visual_description: task.content.visualDescription || null,
    company_id: companyId,
    scheduled_publishing_time: task.scheduled_publishing_time.date
      ? new Date(
          task.scheduled_publishing_time.date.split('/').reverse().join('-'),
        ).toISOString()
      : null,
    is_scheduled: false,
    is_posted: false,
    is_draft: true, // Default to draft
    //   content: task.content.body,
  }));

  const { error: contentError } = await server
    .from('company_content')
    .insert(tasksToInsert);

  if (contentError) {
    throw new Error(`Failed to save content tasks: ${contentError.message}`);
  }
  return tasksToInsert;
}

/**
 * Updates a company content record with the provided fields
 */
export async function updateCompanyContent(
  id: string | undefined,
  updates: Partial<CompanyContent>,
): Promise<void> {
  try {
    if (!id) {
      console.error('Cannot update company content: ID is undefined or null');
      throw new Error(
        'Failed to update company content: ID is undefined or null',
      );
    }

    const processedUpdates = { ...updates };
    if (processedUpdates.status) {
      processedUpdates.status = processedUpdates.status.toLowerCase();
    }

    if (processedUpdates.content_editor_template) {
      processedUpdates.content_editor_template = JSON.parse(
        JSON.stringify(processedUpdates.content_editor_template),
      );
    }

    console.log('UPDATING COMPANY CONTENT', { id, updates: processedUpdates });
    const { data, error } = await server
      .from('company_content')
      .update(
        processedUpdates as Database['public']['Tables']['company_content']['Update'],
      )
      .eq('id', id);
    console.log('UPDATED COMPANY CONTENT', { data, error });

    if (error) {
      console.error('Error updating company content:', error);
      throw new Error(`Failed to update company content: ${error.message}`);
    }
  } catch (err) {
    console.error('Exception in updateCompanyContent:', err);
    throw err;
  }
}

/**
 * Deletes a company content record
 */
export async function deleteCompanyContent(id: string): Promise<void> {
  console.log('DELETING COMPANY CONTENT', { id });

  const { data, error } = await server
    .from('company_content')
    .delete()
    .eq('id', id);
  console.log('DELETED COMPANY CONTENT', { data, error });
  if (error) {
    throw new Error(`Failed to delete company content: ${error.message}`);
  }
}
