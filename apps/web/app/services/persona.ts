'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { Persona, PersonaData, PersonaFormData } from '~/types/persona';


const client = getSupabaseServerClient();

/**
 * Create a new persona
 * @param persona - The persona data to create
 * @returns The created persona
 */
export async function createPersona(
  persona: Partial<PersonaFormData> & { company_id: string; name: string; role: string }
) {
  const {
    data: { user },
  } = await client.auth.getUser();

  if (!user) {
    throw new Error('No authenticated user');
  }

  // Convert array fields to JSONB
  const personaData = {
    ...persona,
    industries: persona.industries ? persona.industries : null,
    tech_stack: persona.tech_stack ? persona.tech_stack : null,
    challenges: persona.challenges ? persona.challenges : null,
    goals: persona.goals ? persona.goals : null,
    info_preferences: persona.info_preferences ? persona.info_preferences : null,
    content_formats: persona.content_formats ? persona.content_formats : null,
    channels: persona.channels ? persona.channels : null,
    topics: persona.topics ? persona.topics : null,
  };

  const { data, error } = await client
    .from('personas')
    .insert(personaData)
    .select()
    .single();

  if (error) throw error;
  return data;
}

/**
 * Update an existing persona
 * @param persona - The persona data to update
 * @returns The updated persona
 */
export async function updatePersona(
  persona: Partial<PersonaFormData> & { id: string }
) {
  // Convert array fields to JSONB
  const personaData = {
    ...persona,
    industries: persona.industries ? persona.industries : null,
    tech_stack: persona.tech_stack ? persona.tech_stack : null,
    challenges: persona.challenges ? persona.challenges : null,
    goals: persona.goals ? persona.goals : null,
    info_preferences: persona.info_preferences ? persona.info_preferences : null,
    content_formats: persona.content_formats ? persona.content_formats : null,
    channels: persona.channels ? persona.channels : null,
    topics: persona.topics ? persona.topics : null,
  };

  const { data, error } = await client
    .from('personas')
    .update(personaData)
    .match({ id: persona.id })
    .select()
    .single();

  if (error) throw error;
  return data;
}

/**
 * Get all personas for a company
 * @param companyId - The company ID to get personas for
 * @returns Array of personas
 */
export async function getPersonas(companyId: string): Promise<Persona[]> {
  const { data, error } = await client
    .from('personas')
    .select('*')
    .eq('company_id', companyId)
    .order('name', { ascending: true });

  if (error) throw error;
  return (data || []).map(item => ({
    ...item,
    data: item.data as PersonaData
  })) as Persona[];
}

/**
 * Get a persona by ID
 * @param id - The persona ID
 * @returns The persona or null if not found
 */
export async function getPersonaById(id: string): Promise<Persona | null> {
  const { data, error } = await client
    .from('personas')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') { // Not found
      return null;
    }
    throw error;
  }
  
  return {
    ...data,
    data: data.data as PersonaData
  } as Persona;
}

/**
 * Delete a persona
 * @param id - The persona ID to delete
 */
export async function deletePersona(id: string): Promise<void> {
  const { error } = await client
    .from('personas')
    .delete()
    .match({ id });

  if (error) throw error;
}

/**
 * Update a specific field on a persona
 * @param personaId - The persona ID
 * @param updates - The field updates
 * @returns The updated persona
 */
export async function updatePersonaField(
  personaId: string,
  updates: Partial<Persona>
): Promise<Persona> {
  const { data, error } = await client
    .from('personas')
    .update(updates)
    .match({ id: personaId })
    .select()
    .single();

  if (error) throw error;
  return {
    ...data,
    data: data.data as PersonaData
  } as Persona;
}

/**
 * Associate a persona with a campaign
 * @param personaId - The persona ID
 * @param campaignId - The campaign ID
 * @param isPrimary - Whether this is the primary persona for the campaign
 */
export async function associatePersonaWithCampaign(
  personaId: string,
  campaignId: string,
  isPrimary: boolean = false
): Promise<void> {
  const { error } = await client
    .from('campaign_personas')
    .upsert(
      {
        persona_id: personaId,
        campaign_id: campaignId,
        is_primary: isPrimary
      },
      { onConflict: 'campaign_id,persona_id' }
    );

  if (error) throw error;
}

/**
 * Get personas associated with a campaign
 * @param campaignId - The campaign ID
 * @returns Array of personas
 */
export async function getPersonasByCampaign(campaignId: string): Promise<Persona[]> {
  const { data, error } = await client
    .from('campaign_personas')
    .select('persona_id, is_primary')
    .eq('campaign_id', campaignId);

  if (error) throw error;

  if (data.length === 0) return [];

  const personaIds = data.map(item => item.persona_id);
  
  const { data: personas, error: personasError } = await client
    .from('personas')
    .select('*')
    .in('id', personaIds);

  if (personasError) throw personasError;

  // Add is_primary field to each persona
  return (personas || []).map(persona => ({
    ...persona,
    data: persona.data as PersonaData,
    is_primary: data.find(item => item.persona_id === persona.id)?.is_primary || false
  })) as Persona[];
}

/**
 * Associate a persona with content
 * @param personaId - The persona ID
 * @param contentId - The content ID
 * @param isPrimary - Whether this is the primary persona for the content
 */
export async function associatePersonaWithContent(
  personaId: string,
  contentId: string,
  isPrimary: boolean = false
): Promise<void> {
  const { error } = await client
    .from('content_personas')
    .upsert(
      {
        persona_id: personaId,
        content_id: contentId,
        is_primary: isPrimary
      },
      { onConflict: 'content_id,persona_id' }
    );

  if (error) throw error;
}

/**
 * Get personas associated with content
 * @param contentId - The content ID
 * @returns Array of personas
 */
export async function getPersonasByContent(contentId: string): Promise<Persona[]> {
  const { data, error } = await client
    .from('content_personas')
    .select('persona_id, is_primary')
    .eq('content_id', contentId);

  if (error) throw error;

  if (data.length === 0) return [];

  const personaIds = data.map(item => item.persona_id);
  
  const { data: personas, error: personasError } = await client
    .from('personas')
    .select('*')
    .in('id', personaIds);

  if (personasError) throw personasError;

  // Add is_primary field to each persona
  return (personas || []).map(persona => ({
    ...persona,
    data: persona.data as PersonaData,
    is_primary: data.find(item => item.persona_id === persona.id)?.is_primary || false
  })) as Persona[];
}