'use server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { CampaignIdea } from '~/types/campaign-idea';

const client = getSupabaseServerClient();

export async function createCampaignIdea(
  campaign: Partial<CampaignIdea> & {
    campaign_id: string;
    content: string;
    languages: string[];
    company_id: string;
    brief: { [key: string]: any } | null;
  },
) {
  const {
    data: { user },
  } = await client.auth.getUser();

  if (!user) {
    throw new Error('No authenticated user');
  }

  const { data: campaignIdea, error } = await client
    .from('campaign_ideas')
    .insert({
      campaign_id: campaign.campaign_id,
      content: campaign.content,
      languages: campaign.languages,
      company_id: campaign.company_id,
      brief: campaign.brief,
      title: campaign.title,
      metadata: campaign.metadata,
      content_types: campaign.content_types ? (campaign.content_types) : null,
      channels: campaign.channels ? (campaign.channels) : null,
    } as CampaignIdea)
    .select()
    .single();

  if (error) throw error;

  return campaignIdea;
}

/**
 * Get a campaign ideas by id
 * @param id - The id of the campaign idea
 * @returns The campaign ideas
 */
export async function getCampaignIdeaById(id: string) : Promise<CampaignIdea[]> {
  //get all campaign ideas by campaign id
  console.log('id', id);
  const { data, error } = await client.from('campaign_ideas').select('*').eq('id', id);
  if (error) throw error;

  return (data || []).map(item => ({
    ...item,
    title: item.title || '', // Handle null title
    brief: item.brief as Json,
    metadata: item.metadata as Json,
    content_types: item.content_types as Json,
    languages: item.languages as Json,
    channels: item.channels as Json,
    brief_blocks: item.brief_blocks as Block[],
    content_blocks: item.content_blocks as Block[]
  })) as CampaignIdea[];
}

/**
 * Get a campaign ideas 
 * @param id - The campaign id
 * @returns The campaign ideas
 */
export async function getCampaignIdeas(id: string) : Promise<CampaignIdea[]> {
  //get all campaign ideas by campaign id
  console.log('id', id);
  const { data, error } = await client.from('campaign_ideas').select('*').eq('campaign_id', id);
  if (error) throw error;

  return (data || []).map(item => ({
    ...item,
    title: item.title || '', // Handle null title
    brief: item.brief as Json,
    metadata: item.metadata as Json,
    content_types: item.content_types as Json,
    languages: item.languages as Json,
    channels: item.channels as Json,
    brief_blocks: item.brief_blocks as Block[],
    content_blocks: item.content_blocks as Block[]
  })) as CampaignIdea[];
}

/**
 * Delete a campaign idea by id
 * @param id - The id of the campaign idea to delete
 * @returns void
 */
export async function deleteCampaignIdea(id: string): Promise<void> {
  const {
    data: { user },
  } = await client.auth.getUser();

  if (!user) {
    throw new Error('No authenticated user');
  }

  const { error } = await client
    .from('campaign_ideas')
    .delete()
    .eq('id', id);

  if (error) throw error;
}
