'use server';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';


// import { getAccountByCurrentUser } from './account';
import { getBrandInformation } from './storage';
import { getStorageFileUrl } from './storage-urls';
import { getFontFormat, getUniqueId } from './utils';

const server = getSupabaseServerClient();

type LoggerContext = {
  name: string;
  userId?: string;
  companyId?: string;
  brandId?: string;
  fileName?: string;
};

export async function createCompanyBrand(brandInfo: any) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'create-company-brand',
    companyId: brandInfo.company_id,
  };

  logger.info(ctx, 'Creating new company brand...');

  try {
    const { data: brand, error } = await server
      .from('company_brand')
      .insert({
        company_id: brandInfo.company_id as string,
        mission: brandInfo.mission,
        vision: brandInfo.vision,
        value_proposition: brandInfo.value_proposition,
        audience: brandInfo.audience,
        personality: brandInfo.personality,
        messaging_pillars: brandInfo.messaging_pillars,
        identity: brandInfo.identity,
        guidelines: brandInfo.guidelines,
        voice: brandInfo.voice,
        brand_colors: brandInfo.brand_colors,
        has_brand_setup: true,
        product_list: brandInfo.product_list,
        is_draft: brandInfo.is_draft,
      })
      .select()
      .single();

    if (error) {
      logger.error({ ...ctx, error }, 'Failed to create company brand');
      throw error;
    }

    logger.info(
      { ...ctx, brandId: brand.id },
      'Successfully created company brand',
    );
    return brand;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error creating company brand');
    throw error;
  }
}

export async function getBrandData(companyId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = { name: 'get-brand-data' };

  logger.info(ctx, 'Fetching brand data...');

  const {
    data: { user },
  } = await server.auth.getUser();
  if (!user) {
    logger.error(ctx, 'User not authenticated');
    throw new Error('User not authenticated');
  }

  ctx.userId = user.id;

  try {
    logger.info(ctx, 'Fetching cached brand information');
    const brandInfo = await getBrandInformation(companyId);
    if (!brandInfo) {
      logger.info(ctx, 'No brand information found - company may not have completed brand setup yet');
      return null;
    }

    logger.info(ctx, 'Successfully retrieved brand information');
    return brandInfo;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Failed to get brand information');
    throw new Error('Could not get brand information');
  }
}

export async function updateBrandDetails(
  companyId: string,
  brandId: string | undefined,
  updates: any,
) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'update-brand-details',
    companyId,
    brandId,
  };

  logger.info(ctx, 'Upserting brand details...');

  try {
    // Prepare the data for upsert (no need to stringify JSON fields for JSONB)
    const upsertData = {
      company_id: companyId,
      ...updates,
      // Pass JSON directly to Supabase for JSONB columns
      brand_colors: updates.brand_colors,
      brand_fonts: updates.brand_fonts,
    };

    // Only include id if brandId is a non-empty string
    if (brandId && brandId.trim() !== '') {
      upsertData['id'] = brandId;
    }

    console.log(upsertData);
    const { data, error } = await server
      .from('company_brand')
      .upsert(upsertData, {
        onConflict: 'id'
      })
      .select()
      .single();

    if (error) {
      logger.error({ ...ctx, error }, 'Error upserting brand details');
      throw error;
    }

    logger.info(ctx, 'Successfully upserted brand details');
    return data;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error upserting brand details');
    throw error;
  }
}

export async function deleteBrand(companyId: string, brandId: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'delete-brand',
    companyId,
    brandId,
  };

  logger.info(ctx, 'Deleting brand and all associated assets...');

  try {
    // Step 1: List and delete all logo files
    logger.info(ctx, 'Deleting all brand logos...');
    const logoPath = `${companyId}/brand/logos/`;
    const { data: logoFiles, error: logoListError } = await server.storage
      .from('brand-assets')
      .list(logoPath);

    if (logoListError) {
      logger.error({ ...ctx, error: logoListError }, 'Error listing brand logos');
    } else if (logoFiles && logoFiles.length > 0) {
      const logoFilePaths = logoFiles.map(file => `${logoPath}${file.name}`);
      const { error: logoDeleteError } = await server.storage
        .from('brand-assets')
        .remove(logoFilePaths);

      if (logoDeleteError) {
        logger.error({ ...ctx, error: logoDeleteError }, 'Error deleting brand logos');
      } else {
        logger.info(ctx, `Successfully deleted ${logoFilePaths.length} logo files`);
      }
    }

    // Step 2: List and delete all font files
    logger.info(ctx, 'Deleting all brand fonts...');
    const fontPath = `${companyId}/brand/fonts/`;
    const { data: fontFiles, error: fontListError } = await server.storage
      .from('brand-assets')
      .list(fontPath);

    if (fontListError) {
      logger.error({ ...ctx, error: fontListError }, 'Error listing brand fonts');
    } else if (fontFiles && fontFiles.length > 0) {
      const fontFilePaths = fontFiles.map(file => `${fontPath}${file.name}`);
      const { error: fontDeleteError } = await server.storage
        .from('brand-assets')
        .remove(fontFilePaths);

      if (fontDeleteError) {
        logger.error({ ...ctx, error: fontDeleteError }, 'Error deleting brand fonts');
      } else {
        logger.info(ctx, `Successfully deleted ${fontFilePaths.length} font files`);
      }
    }

    // Step 3: Delete the brand record from the database
    console.log("Deleting brand record", companyId, brandId);
    const { data, error } = await server
      .from('company_brand')
      .delete()
      // .eq('company_id', companyId)
      .eq('id', brandId);
    console.log("Deleted brand record", data, error);
    if (error) {
      logger.error({ ...ctx, error }, 'Error deleting brand record');
      throw error;
    }

    logger.info(ctx, 'Successfully deleted brand and all assets');
    return true;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error deleting brand');
    throw error;
  }
}

export async function uploadNewBrandLogo(companyId: string, file: File) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-brand-logo',
    companyId,
    fileName: file.name,
  };

  logger.info(ctx, 'Uploading new brand logo...');

  const fileExt = file.name.split('.').pop();
  const sanitizedName = file?.name
    ?.split('.')[0]
    ?.toLowerCase()
    .replace(/[^a-z0-9]/g, '-');

  const fileName = `${sanitizedName}-${getUniqueId()}.${fileExt}`;
  const filePath = `${companyId}/brand/logos/${fileName}`;

  try {
    const { error } = await server.storage
      .from('brand-assets')
      .upload(filePath, file);

    if (error) {
      logger.error({ ...ctx, error, filePath }, 'Failed to upload brand logo');
      throw error;
    }

    const fileUrl = await getStorageFileUrl('brand-assets', filePath, 3600);

    logger.info({ ...ctx, filePath }, 'Successfully uploaded brand logo');
    return {
      name: fileName,
      url: fileUrl,
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error uploading brand logo');
    throw error;
  }
}

export async function deleteBrandLogo(companyId: string, fileName: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'delete-brand-logo',
    companyId,
    fileName,
  };

  logger.info(ctx, 'Deleting brand logo...');
  const filePath = `${companyId}/brand/logos/${fileName}`;

  try {
    const { error } = await server.storage
      .from('brand-assets')
      .remove([filePath]);

    if (error) {
      logger.error({ ...ctx, error, filePath }, 'Failed to delete brand logo');
      throw error;
    }

    logger.info({ ...ctx, filePath }, 'Successfully deleted brand logo');
    return fileName;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error deleting brand logo');
    throw error;
  }
}

export async function uploadNewBrandFont(companyId: string, file: File) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'upload-brand-font',
    companyId,
    fileName: file.name,
  };

  logger.info(ctx, 'Uploading new brand font...');

  const fileExt = file.name.split('.').pop();
  // Get the original font name without extension
  const originalFontName = file.name.split('.')[0];
  // Sanitize the font name but preserve readability
  const sanitizedName = originalFontName
    ?.toLowerCase()
    .replace(/[^a-z0-9]/g, '-');
  const fileName = `${sanitizedName}-${getUniqueId()}.${fileExt}`;
  const filePath = `${companyId}/brand/fonts/${fileName}`;

  try {
    const { error } = await server.storage
      .from('brand-assets')
      .upload(filePath, file);

    if (error) {
      logger.error({ ...ctx, error, filePath }, 'Failed to upload brand font');
      throw error;
    }

    const fileUrl = await getStorageFileUrl('brand-assets', filePath, 3600);

    // Use the sanitized name as the font family
    // This will be consistent with how we extract it in fetchFonts
    const fontFamily = sanitizedName;

    logger.info({ ...ctx, filePath }, 'Successfully uploaded brand font');
    return {
      name: fileName,
      url: fileUrl,
      fontFamily,
      format: getFontFormat(fileName),
    };
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error uploading brand font');
    throw error;
  }
}

export async function deleteBrandFont(companyId: string, fileName: string) {
  const logger = await getLogger();
  const ctx: LoggerContext = {
    name: 'delete-brand-font',
    companyId,
    fileName,
  };

  logger.info(ctx, 'Deleting brand font...');
  const filePath = `${companyId}/brand/fonts/${fileName}`;

  try {
    const { error } = await server.storage
      .from('brand-assets')
      .remove([filePath]);

    if (error) {
      logger.error({ ...ctx, error, filePath }, 'Failed to delete brand font');
      throw error;
    }

    logger.info({ ...ctx, filePath }, 'Successfully deleted brand font');
    return fileName;
  } catch (error) {
    logger.error({ ...ctx, error }, 'Unexpected error deleting brand font');
    throw error;
  }
}
