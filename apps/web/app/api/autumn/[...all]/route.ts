// app/api/autumn/[...all]/route.ts

import { autumnHandler } from "autumn-js/next";
import { getSupabaseServerClient } from "@kit/supabase/server-client";

export const { GET, POST } = autumnHandler({
  identify: async () => {
    const supabase = await getSupabaseServerClient();
    const { data, error } = await supabase.auth.getUser();

    if (error || !data?.user) {
      return null;
    }

    return {
      customerId: data.user.id,
      customerData: {
        name: data.user.user_metadata?.name,
        email: data.user.email,
      },
    };
  },
});