import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import ShortUniqueId from 'short-unique-id';

import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { StorageFile } from '~/types/assets';

export async function POST(request: NextRequest) {
  const logger = await getLogger();
  const ctx = { name: 'asset-blob-process' };

  try {
    logger.info(ctx, 'Starting asset processing from blob URL');
    
    // Check authentication
    const supabase = getSupabaseServerClient();
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error || !user) {
      logger.error(ctx, 'Unauthorized asset processing attempt');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    const { blobUrl, folderName, companyId, assetId, fileName, fileType } = await request.json();
    
    if (!blobUrl || !folderName || !companyId || !assetId) {
      logger.error(ctx, 'Missing required fields');
      return NextResponse.json({ 
        error: 'Missing required fields: blobUrl, folderName, companyId, assetId' 
      }, { status: 400 });
    }

    // Download the file from the blob URL
    logger.info(ctx, `Downloading file from blob URL for user ${user.id}: ${blobUrl}`);
    const fileResponse = await fetch(blobUrl);
    
    if (!fileResponse.ok) {
      logger.error(ctx, 'Failed to download file from blob URL');
      return NextResponse.json({ error: 'Failed to download file' }, { status: 400 });
    }

    // Get the file as a blob
    const fileBlob = await fileResponse.blob();
    
    // Extract filename from provided name or blob URL
    const originalFileName = fileName || blobUrl.split('/').pop() || 'file';
    const [baseName = 'file', extension = 'bin'] = originalFileName.split('.');
    const sanitizedName = baseName.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const uid = new ShortUniqueId({ length: 10 });
    const finalFileName = `${uid.rnd()}-${sanitizedName}.${extension}`;
    
    // Determine the storage path
    const filePath = `${companyId}/assets/${folderName}/${finalFileName}`;

    logger.info(ctx, `Uploading file to Supabase storage: ${filePath}`);

    // Upload to Supabase Storage
    const { error: uploadError } = await supabase.storage
      .from('brand-assets')
      .upload(filePath, fileBlob, {
        contentType: fileType,
        upsert: false
      });

    if (uploadError) {
      logger.error(ctx, 'Failed to upload to Supabase storage:', uploadError);
      return NextResponse.json({ 
        error: 'Failed to upload file to storage' 
      }, { status: 500 });
    }

    // Create signed URL for private bucket (24 hours expiry)
    const { data: signedUrlData, error: signedUrlError } = await supabase.storage
      .from('brand-assets')
      .createSignedUrl(filePath, 86400); // 24 hours

    if (signedUrlError || !signedUrlData?.signedUrl) {
      logger.error(ctx, 'Failed to create signed URL:', signedUrlError);
      return NextResponse.json({
        error: 'Failed to create file access URL'
      }, { status: 500 });
    }

    // Create the storage file object
    const storageFile: StorageFile = {
      path: filePath,
      url: signedUrlData.signedUrl,
      name: finalFileName,
      type: fileType,
    };

    logger.info(ctx, `Successfully processed asset: ${finalFileName}`);

    return NextResponse.json({
      success: true,
      file: storageFile,
      assetId,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error(ctx, 'Error processing asset:', error);
    return NextResponse.json(
      { error: 'Error processing asset' },
      { status: 500 },
    );
  }
}
