import { NextRequest } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Proxy the request to your Express server
    const response = await fetch("http://localhost:8080/extract-audio-transcript", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const error = await response.json();
      return Response.json({ error: error.error || "Failed to extract transcript" }, { status: response.status });
    }

    const result = await response.json();
    return Response.json(result);
  } catch (error: any) {
    console.error("Audio transcript extraction proxy error:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
}