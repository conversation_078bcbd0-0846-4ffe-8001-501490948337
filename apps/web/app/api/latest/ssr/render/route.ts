import { RenderRequest } from "@/components/video-editor/v7/types";
import { startRendering } from "@/components/video-editor/v7/ssr-helpers/custom-renderer";
import { executeApi } from "@/components/video-editor/v7/ssr-helpers/api-response";

/**
 * POST endpoint handler for rendering media using Remotion SSR
 */
export const POST = executeApi(RenderRequest, async (req, body) => {
  console.log("Render request received:", { id: body.id });
  console.log("inputProps:", JSON.stringify(body.inputProps, null, 2));

  // Extract accountId from inputProps
  const accountId = body.inputProps?.accountId as string;

  if (!accountId) {
    console.error("Error: accountId is missing from inputProps");
    throw new Error("accountId is required in inputProps for rendering.");
  }

  try {
    // Start the rendering process using our custom renderer, passing accountId
    const renderId = await startRendering(body.id, body.inputProps, accountId);
    console.log("Rendering started with ID:", renderId);
    return { renderId };
  } catch (error) {
    console.error("Error in renderMedia:", error);
    throw error;
  }
});
