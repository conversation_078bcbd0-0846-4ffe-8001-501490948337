import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { requireUser } from '@kit/supabase/require-user';

// GET /api/video-projects - List projects for current user
export async function GET(request: NextRequest) {
  try {
    const supabase = getSupabaseServerClient();
    const user = await requireUser(supabase);

    const { searchParams } = new URL(request.url);
    const accountId = searchParams.get('accountId');

    if (!accountId) {
      return NextResponse.json(
        { error: 'accountId is required' },
        { status: 400 }
      );
    }

    const { data: projects, error } = await supabase
      .from('video_projects')
      .select(`
        *,
        video_project_overlays (count),
        video_renders (
          id,
          status,
          progress,
          output_url,
          created_at
        )
      `)
      .eq('account_id', accountId)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching video projects:', error);
      return NextResponse.json(
        { error: 'Failed to fetch projects' },
        { status: 500 }
      );
    }

    return NextResponse.json({ projects });
  } catch (error) {
    console.error('Error in GET /api/video-projects:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/video-projects - Create new project
export async function POST(request: NextRequest) {
  try {
    const supabase = getSupabaseServerClient();
    const user = await requireUser(supabase);

    const body = await request.json();
    const {
      accountId,
      name,
      description,
      aspectRatio = '16:9',
      fps = 30,
      width = 1920,
      height = 1080
    } = body;

    if (!accountId || !name) {
      return NextResponse.json(
        { error: 'accountId and name are required' },
        { status: 400 }
      );
    }

    const { data: project, error } = await supabase
      .from('video_projects')
      .insert({
        account_id: accountId,
        user_id: user.id,
        name,
        description,
        aspect_ratio: aspectRatio,
        fps,
        width,
        height,
        created_by: user.id,
        updated_by: user.id
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating video project:', error);
      return NextResponse.json(
        { error: 'Failed to create project' },
        { status: 500 }
      );
    }

    return NextResponse.json({ project }, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/video-projects:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}