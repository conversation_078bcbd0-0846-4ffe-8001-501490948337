import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = getSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    console.log('Upload request - user:', user?.id);
    console.log('Environment check:', {
      NODE_ENV: process.env.NODE_ENV,
      hasSupabaseKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      hasImgbbKey: !!process.env.IMGBB_API_KEY,
      supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL
    });
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const companyId = formData.get('companyId') as string;

    console.log('Upload request details:', {
      fileName: file?.name,
      fileSize: file?.size,
      fileType: file?.type,
      companyId: companyId
    });

    if (!file) {
      console.error('No file provided in upload request');
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    if (!companyId) {
      console.error('No company ID provided in upload request');
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      );
    }

    // Verify user has access to the company
    console.log('Verifying user access to company:', companyId);
    const { data: membership, error: membershipError } = await supabase
      .from('accounts_memberships')
      .select('account_id')
      .eq('user_id', user.id)
      .eq('account_id', companyId)
      .single();

    if (membershipError || !membership) {
      console.error('User does not have access to company:', {
        userId: user.id,
        companyId,
        error: membershipError?.message,
        errorCode: membershipError?.code,
        errorDetails: membershipError?.details
      });

      // Also log all user memberships for debugging
      const { data: allMemberships } = await supabase
        .from('accounts_memberships')
        .select('account_id, user_id')
        .eq('user_id', user.id);

      console.log('User memberships:', allMemberships);

      return NextResponse.json(
        { error: 'Access denied: You do not have permission to upload to this account' },
        { status: 403 }
      );
    }

    console.log('User access verified for company:', companyId);

    // Validate file type and specific formats
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      console.error('Invalid file type:', file.type);
      return NextResponse.json(
        { error: `Only ${allowedTypes.join(', ')} files are allowed` },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      console.error('File too large:', file.size);
      return NextResponse.json(
        { error: 'File size must be less than 10MB' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Validate image buffer integrity
    try {
      // Basic image header validation
      const header = buffer.toString('hex', 0, 8);
      const isValidImage = 
        header.startsWith('ffd8ff') || // JPEG
        header.startsWith('89504e47') || // PNG
        header.startsWith('52494646') || // WebP
        header.startsWith('47494638'); // GIF
      
      if (!isValidImage) {
        console.error('Invalid image header:', header);
        return NextResponse.json(
          { error: 'Invalid or corrupted image file' },
          { status: 400 }
        );
      }
    } catch (validationError) {
      console.error('Image validation failed:', validationError);
      return NextResponse.json(
        { error: 'Failed to validate image file' },
        { status: 400 }
      );
    }

    // Upload to storage based on environment
    // Use Supabase storage if we have the service role key (production/staging/preview)
    // Otherwise use ImgBB for development
    const hasSupabaseServiceKey = !!process.env.SUPABASE_SERVICE_ROLE_KEY;
    let uploadResult;

    try {
      if (hasSupabaseServiceKey) {
        // Use Supabase storage for production/staging/preview
        console.log('Using Supabase storage for upload');
        uploadResult = await uploadToSupabase(buffer, companyId, file.name);
      } else {
        // Use ImgBB for development
        console.log('Using ImgBB for upload');
        uploadResult = await uploadToImgBB(buffer);
      }
    } catch (uploadError) {
      console.error('Upload failed:', uploadError);
      return NextResponse.json(
        { error: `Upload failed: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      url: uploadResult.url,
      path: uploadResult.path || uploadResult.url,
    });

  } catch (error) {
    console.error('Error in image upload API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Upload image to Supabase Storage (production environment)
 */
async function uploadToSupabase(imageBuffer: Buffer, companyId: string, fileName: string): Promise<{ url: string; path: string }> {
  try {

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
      throw new Error('NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables are required');
    }

    console.log('Creating Supabase client for upload with service role');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Generate unique filename with proper extension detection
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueId = crypto.randomUUID();
    
    // Detect file extension from buffer header if filename extension is unreliable
    let fileExtension = fileName.split('.').pop()?.toLowerCase() || 'png';
    const header = imageBuffer.toString('hex', 0, 8);
    if (header.startsWith('ffd8ff')) fileExtension = 'jpg';
    else if (header.startsWith('89504e47')) fileExtension = 'png';
    else if (header.startsWith('52494646')) fileExtension = 'webp';
    else if (header.startsWith('47494638')) fileExtension = 'gif';
    
    const newFileName = `${uniqueId}-${timestamp}.${fileExtension}`;
    const filePath = `${companyId}/references/${newFileName}`;

    // Map extension to proper MIME type
    const mimeTypes: Record<string, string> = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'webp': 'image/webp',
      'gif': 'image/gif'
    };
    const contentType = mimeTypes[fileExtension] || 'image/png';

    console.log('Uploading to Supabase storage:', {
      bucket: 'generated',
      filePath: filePath,
      fileSize: imageBuffer.length,
      contentType: contentType,
      detectedExtension: fileExtension
    });

    // Upload to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('generated')
      .upload(filePath, imageBuffer, {
        contentType: contentType,
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      console.error('Supabase upload error:', {
        message: uploadError.message,
        filePath: filePath,
        contentType: contentType,
        fileSize: imageBuffer.length
      });
      
      // Provide more specific error messages
      if (uploadError.message.includes('INVALID_IMAGE_OPTIMIZE_REQUEST')) {
        throw new Error('Image optimization failed: The uploaded image may be corrupted or in an unsupported format. Please try with a different image.');
      } else if (uploadError.message.includes('File size limit')) {
        throw new Error('File too large: Please upload an image smaller than 10MB.');
      } else if (uploadError.message.includes('Invalid file type')) {
        throw new Error('Invalid file type: Please upload a JPEG, PNG, WebP, or GIF image.');
      } else {
        throw new Error(`Upload failed: ${uploadError.message}`);
      }
    }

    // Create signed URL for private bucket (24 hours expiry)
    const { data: signedUrlData, error: signedUrlError } = await supabase.storage
      .from('generated')
      .createSignedUrl(filePath, 86400); // 24 hours

    if (signedUrlError || !signedUrlData?.signedUrl) {
      console.error('Error creating signed URL:', signedUrlError);
      throw new Error(`Failed to create signed URL: ${signedUrlError?.message || 'Unknown error'}`);
    }

    return { url: signedUrlData.signedUrl, path: filePath };

  } catch (error) {
    throw new Error(`Failed to upload to Supabase: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload image to ImgBB (development environment)
 */
async function uploadToImgBB(imageBuffer: Buffer): Promise<{ url: string }> {
  const imgbbApiKey = process.env.IMGBB_API_KEY;

  if (!imgbbApiKey) {
    throw new Error('IMGBB_API_KEY environment variable is required');
  }

  // Convert buffer to base64 string
  const base64Image = imageBuffer.toString('base64');

  // Create form data using URLSearchParams for Node.js compatibility
  const formData = new URLSearchParams();
  formData.append('image', base64Image);

  const imgbbRes = await fetch(`https://api.imgbb.com/1/upload?key=${imgbbApiKey}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData,
  });

  if (!imgbbRes.ok) {
    const errorText = await imgbbRes.text();
    throw new Error(`ImgBB upload failed with status ${imgbbRes.status}: ${errorText}`);
  }

  const imgbbData = await imgbbRes.json();

  if (!imgbbData.success) {
    throw new Error(`ImgBB upload reported failure: ${JSON.stringify(imgbbData)}`);
  }

  return { url: imgbbData.data.url };
}
