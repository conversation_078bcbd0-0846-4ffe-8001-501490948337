import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = getSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { messages, companyId, conversationId, originalImageUrl, referenceImages } = body;

    // Validate required fields
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    if (!companyId || !conversationId) {
      return NextResponse.json(
        { error: 'Company ID and conversation ID are required' },
        { status: 400 }
      );
    }

    if (!originalImageUrl) {
      return NextResponse.json(
        { error: 'Original image URL is required for editing' },
        { status: 400 }
      );
    }

    // Make request to the sb-server
    const serverUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const response = await fetch(`${serverUrl}/image-editing-chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages,
        userId: user.id,
        companyId,
        conversationId,
        originalImageUrl,
        referenceImages,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error from sb-server:', errorData);
      return NextResponse.json(
        { error: errorData.error || 'Failed to process image editing request' },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in image editing API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
