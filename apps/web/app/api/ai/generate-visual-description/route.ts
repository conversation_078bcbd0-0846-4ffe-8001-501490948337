// Types for visual description generation
interface VisualDescriptionOption {
  level: string;
  prompt: string;
}

interface RequestBody {
  brand_brief: string;
  content: string;
  initial_visual_desc: string;
  image_gen_styles?: string;
}

interface ResponseData {
  data: VisualDescriptionOption[];
}

/**
 * POST handler for generating visual descriptions
 * @description Generates multiple visual description options with different brand integration levels
 */
export const POST = async (request: Request): Promise<Response> => {
  try {
    const body: RequestBody = await request.json();
    
    // Validate required fields
    if (!body.brand_brief || !body.content) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: brand_brief and content are required' }), 
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' } 
        }
      );
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_PUSH_SERVER}/generate-visual-description`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brand_brief: body.brand_brief,
          content: body.content,
          initial_visual_desc: body.initial_visual_desc || "NOT_PROVIDED",
          image_gen_styles: body.image_gen_styles || "NOT_PROVIDED"
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`External API request failed with status ${response.status}`);
    }

    const data: VisualDescriptionOption[] = await response.json();
    
    const responseData: ResponseData = { data };
    
    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error generating visual description:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate visual description';
    
    return new Response(
      JSON.stringify({ error: errorMessage }), 
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    );
  }
};