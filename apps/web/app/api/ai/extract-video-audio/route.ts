import { z } from 'zod';
import { NextRequest, NextResponse } from 'next/server';

import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { getLogger } from '@kit/shared/logger';

const requestSchema = z.object({
  videoUrl: z.string().url(),
  fileName: z.string(),
  folderName: z.string(),
  companyId: z.string().uuid(),
});

async function downloadFile(url: string): Promise<Buffer> {
  const response = await fetch(url);
  return Buffer.from(await response.arrayBuffer());
}

export async function POST(request: NextRequest) {
  const logger = await getLogger();
  
  try {
    const body = await request.json();
    const { videoUrl, fileName, folderName, companyId } = requestSchema.parse(body);

    logger.info('Starting video-to-audio extraction', {
      fileName,
      folderName,
      companyId,
    });

    // Call sb-server endpoint for video-to-audio conversion
    const sbServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    
    logger.info('Calling sb-server for video-to-audio extraction', {
      sbServerUrl,
      endpoint: `${sbServerUrl}/extract-video-audio`,
    });

    const response = await fetch(`${sbServerUrl}/extract-video-audio`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        videoUrl,
        fileName,
        folderName,
        companyId,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error('sb-server video-to-audio extraction failed', {
        sbServerUrl,
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });
      throw new Error(`Video-to-audio extraction failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    
    logger.info('Video-to-audio extraction completed successfully', {
      audioFileName: result.audioFileName,
      audioUrl: result.audioUrl,
    });

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    logger.error('Error processing video-to-audio extraction', { error });
    
    return NextResponse.json(
      { error: 'Failed to extract audio from video file' },
      { status: 500 }
    );
  }
}
