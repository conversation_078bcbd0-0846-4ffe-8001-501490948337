import { NextRequest, NextResponse } from 'next/server';
import { getLogger } from '@kit/shared/logger';

export async function POST(request: NextRequest) {
  const logger = await getLogger();
  
  try {
    const body = await request.json();
    const { audioUrl, fileName, folderName, companyId, assetId } = body;

    if (!audioUrl || !fileName || !folderName || !companyId) {
      logger.error('Missing required fields for audio transcript extraction', {
        audioUrl: !!audioUrl,
        fileName: !!fileName,
        folderName: !!folderName,
        companyId: !!companyId,
      });
      
      return NextResponse.json(
        { error: 'Missing required fields: audioUrl, fileName, folderName, and companyId are required' },
        { status: 400 }
      );
    }

    // Get sb-server URL from environment
    const sbServerUrl = process.env.SB_SERVER_URL || 'http://localhost:8080';

    logger.info('Calling sb-server for audio transcript extraction', {
      sbServerUrl,
      endpoint: `${sbServerUrl}/extract-audio-transcript`,
      fileName,
      folderName,
      companyId,
      assetId,
    });

    const response = await fetch(`${sbServerUrl}/extract-audio-transcript`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        audioUrl,
        fileName,
        folderName,
        companyId,
        assetId,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error('sb-server audio transcript extraction failed', {
        sbServerUrl,
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });
      throw new Error(`Audio transcript extraction failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    
    logger.info('Audio transcript extraction completed successfully', {
      transcriptFileName: result.transcriptFileName,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in audio transcript extraction endpoint', error);
    logger.error('Error in audio transcript extraction endpoint', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to extract transcript from audio',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}