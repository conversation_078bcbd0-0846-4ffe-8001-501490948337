import { NextResponse } from 'next/server';
import { enhanceRouteHandler } from '@kit/next/routes';
import { z } from 'zod';

const TaskContentSchema = z.object({
  taskTitle: z.string(),
  taskDescription: z.string(),
  contentType: z.string().optional(),
  channel: z.string().optional(),
  campaignGoal: z.string().optional(),
  productInformation: z.string().optional(),
  targetICPs: z.string().optional(),
  targetPersonas: z.string().optional(),
  companyBrand: z.any().optional(),
  externalResearch: z.string().optional(),
});

export const POST = enhanceRouteHandler(
  async ({ body, user }) => {
    try {
      console.log('Generate task content request from user:', user.id);

      // Always use streaming for content generation
      return await handleStreamingRequest(body);
    } catch (error) {
      console.error('Error in generate-task-content API route:', error);
      return NextResponse.json(
        {
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  },
  {
    auth: true,
    schema: TaskContentSchema,
  }
);

async function handleStreamingRequest(body: any) {
  try {
    const pushServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const response = await fetch(`${pushServerUrl}/generate-task-content`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error from sb-server:', errorText);
      return NextResponse.json(
        { error: 'Failed to generate content from server', details: errorText },
        { status: response.status }
      );
    }

    const result = await response.json();
    const generatedContent = result.data?.content || result.content || '';

    if (!generatedContent) {
      console.error('No content received from sb-server');
      return NextResponse.json(
        { error: 'No content received from server' },
        { status: 500 }
      );
    }

    // Create streaming response that sends content word by word
    const encoder = new TextEncoder();
    const readableStream = new ReadableStream({
      async start(controller) {
        try {
          const words = generatedContent.split(' ');
          
          for (let i = 0; i < words.length; i++) {
            const word = words[i];
            const space = i < words.length - 1 ? ' ' : '';
            const chunkData = `data: ${JSON.stringify({ content: word + space, type: 'content' })}\n\n`;
            
            controller.enqueue(encoder.encode(chunkData));
            await new Promise(resolve => setTimeout(resolve, 50));
          }
          
          controller.enqueue(encoder.encode('data: [DONE]\n\n'));
        } catch (error) {
          console.error('Streaming error:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify({ error: 'Stream error: ' + errorMessage })}\n\n`)
          );
        } finally {
          controller.close();
        }
      },
    });

    return new NextResponse(readableStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Error in streaming request:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      { error: 'Failed to create streaming response: ' + errorMessage },
      { status: 500 }
    );
  }
}


