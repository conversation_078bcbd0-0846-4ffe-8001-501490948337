import { NextRequest, NextResponse } from 'next/server';
import type { GenerateImageRequest, GenerateImageResponse } from '@kit/shared/image-generation';

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const body: GenerateImageRequest = await request.json();

    if (!body.image_prompt || !body.company_id) {
      return NextResponse.json(
        { error: 'Missing required fields: image_prompt and company_id are required' },
        { status: 400 }
      );
    }

    const sbServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const response = await fetch(`${sbServerUrl}/generate-images`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        image_prompt: body.image_prompt,
        aspect_ratio: body.aspect_ratio ?? 'custom',
        company_id: body.company_id,
        image_gen_styles: body.image_gen_styles,
        brand_context: body.brand_context,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        `sb-server request failed with status ${response.status}: ${errorData.message || 'Unknown error'}`
      );
    }

    const data = (await response.json()) as GenerateImageResponse;
    return NextResponse.json({ url: data.url, path: data.path } satisfies GenerateImageResponse);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate image';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}