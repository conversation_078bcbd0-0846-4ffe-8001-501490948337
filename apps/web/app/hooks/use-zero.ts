'use client';
import { useEffect, useMemo, useState } from 'react';
import { Zero } from '@rocicorp/zero';
import { createUseZero } from '@rocicorp/zero/react';
import { schema } from '@kit/zero-schema';
import { useUser, useUserSession } from '@kit/supabase/hooks/use-user';

import { createMutators } from '@kit/zero-schema';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

// Create a typed useZero hook
export const useZero = createUseZero<typeof schema, ReturnType<typeof createMutators>>();

// Custom hook to create and manage Zero instance
export function useZeroInstance() {
  const workspace = useTeamAccountWorkspace();
  const userId = workspace.user?.id;
  const userSession = useUserSession();
  const access_token = userSession.data?.access_token;

  const z =
   useMemo(() => {
    // Only create Zero instance if we have required data
    if (!userId || !access_token) {
      return null;
    }
    
    return new Zero({
      userID: userId,
      auth: access_token,
      server: process.env.NEXT_PUBLIC_ZERO_SERVER,
      mutators: createMutators(),
      schema,
    });

  }, [userId, access_token])
  
  return z;
}
