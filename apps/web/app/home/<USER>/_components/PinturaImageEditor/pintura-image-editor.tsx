// index.js
import { useRef } from 'react';
import { pintura } from './pintura.css';
import pinturaTheme from './pintura.module.css';
// Import the editor default configuration
import { getEditorDefaults } from '@pqina/pintura';

// Import the editor component from `react-pintura`
import { PinturaEditor } from '@pqina/react-pintura';

// get default properties
const editorConfig = getEditorDefaults();

interface PinturaImageEditorProps {
  toggleEditing: () => void;
  imageSrc: string;
}

export default function PinturaImageEditor({ toggleEditing, imageSrc }: PinturaImageEditorProps) {
    const editorRef = useRef(null);

    // const handleButtonClick = () => {
    //     editorRef.current?.editor
    //         .editImage('image.jpeg')
    //         .then((imageReaderResult) => {
    //             // Logs loaded image data
    //             console.log(imageReaderResult);
    //         });
    // };
    return (
        <div className="w-full h-full"> 
        <PinturaEditor
            ref={editorRef}
            onProcess={toggleEditing}
            className={`${pintura} ${pinturaTheme.index || ''}`}
            {...editorConfig}
            src={imageSrc}
            imageCropAspectRatio={1}
        />
        </div>
    )
        
}