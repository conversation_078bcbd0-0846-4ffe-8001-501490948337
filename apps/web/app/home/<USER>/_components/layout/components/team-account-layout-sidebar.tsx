'use client'
import type { User } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  useSidebar,
} from '@kit/ui/shadcn-sidebar';
import { But<PERSON> } from '@kit/ui/button';
import { PlusIcon } from 'lucide-react';

import { ProfileAccountDropdownContainer } from '~/components/personal-account-dropdown-container';
import { TrialStatusBadgeCompact } from '~/components/trial-status-badge';
import { getTeamAccountSidebarConfig } from '~/config/team-account-navigation.config';
import { TeamAccountNotifications } from '../../navigation';
import { TeamAccountAccountsSelector } from '../../navigation';
import { TeamAccountLayoutSidebarNavigation } from './team-account-layout-sidebar-navigation';
import { OnboardingChecklist } from '../../dashboard-components/onboarding-checklist';

type AccountModel = {
  label: string | null;
  value: string | null;
  image: string | null;
};

export function TeamAccountLayoutSidebar(props: {
  account: string;
  accountId: string;
  accounts: AccountModel[];
  user: User;
}) {
  return (
    <SidebarContainer
      account={props.account}
      accountId={props.accountId}
      accounts={props.accounts}
      user={props.user}
    />
  );
}

function SidebarContainer(props: {
  account: string;
  accountId: string;
  accounts: AccountModel[];
  user: User;
}) {
  const { account, accounts, user } = props;
  const userId = user.id;
  const router = useRouter();
  const config = getTeamAccountSidebarConfig(account);
  const collapsible = config.sidebarCollapsedStyle;
  const { open } = useSidebar();
  
  return (
    <Sidebar collapsible={collapsible}>
      <SidebarHeader className={'min-h-20 max-h-20 justify-center flex-shrink-0'}>
        <div className={'flex flex-col gap-2'}>
          <div className={'flex flex-row items-center justify-between gap-x-3'}>
            <TeamAccountAccountsSelector
              userId={userId}
              selectedAccount={account}
              accounts={accounts}
            />

            <div className={'group-data-[minimized=true]:hidden'}>
              <TeamAccountNotifications
                userId={userId}
                accountId={props.accountId}
              />
            </div>
          </div>

          {/* Trial Status Badge */}
          <div className={'group-data-[minimized=true]:hidden'}>
            <TrialStatusBadgeCompact />
          </div>
        </div>
      </SidebarHeader>
     
      <SidebarContent className="mt-5 flex flex-col h-full overflow-hidden">
        {/* Create button section */}
        <div className="flex-shrink-0 px-2 pb-4">
          <Button
            onClick={() => router.push(`/home/<USER>/create`)}
            variant="outline"
            size={open ? "default" : "icon"}
            className={open ? 'w-full' : 'w-9 h-9 mx-auto p-0'}
          >
            <PlusIcon className="h-4 w-4" />
            {open && <span className="ml-2">Create</span>}
          </Button>
        </div>

        {/* Navigation section - scrollable */}
        <div className="flex-1 overflow-y-auto min-h-0">
          <TeamAccountLayoutSidebarNavigation config={config} />
        </div>

        {/* Bottom section with onboarding checklist - only show when expanded and enough space */}
        {open && (
          <div className="flex-shrink-0 mt-auto pt-4 border-t border-border max-h-48 overflow-y-auto">
            <div className="px-4">
              <OnboardingChecklist />
            </div>
          </div>
        )}
      </SidebarContent>

      <SidebarFooter className="flex-shrink-0">
        <ProfileAccountDropdownContainer user={props.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
