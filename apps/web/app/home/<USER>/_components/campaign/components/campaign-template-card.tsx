'use client';

import { Card, CardContent } from '@kit/ui/card';
import { 
  Target, 
  TrendingUp, 
  Users, 
  Zap,
  Megaphone,
  Heart,
  ShoppingCart,
  Award,
  Calendar,
  Settings
} from 'lucide-react';
import type { CampaignTemplate } from '../types';

interface CampaignTemplateCardProps {
  template: CampaignTemplate;
  onQuickStart: (template: CampaignTemplate) => void;
  disabled?: boolean;
}

// Function to get the appropriate icon based on campaign template type or title
const getCampaignTemplateIcon = (template: CampaignTemplate) => {
  const title = template.title?.toLowerCase() || '';
  const goal = template.goal?.toLowerCase() || '';
  
  // Handle the custom template case
  if (template.id === 'custom') {
    return Settings;
  }

  // Icon mapping based on title or goal keywords
  if (title.includes('launch') || title.includes('product') || goal.includes('launch')) {
    return Target;
  }
  if (title.includes('growth') || title.includes('scale') || goal.includes('growth')) {
    return TrendingUp;
  }
  if (title.includes('engagement') || title.includes('community') || goal.includes('engagement')) {
    return Users;
  }
  if (title.includes('brand') || title.includes('awareness') || goal.includes('brand')) {
    return Megaphone;
  }
  if (title.includes('retention') || title.includes('loyalty') || goal.includes('retention')) {
    return Heart;
  }
  if (title.includes('sales') || title.includes('conversion') || goal.includes('sales')) {
    return ShoppingCart;
  }
  if (title.includes('event') || title.includes('webinar') || goal.includes('event')) {
    return Calendar;
  }
  if (title.includes('award') || title.includes('recognition') || goal.includes('award')) {
    return Award;
  }
  if (title.includes('quick') || title.includes('boost') || goal.includes('quick')) {
    return Zap;
  }
  
  // Default icon
  return Target;
};

export const CampaignTemplateCard = ({ template, onQuickStart, disabled = false }: CampaignTemplateCardProps) => {
  const IconComponent = getCampaignTemplateIcon(template);

  return (
    <Card 
      className={`group transition-all duration-300 p-4 h-full flex flex-col ${
        disabled 
          ? 'opacity-50 cursor-not-allowed bg-muted/20' 
          : 'cursor-pointer hover:shadow-md hover:border-primary/30 bg-muted/30'
      }`}
      onClick={disabled ? undefined : () => onQuickStart(template)}
    >
      <CardContent className="p-0 h-full flex flex-col">
        <div className="flex flex-col h-full text-left">
          {/* Icon - First Row */}
          <div className="mb-3 flex-shrink-0">
            <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
              <IconComponent className="h-5 w-5 text-primary" />
            </div>
          </div>

          {/* Title - Second Row */}
          <div className="mb-2 flex-shrink-0">
            <h3 className="text-base font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
              {template.title}
            </h3>
          </div>

          {/* Description - Third Row */}
          <div className="flex-1 flex items-start">
            <p className="text-sm text-muted-foreground leading-relaxed line-clamp-3">
              {template.description || 'Create a powerful marketing campaign with this template'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
