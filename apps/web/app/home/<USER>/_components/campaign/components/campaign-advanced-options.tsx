'use client';

import { Label } from '@kit/ui/label';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { Checkbox } from '@kit/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { ChevronDown, Check } from 'lucide-react';
import type { CampaignFormData } from '../types';

interface CampaignAdvancedOptionsProps {
  formData: CampaignFormData;
  // savedResearch: any[];
  products: any[];
  // icps: any[];
  // personas: any[];
  // onResearchToggle: (researchId: string, checked: boolean) => void;
  onProductToggle: (productId: string, checked: boolean) => void;
  // onIcpToggle: (icpId: string, checked: boolean) => void;
  // onPersonaToggle: (personaId: string, checked: boolean) => void;
}

export const CampaignAdvancedOptions = ({
  formData,
  // savedResearch,
  products,
  // icps,
  // personas,
  // onResearchToggle,
  onProductToggle,
  // onIcpToggle,
  // onPersonaToggle,
}: CampaignAdvancedOptionsProps) => {
  return (
    <div className="space-y-4 pl-2 border-l-2 border-muted">
      {/* Products */}
      <div className="space-y-2">
        <Label htmlFor="products">
          <Trans i18nKey="dashboard:products" defaults="Products" />
        </Label>
        <p className="text-xs text-muted-foreground">
          <Trans 
            i18nKey="dashboard:productsDescription" 
            defaults="Choose products to reference in your campaign content" 
          />
        </p>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between"
              role="combobox"
            >
              <span className="truncate">
                {formData.products.length === 0
                  ? "Select product options"
                  : `${formData.products.length} selected`}
              </span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <div 
              className="max-h-60 overflow-y-auto overscroll-contain"
              style={{ scrollBehavior: 'smooth' }}
              onWheel={(e) => {
                // Ensure wheel events are handled properly within this container
                e.stopPropagation();
              }}
            >
              {(!products || products.length === 0) ? (
                <div className="p-3 text-sm text-muted-foreground">
                  <Trans 
                    i18nKey="dashboard:noProducts" 
                    defaults="No products available" 
                  />
                </div>
              ) : (
                <div className="p-2 space-y-1">
                  {products.map((product: any) => (
                    <div
                      key={product.id}
                      className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                      onClick={() => 
                        onProductToggle(
                          product.id, 
                          !formData.products.includes(product.id)
                        )
                      }
                    >
                      <Checkbox
                        checked={formData.products.includes(product.id)}
                      />
                      <div className="flex-1">
                        <span className="text-sm font-medium">{product.name}</span>
                        {product.description && (
                          <p className="text-xs text-muted-foreground truncate">
                            {product.description.slice(0, 60)}...
                          </p>
                        )}
                      </div>
                      {formData.products.includes(product.id) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* External Research */}
      {/* <div className="space-y-2">
        <Label htmlFor="external-research">
          <Trans i18nKey="dashboard:externalResearch" defaults="External Research" />
        </Label>
        <p className="text-xs text-muted-foreground">
          <Trans 
            i18nKey="dashboard:externalResearchDescription" 
            defaults="Select market research data to inform your campaign strategy" 
          />
        </p>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between"
              role="combobox"
            >
              <span className="truncate">
                {formData.externalResearch.length === 0
                  ? "Select research options"
                  : `${formData.externalResearch.length} selected`}
              </span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <div 
              className="max-h-60 overflow-y-auto overscroll-contain"
              style={{ scrollBehavior: 'smooth' }}
              onWheel={(e) => {
                // Ensure wheel events are handled properly within this container
                e.stopPropagation();
              }}
            >
              {(!savedResearch || savedResearch.length === 0) ? (
                <div className="p-3 text-sm text-muted-foreground">
                  <Trans 
                    i18nKey="dashboard:noResearch" 
                    defaults="No Market research available" 
                  />
                </div>
              ) : (
                <div className="p-2 space-y-1">
                  {savedResearch.map((research: any) => (
                    <div
                      key={research.id}
                      className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                      onClick={() => 
                        onResearchToggle(
                          research.id, 
                          !formData.externalResearch.includes(research.id)
                        )
                      }
                    >
                      <Checkbox
                        checked={formData.externalResearch.includes(research.id)}
                      />
                      <span className="text-sm flex-1">{research.title}</span>
                      {formData.externalResearch.includes(research.id) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div> */}

      {/* ICP Selection */}
      {/* <div className="space-y-2">
        <Label htmlFor="icp-selection">
          <Trans i18nKey="dashboard:icpSelection" defaults="ICP Selection" />
        </Label>
        <p className="text-xs text-muted-foreground">
          <Trans 
            i18nKey="dashboard:icpSelectionDescription" 
            defaults="Select ideal customer profiles to target in your campaign" 
          />
        </p>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between"
              role="combobox"
            >
              <span className="truncate">
                {formData.icps.length === 0
                  ? "Select ICP options"
                  : `${formData.icps.length} selected`}
              </span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <div 
              className="max-h-60 overflow-y-auto overscroll-contain"
              style={{ scrollBehavior: 'smooth' }}
              onWheel={(e) => {
                // Ensure wheel events are handled properly within this container
                e.stopPropagation();
              }}
            >
              {(!icps || icps.length === 0) ? (
                <div className="p-3 text-sm text-muted-foreground">
                  <Trans 
                    i18nKey="dashboard:noIcps" 
                    defaults="No ICPs available" 
                  />
                </div>
              ) : (
                <div className="p-2 space-y-1">
                  {icps.map((icp: any) => (
                    <div
                      key={icp.id}
                      className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                      onClick={() => 
                        onIcpToggle(
                          icp.id, 
                          !formData.icps.includes(icp.id)
                        )
                      }
                    >
                      <Checkbox
                        checked={formData.icps.includes(icp.id)}
                      />
                      <span className="text-sm flex-1">{icp.name || icp.title}</span>
                      {formData.icps.includes(icp.id) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div> */}

      {/* Persona Selection */}
      {/* <div className="space-y-2">
        <Label htmlFor="persona-selection">
          <Trans i18nKey="dashboard:personaSelection" defaults="Persona Selection" />
        </Label>
        <p className="text-xs text-muted-foreground">
          <Trans 
            i18nKey="dashboard:personaSelectionDescription" 
            defaults="Select personas to tailor your campaign messaging" 
          />
        </p>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between"
              role="combobox"
            >
              <span className="truncate">
                {formData.personas.length === 0
                  ? "Select persona options"
                  : `${formData.personas.length} selected`}
              </span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0" align="start">
            <div 
              className="max-h-60 overflow-y-auto overscroll-contain"
              style={{ scrollBehavior: 'smooth' }}
              onWheel={(e) => {
                // Ensure wheel events are handled properly within this container
                e.stopPropagation();
              }}
            >
              {(!personas || personas.length === 0) ? (
                <div className="p-3 text-sm text-muted-foreground">
                  <Trans 
                    i18nKey="dashboard:noPersonas" 
                    defaults="No personas available" 
                  />
                </div>
              ) : (
                <div className="p-2 space-y-1">
                  {personas.map((persona: any) => (
                    <div
                      key={persona.id}
                      className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                      onClick={() => 
                        onPersonaToggle(
                          persona.id, 
                          !formData.personas.includes(persona.id)
                        )
                      }
                    >
                      <Checkbox
                        checked={formData.personas.includes(persona.id)}
                      />
                      <span className="text-sm flex-1">{persona.name || persona.title}</span>
                      {formData.personas.includes(persona.id) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </PopoverContent>
        </Popover>
      </div> */}
    </div>
  );
};
