'use client';

import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from '@kit/ui/dialog';
import { Input } from '@kit/ui/input';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@kit/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@kit/ui/popover';
import { Checkbox } from '@kit/ui/checkbox';
import { Plus, Loader2, ChevronDown, Check } from 'lucide-react';
import type { CampaignFormData } from '../types';

interface CampaignCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  formData: CampaignFormData;
  onFormChange: (field: keyof CampaignFormData, value: string | string[] | number) => void;
  onCreateCampaign: () => void;
  isCreating: boolean;
  products: any[];
  onProductToggle: (productId: string, checked: boolean) => void;
}

const getColorClass = (color: string) => {
  const colorMap: Record<string, string> = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
    pink: 'bg-pink-500',
    indigo: 'bg-indigo-500',
  };
  return colorMap[color] || 'bg-gray-500';
};

export const CampaignCreationModal = ({
  isOpen,
  onClose,
  formData,
  onFormChange,
  onCreateCampaign,
  isCreating,
  products,
  onProductToggle,
}: CampaignCreationModalProps) => {

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-md w-[95vw] max-w-[95vw] sm:w-full">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="dashboard:createCampaign" defaults="Create Campaign" />
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          {/* Objective */}
          <div className="space-y-2">
            <Label htmlFor="objective">
              <Trans i18nKey="dashboard:objective" defaults="Objective" />
            </Label>
            <Textarea
              id="objective"
              value={formData.objective}
              onChange={(e) => onFormChange('objective', e.target.value)}
              placeholder="Campaign objective"
              rows={3}
            />
          </div>

          {/* Channels */}
          <div className="space-y-2">
            <Label htmlFor="channels">
              <Trans i18nKey="dashboard:channels" defaults="Channels" />
            </Label>
            <p className="text-xs text-muted-foreground">
              <Trans 
                i18nKey="dashboard:channelsDescription" 
                defaults="Choose which channels to publish your campaign content to" 
              />
            </p>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  role="combobox"
                >
                  <span className="truncate">
                    {formData.channels.length === 0
                      ? "Select channels"
                      : `${formData.channels.length} selected`}
                  </span>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0" align="start">
                <div className="p-2 space-y-1">
                  {[
                    { id: 'x', name: 'X (formerly Twitter)' },
                    { id: 'linkedin', name: 'LinkedIn' },
                    { id: 'blog', name: 'Blog' }
                  ].map((channel) => (
                    <div
                      key={channel.id}
                      className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                      onClick={() => {
                        const isSelected = formData.channels.includes(channel.id);
                        const newChannels = isSelected
                          ? formData.channels.filter(id => id !== channel.id)
                          : [...formData.channels, channel.id];
                        onFormChange('channels', newChannels);
                      }}
                    >
                      <Checkbox
                        checked={formData.channels.includes(channel.id)}
                      />
                      <span className="text-sm flex-1">{channel.name}</span>
                      {formData.channels.includes(channel.id) && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </div>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
            <p className="text-xs text-muted-foreground italic">
              <Trans i18nKey="dashboard:moreChannelsComingSoon" defaults="More options coming soon" />
            </p>
          </div>

          {/* Posts Per Week */}
          <div className="space-y-2">
            <Label htmlFor="posts-per-week">
              <Trans i18nKey="dashboard:postsPerWeek" defaults="Posts Per Week" />
            </Label>
            <p className="text-xs text-muted-foreground">
              <Trans 
                i18nKey="dashboard:postsPerWeekDescription" 
                defaults="How many posts to create per week (maximum 21 posts)" 
              />
            </p>
            <Input
              id="posts-per-week"
              type="text"
              value={formData.postsPerWeek}
              onChange={(e) => {
                const value = e.target.value;
                // Parse to number if it's a valid number, otherwise keep as string for validation
                const numericValue = value === '' ? '' : (isNaN(parseInt(value)) ? value : parseInt(value));
                onFormChange('postsPerWeek', numericValue);
              }}
              placeholder="Enter number of posts per week"
            />
            {(typeof formData.postsPerWeek === 'number' ? formData.postsPerWeek : parseInt(formData.postsPerWeek)) > 21 && (
              <p className="text-xs text-red-500">
                <Trans 
                  i18nKey="dashboard:postsPerWeekError" 
                  defaults="Maximum 21 posts per week allowed" 
                />
              </p>
            )}
          </div>

          {/* Color Tag */}
          <div className="space-y-2">
            <Label htmlFor="color-tag">
              <Trans i18nKey="dashboard:colorTag" defaults="Color Tag" />
            </Label>
            <p className="text-xs text-muted-foreground">
              <Trans 
                i18nKey="dashboard:colorTagDescription" 
                defaults="Choose a color to help differentiate this campaign in your calendar" 
              />
            </p>
            <Select value={formData.colorTag} onValueChange={(value) => onFormChange('colorTag', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a color">
                  {formData.colorTag && (
                    <div className="flex items-center gap-2">
                      <div 
                        className={`w-3 h-3 rounded-full ${getColorClass(formData.colorTag)}`}
                      />
                      <span className="capitalize">{formData.colorTag}</span>
                    </div>
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="blue">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500" />
                    <span>Blue</span>
                  </div>
                </SelectItem>
                <SelectItem value="green">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-green-500" />
                    <span>Green</span>
                  </div>
                </SelectItem>
                <SelectItem value="red">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500" />
                    <span>Red</span>
                  </div>
                </SelectItem>
                <SelectItem value="purple">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-purple-500" />
                    <span>Purple</span>
                  </div>
                </SelectItem>
                <SelectItem value="orange">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-orange-500" />
                    <span>Orange</span>
                  </div>
                </SelectItem>
                <SelectItem value="pink">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-pink-500" />
                    <span>Pink</span>
                  </div>
                </SelectItem>
                <SelectItem value="indigo">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-indigo-500" />
                    <span>Indigo</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Products */}
          <div className="space-y-2">
            <Label htmlFor="products">
              <Trans i18nKey="dashboard:products" defaults="Products" />
            </Label>
            <p className="text-xs text-muted-foreground">
              <Trans 
                i18nKey="dashboard:productsDescription" 
                defaults="Choose products to reference in your campaign content" 
              />
            </p>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between"
                  role="combobox"
                >
                  <span className="truncate">
                    {formData.products.length === 0
                      ? "Select product options"
                      : `${formData.products.length} selected`}
                  </span>
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0" align="start">
                <div 
                  className="max-h-60 overflow-y-auto overscroll-contain"
                  style={{ scrollBehavior: 'smooth' }}
                  onWheel={(e) => {
                    // Ensure wheel events are handled properly within this container
                    e.stopPropagation();
                  }}
                >
                  {(!products || products.length === 0) ? (
                    <div className="p-3 text-sm text-muted-foreground">
                      <Trans 
                        i18nKey="dashboard:noProducts" 
                        defaults="No products available" 
                      />
                    </div>
                  ) : (
                    <div className="p-2 space-y-1">
                      {products.map((product: any) => (
                        <div
                          key={product.id}
                          className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                          onClick={() => 
                            onProductToggle(
                              product.id, 
                              !formData.products.includes(product.id)
                            )
                          }
                        >
                          <Checkbox
                            checked={formData.products.includes(product.id)}
                          />
                          <div className="flex-1">
                            <span className="text-sm font-medium">{product.name}</span>
                            {product.description && (
                              <p className="text-xs text-muted-foreground truncate">
                                {product.description.slice(0, 60)}...
                              </p>
                            )}
                          </div>
                          {formData.products.includes(product.id) && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="start-date">
              <Trans i18nKey="dashboard:startDate" defaults="Posting Start Date" />
            </Label>
            <Input
              id="start-date"
              type="date"
              value={formData.startDate}
              onChange={(e) => onFormChange('startDate', e.target.value)}
            />
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <Label htmlFor="end-date">
              <Trans i18nKey="dashboard:endDate" defaults="Posting End Date" />
            </Label>
            <p className="text-xs text-muted-foreground">
              <Trans 
                i18nKey="dashboard:endDateDescription" 
                defaults="When should the campaign content posting end?" 
              />
            </p>
            <Input
              id="end-date"
              type="date"
              value={formData.endDate}
              min={formData.startDate} // Ensure end date is after start date
              onChange={(e) => onFormChange('endDate', e.target.value)}
            />
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              <Trans i18nKey="dashboard:cancel" defaults="Cancel" />
            </Button>
            <Button
              onClick={onCreateCampaign}
              className="flex-1"
              disabled={!formData.objective.trim() || formData.channels.length === 0 || !formData.colorTag || formData.postsPerWeek < 1 || !formData.endDate || isCreating}
            >
              {isCreating ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              <Trans i18nKey="dashboard:createContentSchedule" defaults="Create Content Schedule" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
