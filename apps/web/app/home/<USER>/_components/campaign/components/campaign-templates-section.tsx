'use client';

import { Trans } from '@kit/ui/trans';
import { CampaignTemplateCard } from './campaign-template-card';
import type { CampaignTemplate } from '../types';

interface CampaignTemplatesSectionProps {
  campaignTemplates: CampaignTemplate[];
  onQuickStartCampaign: (template: CampaignTemplate) => void;
  disabled?: boolean;
}

export const CampaignTemplatesSection = ({ 
  campaignTemplates, 
  onQuickStartCampaign,
  disabled = false
}: CampaignTemplatesSectionProps) => {
  // Create custom template
  const customTemplate: CampaignTemplate = {
    id: 'custom',
    title: 'Custom',
    description: 'Create a custom campaign with your own requirements',
    goal: '',
    duration_weeks: null,
    image_url: null,
    created_at: Date.now(),
    style: {}
  };

  // Combine custom template with database templates
  const allTemplates = [customTemplate, ...campaignTemplates];

  return (
    <div className="mb-8">
      {/* Grid Layout - responsive: 1 column on mobile, 2 on tablet, 3 on medium, 4 on desktop, max 4 per row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-fr">
        {allTemplates.map((template: any) => (
          <CampaignTemplateCard
            key={template.id}
            template={template}
            onQuickStart={onQuickStartCampaign}
            disabled={disabled}
          />
        ))}
      </div>
    </div>
  );
};
