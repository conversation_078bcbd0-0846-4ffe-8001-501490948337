import type { ReadonlyJSONValue } from '@rocicorp/zero';

export interface CampaignTemplate {
  id: string;
  title: string;
  description: string | undefined;
  goal: string;
  duration_weeks: number | null;
  image_url: string | null;
  created_at: number;
  style: ReadonlyJSONValue;
}

export interface CampaignFormData {
  objective: string;
  channels: string[];
  postsPerWeek: number;
  colorTag: string;
  startDate: string;
  endDate: string;
  externalResearch: string[];
  products: string[];
  icps: string[];
  personas: string[];
  templateId: string;
}

export interface PostTemplate {
  id: string;
  title: string;
  description: string | null;
  image_url: string | null;
  created_at: number;
  style: ReadonlyJSONValue;
  content_type: string | null;
  channel: string | null;
}
