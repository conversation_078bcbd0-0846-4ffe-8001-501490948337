'use client';

import { AlertCircle } from 'lucide-react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@kit/ui/tooltip';

export function BrandAlert() {
  const { account } = useTeamAccountWorkspace();
  const zero = useZero();
  
  const [companyBrand] = useZeroQuery(
    zero.query.company_brand.where('company_id', '=', account?.id),
    {
      ttl: "10m"
    }
  );

  const [icps] = useZeroQuery(
    zero.query.icps.where("company_id", "=", account?.id),
    {
      ttl: "10m"
    }
  );

  // If no brand exists at all
  if (!companyBrand || companyBrand.length === 0) {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="">
              <AlertCircle className="h-4 w-4 text-red-500" />
            </div>
          </TooltipTrigger>
          <TooltipContent
            side="right"
            sideOffset={10}
            className="bg-gray-900 text-white text-xs px-2 py-1 rounded-md z-[9999] border border-gray-700 max-w-xs"
          >
            <p>You need to create your brand profile in order to create campaigns that are not AI slop</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // If brand exists but no ICPs (sections incomplete)
  if (!icps || icps.length === 0) {
    return (
      <TooltipProvider delayDuration={50}>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="">
              <AlertCircle className="h-4 w-4 text-red-500" />
            </div>
          </TooltipTrigger>
          <TooltipContent
            side="right"
            sideOffset={10}
            className="bg-gray-900 text-white text-xs px-2 py-1 rounded-md z-[9999] border border-gray-700 max-w-xs"
          >
            <p>Complete the sections in the Brand so you are not producing AI Slop</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Brand exists and sections are complete - no alert
  return null;
}
