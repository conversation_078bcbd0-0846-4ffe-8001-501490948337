'use client';

import { AlertCircle } from 'lucide-react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@kit/ui/tooltip';

export function ProductInfoAlert() {
  const { account } = useTeamAccountWorkspace();
  const zero = useZero();
  
  const [products] = useZeroQuery(zero.query.products.where('company_id', account?.id), {
    ttl: "10m"
  });

  // Only show alert if no products exist
  if (products && products.length > 0) {
    return null;
  }

  return (
    <TooltipProvider delayDuration={50}>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="">
            <AlertCircle className="h-4 w-4 text-red-500" />
          </div>
        </TooltipTrigger>
        <TooltipContent
          side="right"
          sideOffset={10}
          className="bg-gray-900 text-white text-xs px-2 py-1 rounded-md z-[9999] border border-gray-700 max-w-xs"
        >
          <p>You need to add at least 1 product in order to create campaigns that are not AI slop</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
