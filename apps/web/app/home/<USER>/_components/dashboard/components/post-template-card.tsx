'use client';

import { Card, CardContent } from '@kit/ui/card';
import { Trans } from '@kit/ui/trans';
import { 
  FileText, 
  Video, 
  Image as ImageIcon, 
  MessageSquare, 
  Share2,
  Megaphone,
  TrendingUp,
  Users
} from 'lucide-react';
import type { PostTemplate } from '../../campaign/types';

interface PostTemplateCardProps {
  template: PostTemplate;
  onQuickStart: (template: PostTemplate) => void;
}

// Function to get the appropriate icon based on template type or title
const getTemplateIcon = (template: PostTemplate) => {
  const title = template.title?.toLowerCase() || '';
  const contentType = template.content_type?.toLowerCase() || '';
  const channel = template.channel?.toLowerCase() || '';

  // Icon mapping based on content type, channel, or title keywords
  if (contentType.includes('video') || title.includes('video')) {
    return Video;
  }
  if (contentType.includes('image') || title.includes('image') || title.includes('photo')) {
    return ImageIcon;
  }
  if (title.includes('story') || title.includes('stories')) {
    return MessageSquare;
  }
  if (title.includes('share') || title.includes('social')) {
    return Share2;
  }
  if (title.includes('announcement') || title.includes('news')) {
    return Megaphone;
  }
  if (title.includes('growth') || title.includes('trend')) {
    return TrendingUp;
  }
  if (title.includes('community') || title.includes('audience')) {
    return Users;
  }
  
  // Default icon
  return FileText;
};

export const PostTemplateCard = ({ template, onQuickStart }: PostTemplateCardProps) => {
  const IconComponent = getTemplateIcon(template);

  return (
    <Card 
      className="group cursor-pointer transition-all duration-300 hover:shadow-md hover:border-primary/30 p-4 h-full bg-muted/30 flex flex-col"
      onClick={() => onQuickStart(template)}
    >
      <CardContent className="p-0 h-full flex flex-col">
        <div className="flex flex-col h-full text-left">
          {/* Icon - First Row */}
          <div className="mb-3 flex-shrink-0">
            <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
              <IconComponent className="h-5 w-5 text-primary" />
            </div>
          </div>

          {/* Title - Second Row */}
          <div className="mb-2 flex-shrink-0">
            <h3 className="text-base font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
              {template.title}
            </h3>
          </div>

          {/* Description - Third Row */}
          <div className="flex-1 flex items-start">
            <p className="text-sm text-muted-foreground leading-relaxed line-clamp-3">
              {template.description || 'Create engaging content with this template'}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
