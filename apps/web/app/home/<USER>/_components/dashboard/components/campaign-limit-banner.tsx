'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Clock, CreditCard } from 'lucide-react';
import { Alert, AlertDescription } from '@kit/ui/alert';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

interface CampaignLimitBannerProps {
  nextResetAt: number;
  onUpgrade?: () => void;
}

export function CampaignLimitBanner({ nextResetAt, onUpgrade }: CampaignLimitBannerProps) {
  const resetDate = new Date(nextResetAt);
  const now = new Date();
  const timeUntilReset = resetDate.getTime() - now.getTime();
  
  // Format the reset date
  const formatResetDate = () => {
    const days = Math.ceil(timeUntilReset / (1000 * 60 * 60 * 24));
    
    if (days <= 1) {
      return resetDate.toLocaleDateString(undefined, {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return resetDate.toLocaleDateString(undefined, {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    }
  };

  return (
    <Alert className="mb-6 border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950">
      <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
      <AlertDescription className="flex items-center justify-between w-full">
        <div className="flex-1">
          <p className="text-orange-800 dark:text-orange-200 font-medium mb-1">
            <Trans i18nKey="dashboard:campaignLimitReached" defaults="Campaign creation limit reached" />
          </p>
          <p className="text-orange-700 dark:text-orange-300 text-sm">
            <Trans 
              i18nKey="dashboard:campaignLimitMessage" 
              defaults="You've used up your campaign creations for this billing period. Your limit will reset on {resetDate} or you can upgrade your plan."
              values={{ resetDate: formatResetDate() }}
            />
          </p>
        </div>
        
        <div className="flex items-center gap-2 ml-4">
          <div className="flex items-center text-orange-700 dark:text-orange-300 text-sm">
            <Clock className="h-4 w-4 mr-1" />
            <span>Resets {formatResetDate()}</span>
          </div>
          
          {onUpgrade && (
            <Button 
              onClick={onUpgrade}
              size="sm"
              className="bg-orange-600 hover:bg-orange-700 text-white"
            >
              <CreditCard className="h-4 w-4 mr-2" />
              <Trans i18nKey="dashboard:upgradePlan" defaults="Upgrade Plan" />
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}
