'use client';

import { Trans } from '@kit/ui/trans';
import { PostTemplateCard } from './post-template-card';
import type { PostTemplate } from '../../campaign/types';

interface PostTemplatesSectionProps {
  postTemplates: PostTemplate[];
  onQuickStartPost: (template: PostTemplate) => void;
}

export const PostTemplatesSection = ({ 
  postTemplates, 
  onQuickStartPost 
}: PostTemplatesSectionProps) => {
  if (!postTemplates || postTemplates.length === 0) {
    return null;
  }

  // Limit to maximum 4 templates
  const limitedTemplates = postTemplates.slice(0, 4);

  return (
    <div className="mb-8">
      <div className="mb-6">
        <h2 className="text-2xl font-bold tracking-tight mb-2">
          <Trans i18nKey="dashboard:postQuickStart" defaults="Post Quick Start" />
        </h2>
        <p className="text-md text-muted-foreground">
          <Trans 
            i18nKey="dashboard:postQuickStartSubtitle" 
            defaults="Create engaging posts using proven templates" 
          />
        </p>
      </div>
      
      {/* Grid Layout - responsive: 1 column on mobile, 2 on tablet, 3 on medium, 4 on desktop, max 4 per row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 auto-rows-fr">
        {limitedTemplates.map((template: any) => (
          <PostTemplateCard
            key={template.id}
            template={template}
            onQuickStart={onQuickStartPost}
          />
        ))}
      </div>
    </div>
  );
};
