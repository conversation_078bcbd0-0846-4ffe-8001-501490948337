"use client"

import { ColumnDef } from "@tanstack/react-table"
import { Circle } from "lucide-react"
import { formatRelativeTime } from "@kit/shared/utils"
import { Trans } from "@kit/ui/trans"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select"
import { AssigneeAvatar } from "../../../../tasks/components/assignee-avatar"
import { TaskTitleButtonProps, RecentActivityColumnsProps } from "./types"
import { memo, useState } from "react"
import { useZero } from "~/hooks/use-zero"
import { useQuery } from "@tanstack/react-query"
import { getCompanyTaskStatuses, TaskStatus } from "~/services/task-status"
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace"

// Memoized clickable task title component for performance
const TaskTitleButton = memo<TaskTitleButtonProps>(({ task, title, onTaskClick }) => (
  <button
    onClick={() => onTaskClick?.(task)}
    className="max-w-[200px] truncate font-medium text-sm hover:text-primary hover:underline transition-colors cursor-pointer text-left"
    type="button"
    aria-label={`Edit task: ${title}`}
  >
    {title}
  </button>
));

TaskTitleButton.displayName = 'TaskTitleButton';

// Status formatting utility
const formatStatus = (status: string | undefined): string => {
  const statusText = status || 'draft';
  return statusText.charAt(0).toUpperCase() + statusText.slice(1);
};

// Status selector component for inline editing
const StatusSelector = memo<{ taskId: string; currentStatus: string | undefined; companyId: string }>(({ taskId, currentStatus, companyId }) => {
  const zero = useZero();
  const [isUpdating, setIsUpdating] = useState(false);

  // Fetch company task statuses - same as TaskEditDialog
  const { data: taskStatuses } = useQuery({
    queryKey: ['taskStatuses', companyId],
    queryFn: () => getCompanyTaskStatuses(companyId),
    enabled: !!companyId,
  });

  const handleStatusChange = async (newStatus: string) => {
    if (newStatus === currentStatus) return;
    
    setIsUpdating(true);
    try {
      await zero.mutate.company_content.update({
        id: taskId,
        values: {
          status: newStatus,
          updated_at: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Get display name for current status
  const currentStatusDisplay = taskStatuses?.find(s => s.name === currentStatus)?.display_name || formatStatus(currentStatus);

  return (
    <Select
      value={currentStatus || 'draft'}
      onValueChange={handleStatusChange}
      disabled={isUpdating}
    >
      <SelectTrigger className="h-8 w-auto border-none bg-transparent hover:bg-muted/50 focus:ring-0 focus:ring-offset-0">
        <div className="flex items-center">
          <Circle className="mr-2 h-3 w-3 text-muted-foreground" />
          <SelectValue>
            <span className="text-xs">{currentStatusDisplay}</span>
          </SelectValue>
        </div>
      </SelectTrigger>
      <SelectContent>
        {taskStatuses ? (
          taskStatuses.map((status: TaskStatus) => (
            <SelectItem key={status.id} value={status.name}>
              {status.display_name}
            </SelectItem>
          ))
        ) : (
          // Fallback statuses if company statuses haven't loaded - same as TaskEditDialog
          <>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="in progress">In Progress</SelectItem>
            <SelectItem value="review">Review</SelectItem>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
            <SelectItem value="published">Published</SelectItem>
          </>
        )}
      </SelectContent>
    </Select>
  );
});

StatusSelector.displayName = 'StatusSelector';

export const createRecentActivityColumns = ({
  onTaskClick
}: RecentActivityColumnsProps = {}): ColumnDef<any>[] => [
  {
    accessorKey: "task_title",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.task'} />,
    cell: ({ row }) => {
      return (
        <TaskTitleButton
          task={row.original}
          title={row.getValue("task_title")}
          onTaskClick={onTaskClick}
        />
      )
    },
  },
  {
    accessorKey: "status",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.status'} />,
    cell: ({ row }) => {
      const status = row.original.status;
      const taskId = row.original.id;
      const companyId = row.original.company_id;
      return (
        <StatusSelector 
          taskId={taskId} 
          currentStatus={status}
          companyId={companyId}
        />
      );
    },
  },
  {
    accessorKey: "assigned_to",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.assignee'} />,
    cell: ({ row }) => {
      const assignedTo = row.original.assigned_to;
      const taskId = row.original.id;
      return (
        <div className="flex items-center justify-center">
          <AssigneeAvatar
            taskId={taskId}
            assignedTo={assignedTo}
          />
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "updated_at",
    header: () => <Trans i18nKey={'dashboard:recentActivity.columns.updated'} />,
    cell: ({ row }) => {
      const updatedAt = row.original.updated_at;

      if (!updatedAt) {
        return (
          <span className="text-xs text-muted-foreground">
            <Trans i18nKey={'dashboard:recentActivity.neverUpdated'} />
          </span>
        );
      }

      return (
        <span className="text-xs text-muted-foreground">
          {formatRelativeTime(updatedAt)}
        </span>
      );
    },
  },
];
