'use client';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useEffect, useState } from 'react';
import { toast } from '@kit/ui/sonner';
import { useRouter } from 'next/navigation';
import { Trans } from '@kit/ui/trans';
import { Loader2 } from 'lucide-react';
import { useCustomer } from "autumn-js/react";

// Import extracted components
import { DashboardHeader } from './dashboard-header';
import { PostTemplatesSection } from './post-templates-section';
import { CampaignLimitBanner } from './campaign-limit-banner';
import {
  CampaignTemplatesSection,
  CampaignCreationModal,
  type CampaignTemplate,
  type CampaignFormData,
  type PostTemplate,
  getTodayDate,
  calculateEndDate,
  generateCampaignId
} from '../../campaign';


export const Dashboard = () => {
  const router = useRouter();
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [isClient, setIsClient] = useState(false);
  const [campaignFeature, setCampaignFeature] = useState<any>(null);

    const [campaignTemplates] = useZeroQuery(
      zero.query.campaign_templates,
      {
        ttl: '1d'
      }
    );

  const [postTemplates] = useZeroQuery(
    zero.query.post_templates,
    {
      ttl: '1d'
    }
  );

  const [savedResearch] = useZeroQuery(
    zero.query.saved_research
    .where("account_id", "=", workspace.account.id),
    {
      ttl: '1d'
    }
  );
 
  const [icps] = useZeroQuery(
    zero.query.icps
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1d'
    }
  );
  const [personas] = useZeroQuery(
    zero.query.personas
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1d'
    }
  );


  const [products] = useZeroQuery(
    zero.query.products
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '1d'
    }
  )


  useEffect(() => {
    setIsClient(true);
  }, []);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<CampaignTemplate | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [formData, setFormData] = useState<CampaignFormData>({
    objective: '',
    channels: [],
    postsPerWeek: 3,
    colorTag: '',
    startDate: getTodayDate(),
    endDate: calculateEndDate(getTodayDate(), 3), // Default to 3 weeks from start
    externalResearch: [],
    products: [],
    icps: [],
    personas: [],
    templateId: ''
  });

  const handleQuickStartCampaign = (template: CampaignTemplate) => {
    setSelectedTemplate(template);
    const startDate = getTodayDate();
    setFormData({
      objective: String(template.goal ?? ''),
      channels: [],
      postsPerWeek: 3,
      colorTag: '',
      startDate: startDate,
      endDate: calculateEndDate(startDate, template.duration_weeks || 3),
      externalResearch: [],
      products: [],
      icps: [],
      personas: [],
      templateId: template.id
    });
    setIsModalOpen(true);
  };

  const handleUpgradePlan = () => {
    // Navigate to billing page
    router.push(`/home/<USER>/billing`);
  };

  const handleFormChange = (field: keyof CampaignFormData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleResearchToggle = (researchId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      externalResearch: checked
        ? [...prev.externalResearch, researchId]
        : prev.externalResearch.filter(id => id !== researchId)
    }));
  };

  const handleProductToggle = (productId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      products: checked
        ? [...prev.products, productId]
        : prev.products.filter(id => id !== productId)
    }));
  };

  const handleIcpToggle = (icpId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      icps: checked
        ? [...prev.icps, icpId]
        : prev.icps.filter(id => id !== icpId)
    }));
  };

  const handlePersonaToggle = (personaId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      personas: checked
        ? [...prev.personas, personaId]
        : prev.personas.filter(id => id !== personaId)
    }));
  };

  // Post handlers
  const handleQuickStartPost = (template: PostTemplate) => {
    const postId = crypto.randomUUID();
    (zero.mutate.company_content as any).insert({
      id: postId,
      values: { 
        company_id: workspace.account.id,
        content_type: template.content_type,
        channel: template.channel,
        created_at: Date.now(),
        updated_at: Date.now(),
        status: 'draft',
        task_title: "Untitled",
      }
    });
    router.push(`/home/<USER>/studio/${postId}`);
  };

  const handleCreateContentSchedule = async () => {
    if (!selectedTemplate || !workspace.user?.id) {
      toast.error('Missing required information');
      return;
    }

    setIsCreating(true);
    
    try {
      const id = generateCampaignId();
      console.log("formData", {
        external_research: formData.externalResearch,
        products: formData.products,
        target_icps: formData.icps,
        target_personas: formData.personas,
      });
      
      // Create the campaign using the mutator
      (zero.mutate.company_campaigns as any).insert({
        id,
        company_id: workspace.account.id,
        user_id: workspace.user.id!,
        purpose: `${formData.channels.join(' & ')} Campaign`,
        objective: formData.objective,
        start_date: formData.startDate,
        end_date: formData.endDate,
        templateId: formData.templateId,
        external_research: formData.externalResearch,
        products: formData.products,
        target_icps: formData.icps,
        target_personas: formData.personas,
        channels: formData.channels,
        posts_per_week: formData.postsPerWeek,
        color_tag: formData.colorTag,
      });

      (zero.mutate.user_cache as any).upsert({
        user_id: workspace.user.id!,
        values: {
          selected_campaign: id,
        }
      });
      setIsModalOpen(false);

      toast.success('Campaign created successfully! Content is being generated. Redirecting to tasks...');
      router.push(`/home/<USER>/tasks`);
       //wait for 2 seconds
      //  setTimeout(() => {
      //   setIsModalOpen(false);
      //   router.push(`/home/<USER>/tasks`);
      // }, 2000);
      // Reset form
      const resetStartDate = getTodayDate();
      setFormData({
        objective: '',
        channels: [],
        postsPerWeek: 3,
        colorTag: '',
        startDate: resetStartDate,
        endDate: calculateEndDate(resetStartDate, 3),
        externalResearch: [],
        products: [],
        icps: [],
        personas: [],
        templateId: ''
      });
      setSelectedTemplate(null);
      
    } catch (error) {
      console.error('Failed to create campaign:', error);
      toast.error('Failed to create campaign. Please try again.');
    } finally {
      setIsCreating(false);
    }
  };

  // const handleViewAllCampaigns = () => {
  //   // TODO: Navigate to campaigns page
  //   console.log('Navigating to campaigns page');
  // };

  // Show loading state on server and until client hydrates
  // if (!isClient || !campaignTemplates || campaignTemplates.length === 0 || isLoadingFeature) {
  //   return (
  //     <div className="p-6 max-w-7xl mx-auto">
  //       <DashboardHeader />

  //       <div className="mb-8 flex flex-col gap-2 justify-center items-center">
  //         <div className="flex items-center justify-center h-64">
  //           <div className="text-center">
  //             <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
  //             <p className="text-muted-foreground">
  //               <Trans 
  //                 i18nKey="dashboard:loadingDashboard" 
  //                 defaults={isLoadingFeature ? "Loading dashboard..." : "Loading campaign templates..."}
  //               />
  //             </p>
  //           </div>
  //         </div>
  //       </div>

  //       <PostTemplatesSection
  //         postTemplates={[]}
  //         onQuickStartPost={handleQuickStartPost}
  //       />
  //     </div>
  //   );
  // }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <DashboardHeader />

      {/* Show banner if campaign creation is disabled */}
      {/* {campaignFeature?.next_reset_at && (
        <CampaignLimitBanner 
          nextResetAt={campaignFeature.next_reset_at}
          onUpgrade={handleUpgradePlan}
        />
      )} */}

      <CampaignTemplatesSection
        campaignTemplates={campaignTemplates}
        onQuickStartCampaign={handleQuickStartCampaign}
      />

      <PostTemplatesSection
        postTemplates={postTemplates || []}
        onQuickStartPost={handleQuickStartPost}
      />

      <CampaignCreationModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        formData={formData}
        onFormChange={handleFormChange}
        products={products || []}
        onProductToggle={handleProductToggle}
        onCreateCampaign={handleCreateContentSchedule}
        isCreating={isCreating}
      />



    </div>
  );
};