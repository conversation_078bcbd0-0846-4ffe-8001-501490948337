'use client';

import React from 'react';
import { Tabs, TabsList, TabsTrigger } from '@kit/ui/tabs';
import { Trans } from '@kit/ui/trans';

interface ImageModeTabsProps {
  mode: 'generate' | 'edit';
  onModeChange: (mode: 'generate' | 'edit') => void;
  hasImage: boolean;
}

export function ImageModeTabs({ mode, onModeChange, hasImage }: ImageModeTabsProps) {
  return (
    <div className="flex justify-center">
      <Tabs value={mode} onValueChange={(value) => onModeChange(value as 'generate' | 'edit')}>
        <TabsList className="grid w-full grid-cols-2 h-8 rounded-full bg-muted/50">
          <TabsTrigger value="generate" className="text-xs rounded-full">
            <Trans i18nKey="imageStudio:generateTab" defaults="Generate" />
          </TabsTrigger>
          <TabsTrigger value="edit" disabled={!hasImage} className="text-xs rounded-full">
            <Trans i18nKey="imageStudio:editTab" defaults="Edit" />
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}
