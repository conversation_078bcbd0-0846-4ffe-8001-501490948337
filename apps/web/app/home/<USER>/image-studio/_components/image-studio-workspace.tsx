'use client';

import React, { useState, useEffect } from 'react';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "~/components/resizable/resizable"
import { ImagePreview } from './image-preview';
import { ConversationSection } from './conversation-section';
import { ConversationDropdown } from './conversation-dropdown';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';

export function ImageStudioWorkspace() {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [isNewConversation, setIsNewConversation] = useState(false);
  const [mode, setMode] = useState<'generate' | 'edit'>('generate');

  // Query for existing conversations for this company
  const [conversations] = useZeroQuery(
    zero.query.image_conversations
      .where("company_id", "=", workspace.account.id as string)
      .orderBy("updated_at", "desc"),
    {
      ttl: '10m'
    }
  );

  // Query for messages in the current conversation
  const [messages] = useZeroQuery(
    currentConversationId ? 
      zero.query.image_messages
        .where("conversation_id", "=", currentConversationId)
        .orderBy("created_at", "asc") :
      zero.query.image_messages.where("id", "=", "never-match"), // Empty query when no conversation
    { ttl: '10m' }
  );

  // Auto-select the most recent conversation if none is selected and not starting a new conversation
  useEffect(() => {
    if (!currentConversationId && !isNewConversation && conversations && conversations.length > 0) {
      setCurrentConversationId(conversations[0]?.id || null);
    }
  }, [conversations, currentConversationId, isNewConversation]);

  const handleNewConversation = () => {
    setCurrentConversationId(null);
    setIsNewConversation(true);
  };

  const handleSelectConversation = (conversationId: string) => {
    setCurrentConversationId(conversationId);
    setIsNewConversation(false);
  };

  const handleConversationCreated = (conversationId: string) => {
    setCurrentConversationId(conversationId);
    setIsNewConversation(false);
  };

  const handleModeChange = (newMode: 'generate' | 'edit') => {
    setMode(newMode);
  };

  const handleUndo = async () => {
    if (!messages || messages.length < 2) return;
    
    // Find all messages with images
    const messagesWithImages = messages.filter(msg => msg.image_path);
    
    if (messagesWithImages.length < 2) return;
    
    // Get the second-to-last image URL
    const secondToLastImageUrl = messagesWithImages[messagesWithImages.length - 2].image_path;
    
    // Get the latest message with an image
    const latestImageMessage = messagesWithImages[messagesWithImages.length - 1];
    console.log("secondToLastImageUrl", secondToLastImageUrl);
    console.log("messagesWithImages", messagesWithImages);
    console.log("latestImageMessage", latestImageMessage);
    if (secondToLastImageUrl && latestImageMessage) {
      // Update the latest message's image_path with the second-to-last image URL
      await (zero.mutate.image_messages as any).update({
        id: latestImageMessage.id,
        values: {
          image_path: secondToLastImageUrl,
        }
      });
    }
  };

  return (
    <div className="h-screen w-full flex flex-col overflow-hidden">
      {/* Conversation Dropdown - positioned in top right */}
      <div className="flex justify-end p-4 border-b flex-shrink-0">
        <ConversationDropdown
          conversations={(conversations || []) as any[]}
          currentConversationId={currentConversationId}
          onSelectConversation={handleSelectConversation}
          onNewConversation={handleNewConversation}
        />
      </div>

      {/* Main workspace */}
      <div className="flex-1 min-h-0">
        <ResizablePanelGroup direction="horizontal" className="h-full w-full">
          {/* Conversation Section - Takes up 25% of the space */}
          <ResizablePanel defaultSize={30} minSize={25} maxSize={50} className="flex flex-col">
            <ConversationSection 
              currentConversationId={currentConversationId}
              onConversationCreated={handleConversationCreated}
              mode={mode}
              onModeChange={handleModeChange}
              messages={(messages || []) as any[]}
            />
          </ResizablePanel>
          
          {/* Resizable Handle with grip */}
          <ResizableHandle withHandle />
       
          {/* Image Preview Section - Takes up 75% of the space */}
          <ResizablePanel defaultSize={75} minSize={50} className="flex flex-col">
            <ImagePreview 
              messages={(messages || []) as any[]} 
              onUndo={handleUndo}
            />
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
}
