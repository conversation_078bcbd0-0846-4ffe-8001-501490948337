'use client';

import React from 'react';
import { ChevronDown, MessageSquare, Plus } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

interface Conversation {
  id: string;
  image_path?: string;
  created_at: number;
  updated_at: number;
}

interface ConversationDropdownProps {
  conversations: Conversation[];
  currentConversationId: string | null;
  onSelectConversation: (conversationId: string) => void;
  onNewConversation: () => void;
}

export function ConversationDropdown({
  conversations,
  currentConversationId,
  onSelectConversation,
  onNewConversation,
}: ConversationDropdownProps) {
  const currentConversation = conversations.find(c => c.id === currentConversationId);
  
  const formatConversationTitle = (conversation: Conversation) => {
    const date = new Date(conversation.created_at);
    return `Conversation ${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-48 justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            <span className="truncate">
              {currentConversation 
                ? formatConversationTitle(currentConversation)
                : <Trans i18nKey="imageStudio:newConversation" defaults="New Conversation" />
              }
            </span>
          </div>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-64" align="end">
        <DropdownMenuLabel>
          <Trans i18nKey="imageStudio:conversations" defaults="Conversations" />
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={onNewConversation}>
          <Plus className="h-4 w-4 mr-2" />
          <Trans i18nKey="imageStudio:newConversation" defaults="New Conversation" />
        </DropdownMenuItem>
        
        {conversations.length > 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuLabel className="text-xs text-muted-foreground uppercase">
              <Trans i18nKey="imageStudio:recent" defaults="Recent" />
            </DropdownMenuLabel>
            {conversations.slice(0, 10).map((conversation) => (
              <DropdownMenuItem
                key={conversation.id}
                onClick={() => onSelectConversation(conversation.id)}
                className={currentConversationId === conversation.id ? 'bg-accent' : ''}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                <div className="flex-1 min-w-0">
                  <div className="truncate text-sm">
                    {formatConversationTitle(conversation)}
                  </div>
                  {conversation.image_path && (
                    <div className="text-xs text-muted-foreground truncate">
                      With image
                    </div>
                  )}
                </div>
              </DropdownMenuItem>
            ))}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
