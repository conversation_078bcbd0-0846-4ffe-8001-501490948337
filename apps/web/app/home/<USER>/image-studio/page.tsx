import { PageBody } from '@kit/ui/page';
import { Trans } from '@kit/ui/trans';

import { createI18nServerInstance } from '~/lib/i18n/i18n.server';
import { withI18n } from '~/lib/i18n/with-i18n';

import { ImageStudioHeader } from './_components/image-studio-header';
import { ImageStudioWorkspace } from './_components/image-studio-workspace';

export const generateMetadata = async () => {
  const i18n = await createI18nServerInstance();
  const title = i18n.t('account:imageStudioPage');

  return {
    title,
  };
};

function ImageStudioPage() {
  return (
    <>
      {/* <ImageStudioHeader
        title={<Trans i18nKey={'common:routes.imageStudio'} />}
        description={<Trans i18nKey={'common:imageStudioDescription'} />}
      /> */}

      <PageBody>
        <ImageStudioWorkspace />
      </PageBody>
    </>
  );
}

export default withI18n(ImageStudioPage);
