import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { Input } from "@kit/ui/input";
import { Folder, ArrowLeft, Upload } from "lucide-react";
import { VideoGallery } from './video-gallery';
import { listAssetFolders, listFolderContents } from '~/services/storage';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { AssetFolder, StorageFile } from '~/types/assets';
import { useVideoContent } from '../../context';

interface ExistingVideoSelectorProps {
  onUploadClick?: () => void;
  onBackToChoice?: () => void;
}

export const ExistingVideoSelector: React.FC<ExistingVideoSelectorProps> = ({ 
  onUploadClick, 
  onBackToChoice
}) => {
  const { account } = useTeamAccountWorkspace();
  const [folders, setFolders] = useState<AssetFolder[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [folderVideos, setFolderVideos] = useState<StorageFile[]>([]);
  const [filterQuery, setFilterQuery] = useState('');
  const { selectedVideoOption } = useVideoContent();
  // Fetch folders on mount
  useEffect(() => {
    const fetchFolders = async () => {
      if (account?.id) {
        const assetFolders = await listAssetFolders(account.id);
        setFolders(assetFolders);
      }
    };
    fetchFolders();
  }, [account?.id]);

  // Fetch folder contents when a folder is selected
  useEffect(() => {
    const fetchFolderContents = async () => {
      if (account?.id && selectedFolder) {
        const contents = await listFolderContents(selectedFolder, account.id);
        // Filter only video files
        const videoFiles = contents.filter(file => file.type?.startsWith('video/'));
        setFolderVideos(videoFiles);
      }
    };
    if (selectedFolder) {
      fetchFolderContents();
    }
  }, [account?.id, selectedFolder]);

  const handleFilter = () => {
    // Filter folders by name
    console.log('Filtering with:', filterQuery);
  };

  // If a folder is selected, show its contents
  if (selectedFolder) {
    return (
      <div className="space-y-4">
        <Button 
          variant="ghost" 
          className="mb-4" 
          onClick={() => setSelectedFolder(null)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Folders
        </Button>

        <div className="mb-4">
          <h3 className="text-md font-medium mb-2">Videos in {selectedFolder}</h3>
          <p className="text-sm text-muted-foreground">Select a video to use in your content.</p>
        </div>

        {folderVideos.length > 0 ? (
          <VideoGallery
            videoUrls={folderVideos.map(file => file.url)}
          />
        ) : (
          <div className="h-40 bg-muted rounded-md flex items-center justify-center">
            <p className="text-sm text-muted-foreground">No videos found in this folder</p>
          </div>
        )}
      </div>
    );
  }

  // Show folder list with upload button
  return (
    <div className="space-y-4">
     {(onBackToChoice && (selectedVideoOption === 'existing')) && (
          <Button 
            variant="ghost" 
            onClick={onBackToChoice}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        )}
      <div className="flex justify-between items-center mb-4"> 
        <div>
          <h3 className="text-md font-medium mb-2">Browse Asset Folders</h3>
          <p className="text-sm text-muted-foreground">Select a folder to view its videos or upload a new one.</p>
        </div>
        
      </div>
      
      <div className="flex gap-2 mb-4">
        <Input 
          placeholder="Filter folders..."
          className="flex-1"
          value={filterQuery}
          onChange={(e) => setFilterQuery(e.target.value)}
        />
        <Button onClick={handleFilter}>Filter</Button>
      </div>
      
      {onUploadClick && (
        <Button 
          onClick={onUploadClick} 
          className="w-full mb-4"
          variant="outline"
        >
          <Upload className="h-4 w-4 mr-2" />
          Upload New Video
        </Button>
      )}
      
      {folders.length > 0 ? (
        <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
          {folders.map((folder) => (
            <div
              key={folder.name}
              onClick={() => setSelectedFolder(folder.name)}
              className="flex cursor-pointer flex-col items-center rounded-lg border p-4 hover:bg-gray-50"
            >
              <Folder className="h-12 w-12 text-blue-500" />
              <span className="mt-2 text-sm">{folder.name}</span>
            </div>
          ))}
        </div>
      ) : (
        <div className="h-40 bg-muted rounded-md flex items-center justify-center">
          <p className="text-sm text-muted-foreground">No asset folders found</p>
        </div>
      )}
    </div>
  );
}; 