'use client'
import React from 'react';
import { Label } from "@kit/ui/label";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@kit/ui/accordion";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { Input } from "@kit/ui/input";
import { X } from "lucide-react";
import { DocumentSelector, SelectedDocument } from '~/components/document-selector';
import { Persona } from '~/types/persona';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface AdvancedOptionsProps {
  selectedDocuments: SelectedDocument[];
  onDocumentsChange: (documents: SelectedDocument[]) => void;
  selectedIcps: any[];
  onIcpsChange: (icps: any[]) => void;
  icps: any[];
  onIcpsListChange: (icps: any[]) => void;
  selectedPersonas: any[];
  onPersonasChange: (personas: any[]) => void;
  personas: Persona[];
  onPersonasListChange: (personas: Persona[]) => void;
  trendKeywords: string[];
  onTrendKeywordsChange: (keywords: string[]) => void;
  seoKeywords: string[];
  onSeoKeywordsChange: (keywords: string[]) => void;
  selectedResearch: any[];
  onResearchChange: (research: any[]) => void;
  researchItems: any[];
  onResearchItemsChange: (items: any[]) => void;
}

export const AdvancedOptions: React.FC<AdvancedOptionsProps> = ({
  selectedDocuments,
  onDocumentsChange,
  selectedIcps,
  onIcpsChange,
  icps,
  onIcpsListChange,
  selectedPersonas,
  onPersonasChange,
  personas,
  onPersonasListChange,
  trendKeywords,
  onTrendKeywordsChange,
  seoKeywords,
  onSeoKeywordsChange,
  selectedResearch,
  onResearchChange,
  researchItems,
  onResearchItemsChange,
}) => {
  const [currentKeyword, setCurrentKeyword] = React.useState('');
  const [currentSeoKeyword, setCurrentSeoKeyword] = React.useState('');
  const workspace = useTeamAccountWorkspace();

  const handleKeywordKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && currentKeyword.trim()) {
      e.preventDefault();
      if (!trendKeywords.includes(currentKeyword.trim())) {
        onTrendKeywordsChange([...trendKeywords, currentKeyword.trim()]);
      }
      setCurrentKeyword('');
    }
  };

  const handleSeoKeywordKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && currentSeoKeyword.trim()) {
      e.preventDefault();
      if (!seoKeywords.includes(currentSeoKeyword.trim())) {
        onSeoKeywordsChange([...seoKeywords, currentSeoKeyword.trim()]);
      }
      setCurrentSeoKeyword('');
    }
  };

  const removeKeyword = (keywordToRemove: string) => {
    onTrendKeywordsChange(trendKeywords.filter(keyword => keyword !== keywordToRemove));
  };

  const removeSeoKeyword = (keywordToRemove: string) => {
    onSeoKeywordsChange(seoKeywords.filter(keyword => keyword !== keywordToRemove));
  };

  return (
    <Accordion type="multiple" defaultValue={["context"]} className="w-full">
      <AccordionItem value="context">
        <AccordionTrigger className="py-2 hover:no-underline">
          <Label className="text-lg font-semibold">Add Context</Label>
        </AccordionTrigger>
        <AccordionContent>
          <DocumentSelector
            accountId={workspace.account.id}
            initialDocuments={selectedDocuments}
            onDocumentsChange={onDocumentsChange}
            description="Select documents to provide context for content generation."
          />
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="icps">
        <AccordionTrigger className="py-2 hover:no-underline">
          <Label className="text-lg font-semibold">Add ICPs</Label>
        </AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4">
            <Label className="text-sm text-muted-foreground">Select ideal customer profiles to tailor content for specific customer segments.</Label>
            
            <div className="flex flex-wrap gap-2 mb-2">
              {selectedIcps.map((icp) => {
                return (
                  <div
                    key={icp.id}
                    className="flex items-center gap-1 rounded-md bg-secondary px-3 py-1 text-sm"
                  >
                    <span>{icp.name}</span>
                    <button
                      onClick={() => onIcpsChange(selectedIcps.filter(i => i.id !== icp.id))}
                      className="ml-1 rounded-full p-1 hover:bg-secondary-foreground/10"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                );
              })}
            </div>

            <Select 
              disabled={icps.length === 0}
              onValueChange={(value) => {
                const selectedIcp = icps.find(i => i.id === value);
                if (selectedIcp && !selectedIcps.some(i => i.id === selectedIcp.id)) {
                  onIcpsChange([...selectedIcps, selectedIcp]);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={icps.length === 0 ? "No ICPs Created" : "Select ICPs"} />
              </SelectTrigger>
              <SelectContent>
                {icps.length === 0 ? (
                  <SelectItem value="none" disabled>No ICPs Created</SelectItem>
                ) : (
                  icps.map((icp) => (
                    <SelectItem key={icp.id} value={icp.id}>
                      {icp.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="persona">
        <AccordionTrigger className="py-2 hover:no-underline">
          <Label className="text-lg font-semibold">Add Persona</Label>
        </AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4">
            <Label className="text-sm text-muted-foreground">Select a persona to generate content for a specific audience.</Label>
            
            <div className="flex flex-wrap gap-2 mb-2">
              {selectedPersonas.map((persona) => {
                return (
                  <div
                    key={persona.id}
                    className="flex items-center gap-1 rounded-md bg-secondary px-3 py-1 text-sm"
                  >
                    <span>{persona.name}</span>
                    <button
                      onClick={() => onPersonasChange(selectedPersonas.filter(p => p.id !== persona.id))}
                      className="ml-1 rounded-full p-1 hover:bg-secondary-foreground/10"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                );
              })}
            </div>

            <Select 
              disabled={personas.length === 0}
              onValueChange={(value) => {
                const selectedPersona = personas.find(p => p.id === value);
                if (selectedPersona && !selectedPersonas.some(p => p.id === selectedPersona.id)) {
                  onPersonasChange([...selectedPersonas, selectedPersona]);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={personas.length === 0 ? "No Personas Created" : "Select Personas"} />
              </SelectTrigger>
              <SelectContent>
                {personas.length === 0 ? (
                  <SelectItem value="none" disabled>No Personas Created</SelectItem>
                ) : (
                  personas.map((persona) => (
                    <SelectItem key={persona.id} value={persona.id}>
                      {persona.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="research">
        <AccordionTrigger className="py-2 hover:no-underline">
          <Label className="text-lg font-semibold">External Research</Label>
        </AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4">
            <Label className="text-sm text-muted-foreground">Select saved research items to provide additional context.</Label>
            
            <div className="flex flex-wrap gap-2 mb-2">
              {selectedResearch.map((research) => {
                return (
                  <div
                    key={research.id}
                    className="flex items-center gap-1 rounded-md bg-secondary px-3 py-1 text-sm"
                  >
                    <span>{research.title || research.name}</span>
                    <button
                      onClick={() => onResearchChange(selectedResearch.filter(r => r.id !== research.id))}
                      className="ml-1 rounded-full p-1 hover:bg-secondary-foreground/10"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                );
              })}
            </div>

            <Select 
              disabled={researchItems.length === 0}
              onValueChange={(value) => {
                const selectedResearchItem = researchItems.find(r => r.id === value);
                if (selectedResearchItem && !selectedResearch.some(r => r.id === selectedResearchItem.id)) {
                  onResearchChange([...selectedResearch, selectedResearchItem]);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder={researchItems.length === 0 ? "No Research Items Found" : "Select Research Items"} />
              </SelectTrigger>
              <SelectContent>
                {researchItems.length === 0 ? (
                  <SelectItem value="none" disabled>No Research Items Found</SelectItem>
                ) : (
                  researchItems.map((research) => (
                    <SelectItem key={research.id} value={research.id}>
                      {research.title} - {research.research_type}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="trend-keywords">
        <AccordionTrigger className="py-2 hover:no-underline">
          <Label className="text-lg font-semibold">Trend Keywords</Label>
        </AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4">
            <Label className="text-sm text-muted-foreground">Add trending keywords to make your content more discoverable.</Label>
            
            <div className="flex flex-wrap gap-2">
              {trendKeywords.map((keyword, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 rounded-md bg-secondary px-3 py-1 text-sm"
                >
                  <span>{keyword}</span>
                  <button
                    onClick={() => removeKeyword(keyword)}
                    className="ml-1 rounded-full p-1 hover:bg-secondary-foreground/10"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>

            <Input
              value={currentKeyword}
              onChange={(e) => setCurrentKeyword(e.target.value)}
              onKeyDown={handleKeywordKeyDown}
              placeholder="Type a keyword and press Enter"
            //   disabled={isGenerating}
            />
          </div>
        </AccordionContent>
      </AccordionItem>

      <AccordionItem value="seo-keywords">
        <AccordionTrigger className="py-2 hover:no-underline">
          <Label className="text-lg font-semibold">SEO Keywords</Label>
        </AccordionTrigger>
        <AccordionContent>
          <div className="space-y-4">
            <Label className="text-sm text-muted-foreground">Add SEO keywords to improve search engine visibility.</Label>
            
            <div className="flex flex-wrap gap-2">
              {seoKeywords.map((keyword, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 rounded-md bg-secondary px-3 py-1 text-sm"
                >
                  <span>{keyword}</span>
                  <button
                    onClick={() => removeSeoKeyword(keyword)}
                    className="ml-1 rounded-full p-1 hover:bg-secondary-foreground/10"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>

            <Input
              value={currentSeoKeyword}
              onChange={(e) => setCurrentSeoKeyword(e.target.value)}
              onKeyDown={handleSeoKeywordKeyDown}
              placeholder="Type a keyword and press Enter"
            //   disabled={isGenerating}
            />
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}; 