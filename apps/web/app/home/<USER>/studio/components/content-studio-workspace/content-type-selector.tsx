import React, { useState, useTransition } from 'react';
import { Type, Image, Video, MoreHorizontal, Pencil } from "lucide-react";
import { But<PERSON> } from "@kit/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@kit/ui/dropdown-menu";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@kit/ui/dialog";
import { Input } from "@kit/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@kit/ui/form";
import { toast } from "@kit/ui/sonner";
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useBaseContent } from './context';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZeroInstance } from '~/hooks/use-zero';
import { useParams } from 'next/navigation';

const RenameTaskSchema = z.object({
  title: z.string().min(1, 'Title is required'),
});

export const ContentTypeSelector: React.FC = () => {
  const { setSelectedType } = useBaseContent();
  const zero = useZeroInstance();
  const params = useParams();
  const contentId = params.id as string;
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [pending, startTransition] = useTransition();

  const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", params.account as string),
    {
      ttl: '10m'
    }
  );
  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];
  const form = useForm({
    resolver: zodResolver(RenameTaskSchema),
    defaultValues: {
      title: selectedCompanyContent?.task_title || '',
    },
  });
  // Don't render if contentId is not available
  if (!contentId) {
    return null;
  }



  const onRenameSubmit = (data: z.infer<typeof RenameTaskSchema>) => {
    startTransition(async () => {
      try {
        await zero.mutate.company_content.update({
          id: contentId,
          values: {
            task_title: data.title,
          },
        });
        
        toast.success('Task renamed successfully');
        setIsRenameDialogOpen(false);
        form.reset({ title: data.title });
      } catch (error) {
        toast.error('Failed to rename task');
        console.error('Error renaming task:', error);
      }
    });
  };

  const handleRenameClick = () => {
    form.reset({ title: selectedCompanyContent?.task_title || '' });
    setIsRenameDialogOpen(true);
  };

  return (
    <div className="p-2">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-xl font-semibold">{selectedCompanyContent?.task_title}</h1>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleRenameClick}>
              <Pencil className="h-4 w-4 mr-2" />
              Rename Task
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>


      <div className="flex flex-wrap gap-2">
        <Button
          variant="outline"
          className="min-h-32 w-full sm:w-[48%] flex-shrink-0 flex flex-col items-center justify-center space-y-2 p-4"
          onClick={() => setSelectedType('text')}
        >
          <Type className="h-8 w-8 flex-shrink-0" />
          <span className="text-center w-full break-words whitespace-normal overflow-hidden">Text Content</span>
        </Button>
        <Button
          variant="outline"
          className="min-h-32 w-full sm:w-[48%] flex-shrink-0 flex flex-col items-center justify-center space-y-2 p-4"
          onClick={() => setSelectedType('image')}
          data-test="image-section-tab"
        >
          <Image className="h-8 w-8 flex-shrink-0" />
          <span className="text-center w-full break-words whitespace-normal overflow-hidden">Image</span>
        </Button>
        <Button
          variant="outline"
          className="min-h-32 w-full sm:w-[48%] flex-shrink-0 flex flex-col items-center justify-center space-y-2 p-4"
          onClick={() => setSelectedType('video')}
        >
          <Video className="h-8 w-8 flex-shrink-0" />
          <span className="text-center w-full break-words whitespace-normal overflow-hidden">Video</span>
        </Button>
      </div>

      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename Task</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onRenameSubmit)} className="space-y-4">
              <FormField
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Task Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter task title"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsRenameDialogOpen(false)}
                  disabled={pending}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={pending}>
                  {pending ? 'Saving...' : 'Save'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
};