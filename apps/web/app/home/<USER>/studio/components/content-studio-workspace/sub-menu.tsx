import React, { useState } from 'react';
import { ArrowLeft } from "lucide-react";
import { But<PERSON> } from "@kit/ui/button";
import { useBaseContent, useImageContent, useVideoContent } from './context';
import {
  TextContentEditor,
  ImageContentEditor
} from './_components';
import { VideoContentEditor } from './_components/video/video-content-editor';

interface SubMenuProps {
  onBack: () => void;
}

export const SubMenu: React.FC<SubMenuProps> = ({ onBack }) => {
  // Local UI state only for loading indicators
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingDescription, setIsGeneratingDescription] = useState(false);
  
  // Get shared state from contexts
  const { selectedType, handleInsert } = useBaseContent();
  
  const {
    input, 
    setInput,
    imageOptions, 
    setImageOptions,
    selectedImageOption,
    setSelectedImageOption,
    visualDescriptionGroup, 
    setImageUrls,
    selectedEditorImage,
    generateVisualDescription,
    generateImages
  } = useImageContent();

  const {
    selectedVideoOption
  } = useVideoContent();

  const getVisualDescription = async () => {
    try {
      setIsGeneratingDescription(true);
      // Simplified call using context values
      return await generateVisualDescription(
        input, 
        imageOptions, // campaignIdea is accessible in the context now
        "" // companyContentId is accessible in the context now
      );
    } catch (error) {
      console.error('Error generating visual description:', error);
    } finally {
      setIsGeneratingDescription(false);
    }
  };

  const handleSubmit = async () => {
    if (selectedType === 'image' && selectedImageOption === 'ai') {
      try {
        setIsGenerating(true);
        if(visualDescriptionGroup.Balanced === "") {
          visualDescriptionGroup.Balanced = input;
          visualDescriptionGroup.Full = input;
          visualDescriptionGroup.Learned = input;
          visualDescriptionGroup.Prominent = input;
          visualDescriptionGroup.Subtle = input;
        }
        await generateImages(visualDescriptionGroup, imageOptions);
      } catch (error) {
        console.error('Error generating images:', error);
      } finally {
        setIsGenerating(false);
      }
    } else {
      handleInsert(input);
    }
    setImageOptions({});
  };

  // Renders the appropriate content editor based on the selected type
  const renderContentEditor = () => {
    switch (selectedType) {
      case 'text':
        return (
          <TextContentEditor />
        );
      case 'image':
        if(!selectedEditorImage && selectedImageOption == 'selected') setSelectedImageOption(null);
        return (
          <ImageContentEditor
            onGetVisualDescription={getVisualDescription}
            onSubmit={handleSubmit}
            isGenerating={isGenerating}
            isGeneratingDescription={isGeneratingDescription}
          />
        );
      case 'video':
        return <VideoContentEditor />;
      default:
        return null;
    }
  };

  // For all content types, show the regular submenu
  return (
    <div className="p-4 space-y-4 h-full overflow-auto">
      <Button 
        variant="ghost" 
        className="mb-4" 
        onClick={onBack}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>
      
      {renderContentEditor()}
    </div>
  );
};