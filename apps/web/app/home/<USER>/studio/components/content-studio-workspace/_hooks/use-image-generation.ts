import { useCallback, useState } from "react";
import type { VisualDescriptionGroup, ImageGenerationOptions } from "../context/types";
import { generateImage } from "~/lib/clients/image-generation";

function formatImageStyles(imageOptions: ImageGenerationOptions) {
  const image_gen_styles: any = {
    artistic_influences_references: imageOptions?.artistic_influences_references ?? null,
    image_camera_angle_perspective: imageOptions?.image_camera_angle_perspective ?? undefined,
    image_color_balance: imageOptions?.image_color_balance ?? undefined,
    image_composition_framing: imageOptions?.image_composition_framing ?? undefined,
    image_details: imageOptions?.image_details ?? undefined,
    image_environment_setting: imageOptions?.image_environment_setting ?? undefined,
    image_lighting: imageOptions?.image_lighting ?? undefined,
    image_material_surface_texture: imageOptions?.image_material_surface_texture ?? undefined,
    image_modes: imageOptions?.image_modes ?? undefined,
    image_mood_atmosphere: imageOptions?.image_mood_atmosphere ?? undefined,
    image_special_effects_post_processing: imageOptions?.image_special_effects_post_processing ?? undefined,
    image_styles: imageOptions?.image_styles ?? undefined,
    main_actions: [],
    main_colors: [],
    main_elements: [],
  };
  Object.keys(image_gen_styles).forEach((k) => image_gen_styles[k] === undefined && delete image_gen_styles[k]);
  return image_gen_styles;
}

export function useImageGeneration() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generate = useCallback(
    async (args: {
      visualDescriptionGroup: VisualDescriptionGroup;
      imageOptions: ImageGenerationOptions;
      input: string;
      companyId: string;
      brandBrief?: string;
    }): Promise<string[]> => {
      const { visualDescriptionGroup, imageOptions, input, companyId, brandBrief } = args;
      const imagePrompts = Object.values(visualDescriptionGroup).filter((p) => p.trim() !== "");
      const image_gen_styles = formatImageStyles(imageOptions);
      setIsGenerating(true);
      setError(null);
      try {
        const urls = await Promise.all(
          imagePrompts.map(async () => {
            const res = await generateImage({
              image_prompt: `${input} ${JSON.stringify(image_gen_styles)}`,
              aspect_ratio: "custom",
              company_id: companyId,
              image_gen_styles,
              brand_context: brandBrief,
            });
            return res.url;
          })
        );
        return urls;
      } catch (e: any) {
        setError(e?.message ?? "Failed to generate image");
        throw e;
      } finally {
        setIsGenerating(false);
      }
    },
    []
  );

  return { isGenerating, error, generate };
}

