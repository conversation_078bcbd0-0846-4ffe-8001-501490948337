import React, { createContext, useContext, useState, ReactNode } from 'react';
import { Block } from "@blocknote/core";
import { BlockNoteEditor } from "@blocknote/core";
import { EditorContentContextState } from './types';

// Create the editor content context
const EditorContentContext = createContext<EditorContentContextState | undefined>(undefined);

// Provider component for editor content context
export function EditorContentProvider({ children }: { children: ReactNode }) {
  const [blockContent, setBlockContent] = useState<Block[] | null | undefined>(null);
  const [editor, setEditor] = useState<BlockNoteEditor | null>(null);
  const value = {
    blockContent,
    setBlockContent,
    editor,
    setEditor
  };

  return (
    <EditorContentContext.Provider value={value}>
      {children}
    </EditorContentContext.Provider>
  );
}

// Custom hook for accessing the editor content context
export function useEditorContent() {
  const context = useContext(EditorContentContext);
  if (context === undefined) {
    throw new Error('useEditorContent must be used within an EditorContentProvider');
  }
  return context;
}