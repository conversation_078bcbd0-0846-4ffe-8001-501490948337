import React, { useState } from 'react';
import { Button } from "@kit/ui/button";
import { Input } from "@kit/ui/input";
import { toast } from 'sonner';
import { ImageGallery } from '../image-gallery';

export const StockImageSelector = () => {
  const [stockImages, setStockImages] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  
  const handleStockSearch = async (query: string) => {
    if (!query.trim()) return;
    
    setIsSearching(true);
    try {
      const response = await fetch(`/api/stock-images/search?query=${encodeURIComponent(query)}`);
      const data = await response.json();
      setStockImages(data.photos);
    } catch (error) {
      console.error(error);
      toast.error('Failed to fetch stock images');
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleStockSearch(searchQuery);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Search Stock Images</h3>
        <p className="text-sm text-muted-foreground">Find the perfect stock image for your content.</p>
      </div>
      
      <div className="flex gap-2">
        <Input 
          placeholder="Search for stock images..."
          className="flex-1"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleKeyPress}
        />
        <Button 
          onClick={() => handleStockSearch(searchQuery)}
          disabled={isSearching}
        >
          {isSearching ? 'Searching...' : 'Search'}
        </Button>
      </div>
      
      {stockImages.length > 0 && (
        <>
          <ImageGallery
            imageUrls={stockImages.map(image => image.src.medium)}
          />
        </>
      )}
      
      {stockImages.length === 0 && !isSearching && (
        <div className="text-center py-8 text-muted-foreground">
          Search for stock images to display results here.
        </div>
      )}
      
      {isSearching && (
        <div className="text-center py-8 text-muted-foreground">
          Searching for images...
        </div>
      )}
      
      <p className="text-xs text-muted-foreground mt-4">
        This is a placeholder. Stock image integration will be implemented in a future update.
      </p>
    </div>
  );
};