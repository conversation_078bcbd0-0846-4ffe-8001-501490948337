"use client";

import React, { useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@kit/ui/tabs";

import { SelectedDocument } from "~/components/document-selector";
import { useEditorContent } from "../../context";
import { AdvancedOptions } from "./advanced-options";
import {
  BasicOptions,
  ContentGenerationButton,
  useDataQueries,
  useCampaignDataInitializer,
  useContentGenerationService,
} from "./_components";

export const TextContentEditor: React.FC = () => {
  // Get data from context and queries
  const { editor } = useEditorContent();
  const {
    zero,
    selectedCompanyContent,
    savedResearch,
    companyBrand,
    personas,
    icps,
    companyCampaigns,
    productDocuments,
  } = useDataQueries();

  // Local state
  const [isGenerating, setIsGenerating] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [taskTitle, setTaskTitle] = useState('');
  const [taskDescription, setTaskDescription] = useState('');

  // Advanced options state
  const [trendKeywords, setTrendKeywords] = useState<string[]>([]);
  const [seoKeywords, setSeoKeywords] = useState<string[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocument[]>([]);
  const [selectedIcps, setSelectedIcps] = useState<any[]>([]);
  const [icpItems, setIcpItems] = useState<any[]>(icps || []);
  const [selectedPersonas, setSelectedPersonas] = useState<any[]>([]);
  const [personaItems, setPersonaItems] = useState<any[]>(personas || []);
  const [selectedResearch, setSelectedResearch] = useState<any[]>([]);
  const [researchItems, setResearchItems] = useState<any[]>(savedResearch || []);

  // Initialize task title and description from company content
  useEffect(() => {
    if (selectedCompanyContent) {
      setTaskTitle(selectedCompanyContent?.task_title || '');
      setTaskDescription(selectedCompanyContent?.task_description || '');
    }
  }, [selectedCompanyContent]);

  // Auto-update editor content when database changes
  useEffect(() => {
    const newBlocks = selectedCompanyContent?.content_editor_template;
    if (!editor || !Array.isArray(newBlocks)) return;

    // Replace the entire content when it changes
    if (JSON.stringify(editor.document) !== JSON.stringify(newBlocks)) {
      editor.replaceBlocks(editor.document, newBlocks);
    }
  }, [selectedCompanyContent?.content_editor_template, editor]);

  // Initialize campaign data
  useCampaignDataInitializer({
    selectedCompanyContent,
    companyCampaigns,
    productDocuments,
    icps,
    personas,
    savedResearch,
    setSelectedIcps,
    setSelectedPersonas,
    setSelectedResearch,
    setSelectedDocuments,
  });

  // Content generation service
  const { generateContentStreaming } = useContentGenerationService({
    selectedCompanyContent,
    editor,
    taskTitle,
    taskDescription,
    selectedPersonas,
    selectedIcps,
    selectedResearch,
    selectedDocuments,
    companyBrand,
    companyCampaigns,
    zero,
    setIsGenerating,
    setHasError,
  });
  // Memoize the description change handler to prevent unnecessary re-renders
  const onDescriptionChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setTaskDescription(newValue);

    if (selectedCompanyContent?.id) {
      // @ts-expect-error - Zero mutator types are not properly inferred
      zero.mutate.company_content.update({
        id: selectedCompanyContent.id,
        values: {
          task_description: newValue,
        },
      });
    }
  }, [selectedCompanyContent?.id, zero]);

  return (
    <div className="space-y-4 p-4">
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <BasicOptions
            taskDescription={taskDescription}
            onDescriptionChange={onDescriptionChange}
            isGenerating={isGenerating}
          />
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <BasicOptions
            taskDescription={taskDescription}
            onDescriptionChange={onDescriptionChange}
            isGenerating={isGenerating}
          />
          <AdvancedOptions
            selectedDocuments={selectedDocuments}
            onDocumentsChange={setSelectedDocuments}
            selectedIcps={selectedIcps}
            onIcpsChange={setSelectedIcps}
            icps={icpItems}
            onIcpsListChange={setIcpItems}
            selectedPersonas={selectedPersonas}
            onPersonasChange={setSelectedPersonas}
            personas={personaItems}
            onPersonasListChange={setPersonaItems}
            trendKeywords={trendKeywords}
            onTrendKeywordsChange={setTrendKeywords}
            seoKeywords={seoKeywords}
            onSeoKeywordsChange={setSeoKeywords}
            selectedResearch={selectedResearch}
            onResearchChange={setSelectedResearch}
            researchItems={researchItems}
            onResearchItemsChange={setResearchItems}
          />
        </TabsContent>
      </Tabs>

      <ContentGenerationButton
        isGenerating={isGenerating}
        hasError={hasError}
        taskTitle={taskTitle}
        taskDescription={taskDescription}
        onGenerate={generateContentStreaming}
      />
    </div>
  );
};
