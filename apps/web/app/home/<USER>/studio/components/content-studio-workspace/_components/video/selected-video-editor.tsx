import React from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { ArrowLeft } from "lucide-react";
import { useVideoContent } from '../../context';
import { useBaseContent } from '../../context';

export const SelectedVideoEditor: React.FC = () => {
  const { selectedEditorVideo, setSelectedVideoOption } = useVideoContent();
  const { handleInsert } = useBaseContent();

  const handleInsertVideo = () => {
    if (selectedEditorVideo) {
      handleInsert(selectedEditorVideo);
    }
  };

  if (!selectedEditorVideo) {
    return (
      <div className="space-y-4">
        <Button 
          variant="ghost" 
          className="mb-4" 
          onClick={() => setSelectedVideoOption(null)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <p className="text-sm text-muted-foreground">No video selected.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Button 
        variant="ghost" 
        className="mb-4" 
        onClick={() => setSelectedVideoOption(null)}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>
      
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Selected Video</h3>
        <p className="text-sm text-muted-foreground">
          Review your selected video before inserting it.
        </p>
      </div>
      
      <div className="rounded-md overflow-hidden border mb-4">
        <video 
          className="w-full h-auto" 
          controls
          src={selectedEditorVideo}
        >
          Your browser does not support the video tag.
        </video>
      </div>
      
      {/* Add additional video options/settings here if needed */}
      
      <Button 
        onClick={handleInsertVideo} 
        className="w-full"
      >
        Insert Video
      </Button>
    </div>
  );
}; 