'use client'
import React, { useEffect, useState } from 'react';
import { Dialog, DialogContent, DialogTitle } from "@kit/ui/dialog";
import { VisuallyHidden } from "@kit/ui/visually-hidden";
import { listAssetFolders, listFolderContents } from '~/services/storage';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

interface EditImageDialogProps {
  imageUrl: string;
  isOpen: boolean;
  onClose: () => void;
  onSave: (file: File) => Promise<void>;
}

export function EditImageDialog({ imageUrl, isOpen, onClose, onSave }: EditImageDialogProps) {
  const workspace = useTeamAccountWorkspace();
  const [imageURLs, setImageURLs] = useState<string[]>([]);


  useEffect(() => {
    const fetchFolders = async () => {

      const folders = await listAssetFolders(workspace.account.id);
      if(folders) {
        folders.forEach(async (folder) => {
          const contents = await listFolderContents(folder.name, workspace.account.id);
          setImageURLs(contents.map((content) => content.url));
        });
      }
    };
    fetchFolders();
  }, [workspace.account.id, imageURLs, setImageURLs]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-[90vw] h-screen max-h-[90vh] p-0">
        <VisuallyHidden>
          <DialogTitle>Edit Image</DialogTitle>
        </VisuallyHidden>
      </DialogContent>
    </Dialog>
  );
} 