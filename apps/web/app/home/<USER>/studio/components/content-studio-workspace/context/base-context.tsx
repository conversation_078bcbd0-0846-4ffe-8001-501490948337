import React, { createContext, useContext, useState, ReactNode } from 'react';
import { BaseContentContextState, ContentType, QualityData } from './types';
import { Block } from '@blocknote/core';

// Create the base content context
const BaseContentContext = createContext<BaseContentContextState | undefined>(undefined);

// Provider component for base content context
export function BaseContentProvider({ children }: { children: ReactNode }) {
  const [selectedType, setSelectedType] = useState<ContentType | null>(null);
  const [currentBlocks, setCurrentBlocks] = useState<Block[] | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  
  // Quality data for the meter
  const qualityData: QualityData = {
    score: 65,
    suggestions: [
      "Add more descriptive headings",
      "Include relevant keywords",
      "Break up long paragraphs",
      "Add supporting media"
    ]
  };
  
  const handleInsert = (content: string) => {
    // Implementation from useContentStudioState
    console.log('Inserting content:', content);
  };
  
  const value = {
    selectedType,
    setSelectedType,
    handleInsert,
    qualityData,
    currentBlocks,
    setCurrentBlocks,
    isGenerating,
    setIsGenerating
  };

  // Expose the context to window for access from the video editor
  if (typeof window !== 'undefined') {
    window._baseContentContext = value;
  }

  return (
    <BaseContentContext.Provider value={value}>
      {children}
    </BaseContentContext.Provider>
  );
}

// Custom hook for accessing the base content context
export function useBaseContent() {
  const context = useContext(BaseContentContext);
  if (context === undefined) {
    throw new Error('useBaseContent must be used within a BaseContentProvider');
  }
  return context;
}