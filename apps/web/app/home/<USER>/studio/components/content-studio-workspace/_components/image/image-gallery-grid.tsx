import React from "react";
import { Button } from "@kit/ui/button";
import { cn } from "@kit/ui/utils";
import Image from "next/image";
import { Plus } from "lucide-react";

interface ImageGalleryGridProps {
  imageUrls: string[];
  selectedUrl: string | null;
  onSelect: (url: string) => void;
  onOpenModal: (url: string) => void;
  onSave: (url: string) => void;
}

export function ImageGalleryGrid({ imageUrls, selectedUrl, onSelect, onOpenModal, onSave }: ImageGalleryGridProps) {
  if (imageUrls.length === 0) return null;

  return (
    <div className="grid grid-cols-2 gap-4">
      {imageUrls.map((url, index) => (
        <div key={index} className="flex flex-col gap-4">
          <div
            className={cn(
              "relative cursor-pointer rounded-lg overflow-hidden border-2",
              selectedUrl === url ? "border-primary" : "border-transparent"
            )}
            onClick={() => {
              onSelect(url);
              onOpenModal(url);
            }}
          >
            <Image
              src={url}
              alt={`Generated image ${index + 1}`}
              className="w-full h-auto object-cover"
              width={400}
              height={400}
              unoptimized
              data-test="generated-image"
            />
          </div>
          <Button
            variant="outline"
            size="icon"
            className="flex items-center justify-center w-full"
            onClick={() => onSave(url)}
            aria-label="Add image to content"
            data-test={`save-image-button-${index}`}
          >
            <Plus className="h-6 w-6" /> Add
          </Button>
        </div>
      ))}
    </div>
  );
}

