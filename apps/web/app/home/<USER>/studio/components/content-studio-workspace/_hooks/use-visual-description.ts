import { useCallback, useState } from "react";
import type { ImageGenerationOptions, VisualDescriptionGroup } from "../context/types";

function formatImageStyles(imageOptions: ImageGenerationOptions) {
  const image_gen_styles: any = {
    artistic_influences_references: imageOptions?.artistic_influences_references ?? null,
    image_camera_angle_perspective: imageOptions?.image_camera_angle_perspective ?? undefined,
    image_color_balance: imageOptions?.image_color_balance ?? undefined,
    image_composition_framing: imageOptions?.image_composition_framing ?? undefined,
    image_details: imageOptions?.image_details ?? undefined,
    image_environment_setting: imageOptions?.image_environment_setting ?? undefined,
    image_lighting: imageOptions?.image_lighting ?? undefined,
    image_material_surface_texture: imageOptions?.image_material_surface_texture ?? undefined,
    image_modes: imageOptions?.image_modes ?? undefined,
    image_mood_atmosphere: imageOptions?.image_mood_atmosphere ?? undefined,
    image_special_effects_post_processing: imageOptions?.image_special_effects_post_processing ?? undefined,
    image_styles: imageOptions?.image_styles ?? undefined,
    main_actions: [],
    main_colors: [],
    main_elements: [],
  };
  Object.keys(image_gen_styles).forEach((k) => image_gen_styles[k] === undefined && delete image_gen_styles[k]);
  return image_gen_styles;
}

export function useVisualDescription() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generate = useCallback(
    async (args: {
      brandName: string;
      brandBrief?: string;
      productInfo?: unknown;
      input: string;
      imageOptions: ImageGenerationOptions;
      language?: string;
    }): Promise<{ visual_description: VisualDescriptionGroup }> => {
      const { brandName, brandBrief, productInfo, input, imageOptions, language = "English" } = args;
      setIsLoading(true);
      setError(null);
      const image_gen_styles = formatImageStyles(imageOptions);
      try {
        const res = await fetch("/api/ai/generate-idea-visual-description", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            brand_name: brandName,
            brand_brief: brandBrief ?? "No Brand Brief Provided",
            product_info: productInfo ?? "No Product Info Provided",
            generated_content: input || "No Generated Content Provided",
            initial_visual_desc: input || "No Initial Visual Description Provided",
            language,
            image_gen_styles,
          }),
        });
        if (!res.ok) throw new Error(`Visual description failed (${res.status})`);
        const data = await res.json();
        return data;
      } catch (e: any) {
        setError(e?.message ?? "Failed to generate visual description");
        throw e;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  return { isLoading, error, generate };
}

