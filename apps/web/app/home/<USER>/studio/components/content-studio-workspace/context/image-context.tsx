import React, { useEffect, createContext, useContext, useState, ReactNode } from 'react';
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";
import { useBrandData } from "~/hooks/use-brand-data";
import { extractBrandBrief } from "~/utils/brief.util";
import { listFolderContents } from "~/services/storage";
import {
  ImageContentContextState,
  VisualDescriptionGroup,
  ImageGenerationOptions,
  ImageOptionType
} from './types';
import { useImageGeneration } from "../_hooks/use-image-generation";
import { useVisualDescription } from "../_hooks/use-visual-description";

// Create the image content context
const ImageContentContext = createContext<ImageContentContextState | undefined>(undefined);

// Provider component for image content context
export function ImageContentProvider({ children }: { children: ReactNode }) {
  const [selectedImageOption, setSelectedImageOption] = useState<ImageOptionType | null>(null);
  const [input, setInput] = useState('');
  const [imageOptions, setImageOptions] = useState<ImageGenerationOptions>({});
  
  // Wrap setImageOptions to add logging
  const setImageOptionsWithLogging = (newOptions: ImageGenerationOptions | ((prev: ImageGenerationOptions) => ImageGenerationOptions)) => {
    setImageOptions(newOptions);
  };
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [selectedImageId, setSelectedImageId] = useState<string | null>(null);
  const [enlargedImageUrl, setEnlargedImageUrl] = useState<string | null>(null);
  const [visualDescriptionGroup, setVisualDescriptionGroup] = useState<VisualDescriptionGroup>({
    Balanced: '',
    Subtle: '',
    Learned: '',
    Prominent: '',
    Full: ''
  });
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [selectedEditorImage, setSelectedEditorImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // External hooks for API calls
  const workspace = useTeamAccountWorkspace();
  const brand = useBrandData(workspace.account.id);
  
  // API methods for image generation
  const { generate: generateWithHook } = useImageGeneration();
  const { generate: generateVD } = useVisualDescription();

  // Bootstrap gallery from persistent sources (Supabase Storage + localStorage)
  useEffect(() => {
    let cancelled = false;
    const load = async () => {
      try {
        const companyId = workspace.account.id;
        const files = await listFolderContents('generated', companyId);
        const stored = typeof window !== 'undefined' ? (localStorage.getItem(`generated_gallery:${companyId}`) || '[]') : '[]';
        const storedUrls: string[] = JSON.parse(stored);
        const urls = Array.from(new Set([...(files?.map(f => f.url) || []), ...storedUrls]));
        if (!cancelled) setImageUrls(urls);
      } catch {
        // ignore bootstrap errors
      }
    };
    load();
    return () => { cancelled = true; };
  }, [workspace.account.id]);

  const generateVisualDescription = async (
    input: string,
    imageOptions: ImageGenerationOptions,
    _companyContentId: string
  ) => {
    const data = await generateVD({
      brandName: workspace.account.name,
      brandBrief: brand.data?.brand ? extractBrandBrief(brand.data.brand) : "No Brand Brief Provided",
      productInfo: (brand.data?.brand as any)?.product_list || "No Product Info Provided",
      input,
      imageOptions,
      language: "English",
    });

    setInput(data.visual_description.Balanced);
    setVisualDescriptionGroup(data.visual_description);
    return data;
  };

  // Generate images based on visual descriptions (via hook)
  const generateImages = async (
    visualDescriptionGroup: VisualDescriptionGroup,
    imageOptions: ImageGenerationOptions
  ) => {
    const companyId = workspace.account.id;
    setError(null); // Clear any previous errors
    try {
      const urls = await generateWithHook({
        visualDescriptionGroup,
        imageOptions,
        input,
        companyId,
        brandBrief: brand.data?.brand ? extractBrandBrief(brand.data.brand) : undefined,
      });
      const merged = Array.from(new Set([...(imageUrls || []), ...urls]));
      setImageUrls(merged);
      try {
        if (typeof window !== 'undefined') {
          const existing: string[] = JSON.parse(localStorage.getItem(`generated_gallery:${companyId}`) || '[]');
          const combined = Array.from(new Set([...(existing || []), ...urls]));
          localStorage.setItem(`generated_gallery:${companyId}`, JSON.stringify(combined));
        }
      } catch {/* ignore localStorage errors */}
      return urls;
    } catch (err: any) {
      setError(err?.message ?? 'Failed to generate images');
      throw err;
    }
  };
  
  const value = {
    selectedImageOption,
    setSelectedImageOption,
    input,
    setInput,
    imageOptions,
    setImageOptions: setImageOptionsWithLogging,
    selectedImageUrl,
    setSelectedImageUrl,
    selectedImageId,
    setSelectedImageId,
    enlargedImageUrl,
    setEnlargedImageUrl,
    visualDescriptionGroup,
    setVisualDescriptionGroup,
    imageUrls,
    setImageUrls,
    selectedEditorImage,
    setSelectedEditorImage,
    error,
    setError,
    generateVisualDescription,
    generateImages
  };

  return (
    <ImageContentContext.Provider value={value}>
      {children}
    </ImageContentContext.Provider>
  );
}

// Custom hook for accessing the image content context
export function useImageContent() {
  const context = useContext(ImageContentContext);
  if (context === undefined) {
    throw new Error('useImageContent must be used within an ImageContentProvider');
  }
  return context;
}