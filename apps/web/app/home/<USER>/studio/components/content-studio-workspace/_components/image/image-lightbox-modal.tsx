import React, { useEffect } from "react";
import { But<PERSON> } from "@kit/ui/button";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";

interface ImageLightboxModalProps {
  imageUrls: string[];
  currentUrl: string;
  onClose: () => void;
  onPrev: () => void;
  onNext: () => void;
}

export function ImageLightboxModal({ imageUrls, currentUrl, onClose, onPrev, onNext }: ImageLightboxModalProps) {
  // Keyboard navigation and escape to close
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") onClose();
      else if (e.key === "ArrowLeft") onPrev();
      else if (e.key === "ArrowRight") onNext();
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [onClose, onPrev, onNext]);

  // Prevent background scroll when open
  useEffect(() => {
    const prev = document.body.style.overflow;
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = prev;
    };
  }, []);

  const index = Math.max(0, imageUrls.findIndex((u) => u === currentUrl));

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-2"
         role="dialog" aria-modal="true" aria-label="Image preview" data-test="image-lightbox">
      <div className="relative w-full h-full flex items-center justify-center">
        <Button
          variant="outline"
          size="icon"
          className="absolute right-2 top-2 z-10 rounded-full bg-background/80 hover:bg-background"
          onClick={onClose}
          aria-label="Close"
          data-test="close-lightbox-button"
        >
          <X className="h-6 w-6" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 rounded-full bg-background/80 hover:bg-background"
          onClick={onPrev}
          aria-label="Previous image"
          data-test="prev-image-button"
        >
          <ChevronLeft className="h-6 w-6" />
        </Button>

        <Button
          variant="outline"
          size="icon"
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 rounded-full bg-background/80 hover:bg-background"
          onClick={onNext}
          aria-label="Next image"
          data-test="next-image-button"
        >
          <ChevronRight className="h-6 w-6" />
        </Button>

        <div className="flex items-center justify-center w-full h-full">
          <Image
            src={currentUrl}
            alt="Enlarged view"
            height={1900}
            width={1900}
            className="max-w-[90vw] max-h-[90vh] object-contain mx-auto"
            unoptimized
          />
        </div>

        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-background/80 px-3 py-1 rounded-full text-sm">
          {index + 1} / {imageUrls.length}
        </div>
      </div>
    </div>
  );
}

