import React, { useCallback } from 'react';
import { useImageContent } from '../../context';
import { useZero } from '~/hooks/use-zero';
import { useParams } from 'next/navigation';
import { useQuery as useZeroQuery } from "@rocicorp/zero/react";
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { ImageGalleryGrid } from './image-gallery-grid';
import { ImageLightboxModal } from './image-lightbox-modal';

interface ImageGalleryProps {
  imageUrls: string[];
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({ imageUrls }) => {
  const { selectedImageUrl, setSelectedImageUrl, enlargedImageUrl, setEnlargedImageUrl } = useImageContent();
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [companyContent] = useZeroQuery(
    zero.query.company_content.where('company_id', '=', workspace.account.id),
    { ttl: '10m' }
  );

  const selectedCompanyContent = companyContent.filter((content: any) => content.id === contentId)[0];

  const isVideoUrl = (url: string): boolean =>
    url.includes('.mp4') || url.includes('.mov') || url.includes('.avi') || url.includes('.webm');

  const save = (url: string) => {
    const currentImages = (selectedCompanyContent?.image_urls as string[]) || [];
    const existingImages = currentImages.filter((existingUrl) => !isVideoUrl(existingUrl));
    zero.mutate.company_content.update({
      id: selectedCompanyContent?.id || '',
      values: { image_urls: [...existingImages, url] },
    });
  };

  const navigateImage = useCallback(
    (direction: 'prev' | 'next') => {
      if (!enlargedImageUrl) return;
      const currentIndex = imageUrls.findIndex((url) => url === enlargedImageUrl);
      if (currentIndex === -1) return;
      const newIndex = direction === 'prev'
        ? currentIndex === 0 ? imageUrls.length - 1 : currentIndex - 1
        : currentIndex === imageUrls.length - 1 ? 0 : currentIndex + 1;
      setEnlargedImageUrl(imageUrls[newIndex]);
    },
    [enlargedImageUrl, imageUrls, setEnlargedImageUrl]
  );

  if (imageUrls.length === 0) return null;

  return (
    <div className="space-y-4 mt-8">
      <ImageGalleryGrid
        imageUrls={imageUrls}
        selectedUrl={selectedImageUrl}
        onSelect={setSelectedImageUrl}
        onOpenModal={setEnlargedImageUrl}
        onSave={save}
      />

      {enlargedImageUrl && (
        <ImageLightboxModal
          imageUrls={imageUrls}
          currentUrl={enlargedImageUrl}
          onClose={() => setEnlargedImageUrl(null)}
          onPrev={() => navigateImage('prev')}
          onNext={() => navigateImage('next')}
        />
      )}
    </div>
  );
};