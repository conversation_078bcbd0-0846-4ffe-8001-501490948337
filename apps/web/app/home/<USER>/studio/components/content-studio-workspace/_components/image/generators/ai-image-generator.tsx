import React from 'react';
import { <PERSON><PERSON> } from "@kit/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@kit/ui/tabs";
import { Loader2, AlertCircle } from "lucide-react";
import { VisualDescriptionInput } from './visual-description-input';
import { AdvancedImageOptions } from '../advanced-image-options';
import { ImageGallery } from '../image-gallery';
import { useImageContent } from '../../../context';
import { Alert, AlertDescription } from "@kit/ui/alert";

interface AIImageGeneratorProps {
  onSubmit: () => void;
  isGenerating: boolean;
  error?: string | null;
}

export const AIImageGenerator: React.FC<AIImageGeneratorProps> = ({
  onSubmit,
  isGenerating,
  error
}) => {
  // Get state from context
  const { input, imageUrls } = useImageContent();

  return (
    <div className="space-y-4">
      {/* Visual Description Input - Shared between basic and advanced */}
      <VisualDescriptionInput />
      
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic" data-test="basic-tab">Basic</TabsTrigger>
          <TabsTrigger value="advanced" data-test="advanced-tab">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          {/* Basic mode - just the visual description input above */}
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4" data-test="advanced-options">
          <AdvancedImageOptions />
        </TabsContent>
      </Tabs>

      <Button 
        className="w-full" 
        onClick={onSubmit}
        disabled={isGenerating || !input.trim()}
        data-test="generate-images-button"
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" data-test="generating-loader" />
            Generating...
          </>
        ) : (
          'Generate Images'
        )}
      </Button>

      {error && (
        <Alert variant="destructive" data-test="error-message">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {imageUrls.length > 0 && (
        <ImageGallery imageUrls={imageUrls} />
      )}
    </div>
  );
};