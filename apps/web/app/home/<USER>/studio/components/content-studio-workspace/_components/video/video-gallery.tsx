import React from 'react';
import { useVideoContent } from '../../context';

interface VideoGalleryProps {
  videoUrls: string[];
}

export const VideoGallery: React.FC<VideoGalleryProps> = ({ videoUrls }) => {
  const { setSelectedEditorVideo, setSelectedVideoOption } = useVideoContent();

  const handleVideoSelect = (videoUrl: string) => {
    setSelectedEditorVideo(videoUrl);
    setSelectedVideoOption('selected');
  };

  return (
    <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
      {videoUrls.map((url, index) => (
        <div 
          key={index}
          onClick={() => handleVideoSelect(url)}
          className="cursor-pointer rounded-md overflow-hidden border hover:border-blue-500"
        >
          <video 
            className="w-full h-32 object-cover bg-black" 
            preload="metadata"
          >
            <source src={url} />
            Your browser does not support the video tag.
          </video>
        </div>
      ))}
    </div>
  );
}; 