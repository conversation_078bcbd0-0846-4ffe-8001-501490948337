'use client';
import { Block } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import { useEffect, useState, useCallback, useRef } from "react";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useBaseContent, useEditorContent, useImageContent } from "../../context";
import { useTheme } from "next-themes";
import { en as aiEn } from "@blocknote/xl-ai/locales";
import "@blocknote/xl-ai/style.css"; // add the AI stylesheet
import { createOpenAI } from '@ai-sdk/openai';
import { en } from "@blocknote/core/locales";
import {
  FormattingToolbar,
  FormattingToolbarController,
  getFormattingToolbarItems,
  useCreateBlockNote,
} from "@blocknote/react";
import {
  <PERSON><PERSON><PERSON>u<PERSON>ontroll<PERSON>,
  AIToolbarButton,
  createAIExtension,
} from "@blocknote/xl-ai";
import { useZero } from "~/hooks/use-zero";
import { debounce } from "lodash";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";

export default function ContentEditor({ 
  content_type, 
  imageUrl, 
  title, 
  rawContent,
  companyContentId,
  blockContent
}: { 
  content_type: string, 
  imageUrl: string, 
  title: string, 
  rawContent: string,
  companyContentId: string,
  blockContent: Block[] | null | undefined,
}) {
  // Track if this is the initial content load
  const { theme } = useTheme();
  const workspace = useTeamAccountWorkspace();

  const zero = useZero();  
  const [companyContent] = useZeroQuery(
    zero.query.company_content
    .where("company_id", "=", workspace.account.id),
    {
      ttl: '10m'
    }
  );

  const openai = createOpenAI({
    compatibility: 'strict', // strict mode, enable when using the OpenAI API
    apiKey: process.env.NEXT_PUBLIC_OPENROUTER_API_KEY || "my-secret-token",
    baseURL: "https://openrouter.ai/api/v1",
  });

  const editor = useCreateBlockNote({
    // Register the AI extension
    dictionary: {
      ...en,
      ai: aiEn, // add default translations for the AI extension
    },
    extensions: [
      createAIExtension({
        model: openai("gpt-4o"),
      }),
    ],
  });

  const [selectedImageBlock, setSelectedImageBlock] = useState<{
    id: string;
    url: string;
  } | null>(null);

  // Use context instead of direct hook usage
  const { setSelectedType } = useBaseContent();
  const { setSelectedImageOption, setSelectedImageId, setSelectedEditorImage } = useImageContent();
  const { setEditor } = useEditorContent();

  // Create debounced update function (saves every 5 seconds)
  const debouncedUpdateContent = useCallback(
    debounce((blocks: Block[]) => {
      zero.mutate.company_content.update({
        id: companyContentId,
        values: {
          content_editor_template: blocks,
        }
      });
    }, 30000),
    [companyContentId, zero]
  );

  // Track if selection change is being processed
  const isProcessingSelection = useRef(false);

  // Handle initial content setup
  useEffect(() => {
    if (!editor) return;
    setEditor(editor);

    async function updateContent() {
      let blocks;
       if (blockContent) {
        blocks = blockContent;
      } else {
        blocks = await editor.tryParseMarkdownToBlocks(`# ${title}`);
          zero.mutate.company_content.update({
            id: companyContentId,
            values: {
              content_editor_template: blocks,
            }
          });
      }
      editor.replaceBlocks(editor.document, blocks);
    }

    updateContent();
  }, [companyContentId, zero, editor, title, blockContent, setEditor]);

  // Handle image selection for editing
  useEffect(() => {

    if (selectedImageBlock) {
      setSelectedType('image');
      setSelectedImageOption('selected');
      setSelectedEditorImage(selectedImageBlock.url);
      setSelectedImageId(selectedImageBlock.id);
    }
  }, [selectedImageBlock, setSelectedType, setSelectedImageOption, setSelectedEditorImage, setSelectedImageId]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedUpdateContent.cancel();
    };
  }, [debouncedUpdateContent]);

  return (
    <>
    <BlockNoteView
      editor={editor} 
      theme={theme as "light" | "dark"} 
      formattingToolbar={false}
      onSelectionChange={() => {
        // Prevent re-rendering during selection processing
        if (isProcessingSelection.current) return;
        
        try {
          isProcessingSelection.current = true;
          const textCursorPosition = editor.getTextCursorPosition();
          const block = textCursorPosition.block;
          if (block?.type === 'image' && block.props?.url) {
            setSelectedImageBlock({
              id: block.id,
              url: block.props.url
            });
          } else {
            // Only update these states if they've actually changed
            if (selectedImageBlock) {
              setSelectedEditorImage(null);
              setSelectedImageId(null);
            }
          }
        } finally {
          // Reset the flag after a short delay to ensure selection is stable
          setTimeout(() => {
            isProcessingSelection.current = false;
          }, 0);
        }
      }}
      onChange={() => {
        const blocks = editor.document;
        debouncedUpdateContent(blocks);
      }}
    >
       <AIMenuController />
       <FormattingToolbarWithAI />
    </BlockNoteView>
    </>
  );
}

// Formatting toolbar with the `AIToolbarButton` added
function FormattingToolbarWithAI() {
  return (
    <FormattingToolbarController
      formattingToolbar={() => (
        <FormattingToolbar>
          {...getFormattingToolbarItems()}
          {/* Add the AI button */}
          <AIToolbarButton />
        </FormattingToolbar>
      )}
    />
  );
}
