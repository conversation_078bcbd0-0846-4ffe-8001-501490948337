import React, { ReactNode } from 'react';
import { BaseContentProvider, useBaseContent } from './base-context';
import { TextContentProvider, useTextContent } from './text-context';
import { ImageContentProvider, useImageContent } from './image-context';
import { EditorContentProvider, useEditorContent } from './editor-context';
import { VideoContentProvider, useVideoContent } from './video-context';

// Main provider component that composes all domain contexts
export function ContentStudioProvider({ children }: { children: ReactNode }) {
  return (
    <BaseContentProvider>
      <TextContentProvider>
        <ImageContentProvider>
          <EditorContentProvider>
            <VideoContentProvider>
              {children}
            </VideoContentProvider>
          </EditorContentProvider>
        </ImageContentProvider>
      </TextContentProvider>
    </BaseContentProvider>
  );
}

// Combined hook for convenience when needing all contexts
export function useContentStudio() {
  return {
    ...useBaseContent(),
    ...useTextContent(),
    ...useImageContent(),
    ...useEditorContent(),
    ...useVideoContent()
  };
}

// Re-export individual context hooks for direct imports
export {
  useBaseContent,
  useTextContent,
  useImageContent,
  useEditorContent,
  useVideoContent
};