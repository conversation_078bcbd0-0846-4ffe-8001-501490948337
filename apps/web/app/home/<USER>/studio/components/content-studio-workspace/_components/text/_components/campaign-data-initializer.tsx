'use client';

import { useEffect } from 'react';
import { SelectedDocument } from '~/components/document-selector';

interface CampaignDataInitializerProps {
  selectedCompanyContent: any;
  companyCampaigns: any[];
  productDocuments: any[];
  icps: any[];
  personas: any[];
  savedResearch: any[];
  setSelectedIcps: (icps: any[]) => void;
  setSelectedPersonas: (personas: any[]) => void;
  setSelectedResearch: (research: any[]) => void;
  setSelectedDocuments: (documents: SelectedDocument[]) => void;
}

export const useCampaignDataInitializer = ({
  selectedCompanyContent,
  companyCampaigns,
  productDocuments,
  icps,
  personas,
  savedResearch,
  setSelectedIcps,
  setSelectedPersonas,
  setSelectedResearch,
  setSelectedDocuments,
}: CampaignDataInitializerProps) => {
  useEffect(() => {
    if (selectedCompanyContent?.campaign_id && companyCampaigns?.length > 0) {
      const associatedCampaign = companyCampaigns.find(
        (campaign: any) => campaign.id === selectedCompanyContent.campaign_id,
      );
      
      if (associatedCampaign) {
        // Pre-populate target_icps - convert IDs to full objects
        if (
          associatedCampaign.target_icps &&
          Array.isArray(associatedCampaign.target_icps) &&
          icps?.length > 0
        ) {
          const icpObjects = associatedCampaign.target_icps
            .map((id: string) => icps.find((icp: any) => icp.id === id))
            .filter(Boolean)
            .map((icp: any) => ({
              id: icp.id,
              name: icp.name || 'ICP',
              data: icp.data
            }));
          setSelectedIcps(icpObjects);
        }

        // Pre-populate target_personas - convert IDs to full objects
        if (
          associatedCampaign.target_personas &&
          Array.isArray(associatedCampaign.target_personas) &&
          personas?.length > 0
        ) {
          const personaObjects = associatedCampaign.target_personas
            .map((id: string) => personas.find((persona: any) => persona.id === id))
            .filter(Boolean)
            .map((persona: any) => ({
              id: persona.id,
              name: persona.name || 'Persona',
              data: persona.data
            }));
          setSelectedPersonas(personaObjects);
        }

        // Pre-populate external_research - convert IDs to full objects
        if (
          associatedCampaign.external_research &&
          Array.isArray(associatedCampaign.external_research) &&
          savedResearch?.length > 0
        ) {
          const researchObjects = associatedCampaign.external_research
            .map((id: string) => savedResearch.find((research: any) => research.id === id))
            .filter(Boolean)
            .map((research: any) => ({
              id: research.id,
              title: research.title || research.topic || 'Research Item',
              research_type: research.research_type,
              description: research.description,
            }));
          setSelectedResearch(researchObjects);
        }

        // Pre-populate documents - convert product IDs to SelectedDocument objects
        if (
          associatedCampaign.products &&
          Array.isArray(associatedCampaign.products) &&
          productDocuments?.length > 0
        ) {
          const selectedDocs: SelectedDocument[] = associatedCampaign.products
            .map((docId: string) => {
              const doc = productDocuments.find((pd: any) => pd.id === docId);
              return doc
                ? {
                    id: doc.id,
                    documentTitle: doc.title,
                    content: doc.content || '',
                  }
                : null;
            })
            .filter(Boolean) as SelectedDocument[];

          setSelectedDocuments(selectedDocs);
        }
      }
    }
  }, [
    selectedCompanyContent,
    companyCampaigns,
    productDocuments,
    icps,
    personas,
    savedResearch,
    setSelectedIcps,
    setSelectedPersonas,
    setSelectedResearch,
    setSelectedDocuments,
  ]);
};
