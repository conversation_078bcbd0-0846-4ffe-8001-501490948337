'use client';

import { useCallback } from 'react';
import { toast } from '@kit/ui/sonner';
import { SelectedDocument } from '~/components/document-selector';

interface ContentGenerationServiceProps {
  selectedCompanyContent: any;
  editor: any;
  taskTitle: string;
  taskDescription: string;
  selectedPersonas: any[];
  selectedIcps: any[];
  selectedResearch: any[];
  selectedDocuments: SelectedDocument[];
  companyBrand: any;
  companyCampaigns: any[];
  zero: any;
  setIsGenerating: (generating: boolean) => void;
  setHasError: (error: boolean) => void;
}

export const useContentGenerationService = ({
  selectedCompanyContent,
  editor,
  taskTitle,
  taskDescription,
  selectedPersonas,
  selectedIcps,
  selectedResearch,
  selectedDocuments,
  companyBrand,
  companyCampaigns,
  zero,
  setIsGenerating,
  setHasError,
}: ContentGenerationServiceProps) => {
  // Function to handle streaming content updates
  const handleStreamingContent = useCallback(async (content: string) => {
    if (!editor) return;

    try {
      // Parse the content as markdown and convert to blocks
      const blocks = await editor.tryParseMarkdownToBlocks(content);
      
      // Replace the current content with the new blocks
      editor.replaceBlocks(editor.document, blocks);
    } catch (error) {
      // Ignore parse errors for partial chunks; wait for more content
      console.warn('Streaming parse error (will retry on next chunk):', error);
      return;
    }
  }, [editor]);

  // Create a streaming function to generate content
  const generateContentStreaming = useCallback(async () => {
    setIsGenerating(true);
    setHasError(false);

    if (!selectedCompanyContent?.id || !editor) {
      setIsGenerating(false);
      return;
    }

    try {
      // Get the associated campaign to access its objective
      const associatedCampaign = companyCampaigns?.find(
        (campaign: any) => campaign.id === selectedCompanyContent.campaign_id,
      );

      // Prepare content generation parameters for the generate-task-content endpoint
      const contentParams = {
        taskTitle: taskTitle || selectedCompanyContent.task_title || 'Content Generation',
        taskDescription,
        contentType: selectedCompanyContent.content_type || 'blog_post',
        channel: selectedCompanyContent.channel || 'website',
        campaignGoal: associatedCampaign?.objective || 'awareness',
        productInformation: selectedDocuments.map(doc => doc.content).join('\n\n'),
        targetICPs: selectedIcps.map(icp => icp.name).join(', '),
        targetPersonas: selectedPersonas.map(persona => persona.name).join(', '),
        companyBrand: companyBrand || {},
        externalResearch: selectedResearch.map(research => research.description).join('\n\n')
      };

      // Call the generate-task-content endpoint with streaming
      const response = await fetch('/api/ai/generate-task-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contentParams),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Response error:', errorData);
        throw new Error(errorData.error || 'Failed to generate content');
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body available for streaming');
      }

      const decoder = new TextDecoder();
      let accumulatedContent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('Stream reading complete');
          break;
        }

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              // Update the database with the final content
              if (selectedCompanyContent?.id) {

                zero.mutate.company_content.update({
                  id: selectedCompanyContent.id,
                  values: {
                    content_editor_template: editor.document,
                    is_generating: false,
                    error_generating: false,
                  },
                });
              }
              
              toast.success('Content generated successfully!');
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.content && parsed.type === 'content') {
                accumulatedContent += parsed.content;
                await handleStreamingContent(accumulatedContent);
              }
            } catch (e) {
              // Ignore parsing errors for malformed chunks
              console.warn('Failed to parse streaming chunk:', data);
            }
          }
        }
      }

    } catch (error) {
      console.error('Error generating content:', error);
      setHasError(true);
      toast.error('Failed to generate content. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  }, [
    selectedCompanyContent,
    editor,
    taskTitle,
    taskDescription,
    selectedPersonas,
    selectedIcps,
    selectedResearch,
    selectedDocuments,
    companyBrand,
    zero,
    companyCampaigns,
    handleStreamingContent,
    setIsGenerating,
    setHasError,
  ]);

  return {
    generateContentStreaming,
  };
};
