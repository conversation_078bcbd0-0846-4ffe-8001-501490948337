'use client';

import React from 'react';
import { Label } from '@kit/ui/label';
import { Textarea } from '@kit/ui/textarea';

interface BasicOptionsProps {
  taskDescription: string;
  onDescriptionChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  isGenerating: boolean;
}

export const BasicOptions: React.FC<BasicOptionsProps> = ({
  taskDescription,
  onDescriptionChange,
  isGenerating,
}) => {
  return (
    <div className="space-y-2">
      <Label className="text-lg font-semibold">Topic</Label>
      <br />
      <Label className="text-muted-foreground text-sm">
        Enter the topic or basis used to generate the content.
      </Label>

      <Textarea
        value={taskDescription}
        onChange={onDescriptionChange}
        className="text-muted-foreground"
        rows={4}
        disabled={isGenerating}
      />
    </div>
  );
};
