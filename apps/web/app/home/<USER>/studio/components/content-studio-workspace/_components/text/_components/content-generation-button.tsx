'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@kit/ui/button';

interface ContentGenerationButtonProps {
  isGenerating: boolean;
  hasError: boolean;
  taskTitle: string;
  taskDescription: string;
  onGenerate: () => void;
}

export const ContentGenerationButton: React.FC<ContentGenerationButtonProps> = ({
  isGenerating,
  hasError,
  taskTitle,
  taskDescription,
  onGenerate,
}) => {
  const isDisabled = isGenerating || !taskTitle.trim() || !taskDescription.trim();

  return (
    <>
      <Button
        onClick={onGenerate}
        disabled={isDisabled}
      >
        {isGenerating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating content...
          </>
        ) : (
          'Generate'
        )}
      </Button>

      {hasError && (
        <div className="mt-2">
          <button
            onClick={onGenerate}
            className="cursor-pointer text-sm text-red-600 underline hover:text-red-800"
          >
            Error Generating, Click to try again
          </button>
        </div>
      )}
    </>
  );
};
