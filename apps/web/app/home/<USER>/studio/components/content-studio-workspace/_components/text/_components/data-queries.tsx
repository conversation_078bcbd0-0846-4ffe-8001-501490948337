'use client';

import { useMemo } from 'react';
import { useParams } from 'next/navigation';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from '~/hooks/use-zero';

export const useDataQueries = () => {
  const params = useParams();
  const contentId = params.id;
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();

  const [companyContent] = useZeroQuery(
    zero.query.company_content.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [savedResearch] = useZeroQuery(
    zero.query.saved_research.where('account_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [companyBrand] = useZeroQuery(
    zero.query.company_brand.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [personas] = useZeroQuery(
    zero.query.personas.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [icps] = useZeroQuery(
    zero.query.icps.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [companyCampaigns] = useZeroQuery(
    zero.query.company_campaigns.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const [productDocuments] = useZeroQuery(
    zero.query.product_documents.where('company_id', '=', workspace.account.id),
    {
      ttl: '1m',
    },
  );

  const selectedCompanyContent = useMemo(() => {
    return companyContent.filter(
      (content: any) => content.id === contentId,
    )[0];
  }, [companyContent, contentId]);

  return {
    zero,
    workspace,
    selectedCompanyContent,
    savedResearch,
    companyBrand,
    personas,
    icps,
    companyCampaigns,
    productDocuments,
  };
};
