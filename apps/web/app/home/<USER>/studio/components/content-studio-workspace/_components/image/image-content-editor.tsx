import React from 'react';
import { <PERSON><PERSON> } from "@kit/ui/button";
import { ImageSubmenu } from './image-submenu';
import { AIImageGenerator } from './generators/ai-image-generator';
import { StockImageSelector } from './selectors/stock-image-selector';
import { ExistingImageSelector } from './selectors/existing-image-selector';
import { ImageUploader } from './image-uploader';
import { SelectedImageEditor } from './editors/selected-image-editor';
import { useImageContent } from '../../context';

interface ImageContentEditorProps {
  onGetVisualDescription: () => Promise<any>;
  onSubmit: () => void;
  isGenerating: boolean;
  isGeneratingDescription: boolean;
}

export const ImageContentEditor: React.FC<ImageContentEditorProps> = ({
  onGetVisualDescription,
  onSubmit,
  isGenerating,
  isGeneratingDescription
}) => {
  // Get all state from context
  const {
    selectedImageOption,
    setSelectedImageOption,
    error,
  } = useImageContent();
  
  // If no option is selected, show the submenu
  if (selectedImageOption === null) {
    return (
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Select Image Source</h2>
        <ImageSubmenu />
      </div>
    );
  }
  
  // Show the selected option's component
  switch (selectedImageOption) {
    case 'ai':
      return (
        <AIImageGenerator
          onSubmit={onSubmit}
          isGenerating={isGenerating}
          error={error}
        />
      );
    case 'stock':
      return <StockImageSelector />;
    case 'upload':
      return <ImageUploader />;
    case 'existing':
      return <ExistingImageSelector  />;
    case 'selected':
      return (
        <SelectedImageEditor />
      );
    default:
      return (
        <div className="space-y-4">
          <Button
            variant="ghost"
            className="mb-4"
            onClick={() => {console.log("clicked,  ", selectedImageOption); setSelectedImageOption(null);}}
          >
            Back
          </Button>
          <p>Option not implemented yet.</p>
        </div>
      );
  }
};