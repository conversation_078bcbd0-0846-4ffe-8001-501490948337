import React, { useState } from 'react';
import { But<PERSON> } from "@kit/ui/button";
import { Upload } from "lucide-react";
import { ImageGallery } from './image-gallery';


export const ImageUploader: React.FC = () => {
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  // Mock file upload - in a real implementation, this would upload the file to your server/storage
  const handleFileUpload = () => {
    // Simulate a new upload with a placeholder image URL
    const mockImageUrl = "https://placeholder.pics/svg/300x200/DEDEDE/555555/Image%20Placeholder";
    setUploadedImages([...uploadedImages, mockImageUrl]);
  };
  

  return (
    <div className="space-y-4">
      <div className="mb-4">
        <h3 className="text-md font-medium mb-2">Upload Your Own Image</h3>
        <p className="text-sm text-muted-foreground">Upload an image from your computer.</p>
      </div>
      
      <div 
        className="border-2 border-dashed border-muted-foreground/20 rounded-md p-8 flex flex-col items-center justify-center gap-4"
        onDragOver={(e) => e.preventDefault()}
        onDrop={(e) => {
          e.preventDefault();
          handleFileUpload();
        }}
      >
        <div className="bg-primary/10 p-4 rounded-full">
          <Upload className="h-8 w-8 text-primary" />
        </div>
        <div className="text-center">
          <p className="text-sm font-medium">Drag and drop your image here</p>
          <p className="text-xs text-muted-foreground">or</p>
        </div>
        <Button onClick={handleFileUpload}>Browse Files</Button>
        <p className="text-xs text-muted-foreground">
          Supports: JPG, PNG, GIF up to 5MB
        </p>
      </div>
      
      {uploadedImages.length > 0 && (
        <>
          <h3 className="text-md font-medium mt-6 mb-2">Uploaded Images</h3>
          <ImageGallery 
            imageUrls={uploadedImages}
          />
        </>
      )}
      
      <p className="text-xs text-muted-foreground mt-4">
        This is a placeholder. Image upload functionality will be implemented in a future update.
      </p>
    </div>
  );
}