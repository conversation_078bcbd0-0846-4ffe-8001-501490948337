'use client';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { TeamAccountLayoutPageHeader } from '../_components/layout';
import { VideoStudioWorkspace } from './_components/video-studio-workspace';

const VideoStudioPage = () => {
  const workspace = useTeamAccountWorkspace();

  return (
    <>
      {/* <TeamAccountLayoutPageHeader
        account={workspace.account.slug}
        title={'Video Studio'}
        description={<AppBreadcrumbs />}
      /> */}
      
      {/* Full height container for video editor */}
      <div className="w-full h-full overflow-hidden">
        <VideoStudioWorkspace />
      </div>
    </>
  );
};

export default VideoStudioPage;

