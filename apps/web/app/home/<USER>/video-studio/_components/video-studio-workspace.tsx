'use client';

import ReactVideoEditor from '@/components/video-editor/v7/react-video-editor';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useMemo } from 'react';
import { VideoProjectSelector } from './video-project-selector';

export const VideoStudioWorkspace = () => {
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();

  // Get all projects for this account, sorted by most recent
  const [allProjects, allProjectsResult] = useZeroQuery(
    zero?.query.video_projects
      .where('account_id', '=', workspace.account.id)
      .orderBy("updated_at", "desc"),
    {
      ttl: '5m'
    }
  );

  // Get user cache to check for selected video project
  const [userCacheResults] = useZeroQuery(
    zero?.query.user_cache.where('user_id', '=', workspace.user?.id || ''),
    {
      ttl: '1d'
    }
  );

  const userCache = userCacheResults?.[0];

  // Determine current project ID based on user_cache and available projects
  const currentProjectId = useMemo(() => {
    // If still loading, return null
    if (!zero || !workspace.user?.id || allProjectsResult.type !== 'complete') {
      return null;
    }

    // If user has no projects, create first project
    if (!allProjects || allProjects.length === 0) {
      const projectId = crypto.randomUUID();
      
      // Create the first project asynchronously using custom mutator
      (zero.mutate as any).video_projects.insert({
        id: projectId,
        account_id: workspace.account.id,
        user_id: workspace.user.id,
        name: 'My First Video Project',
        description: 'Welcome to the video studio! Start creating amazing videos.',
        aspect_ratio: '16:9',
        fps: 30,
        width: 1920,
        height: 1080,
        status: 'draft',
        is_template: false,
        created_by: workspace.user.id,
        updated_by: workspace.user.id,
      })
      console.log("projectId", projectId);
      (zero.mutate.user_cache as any).update({
        user_id: workspace.user.id,
        values: {
          selected_video_project: projectId,
        }
      });

      return projectId;
    }

    // Check if user has a selected project in cache and it exists in current projects
    if (userCache?.selected_video_project) {

      const cachedProject = allProjects.find(p => p.id === userCache.selected_video_project);

      if (cachedProject) {
        return cachedProject.id;
      }
    }

    // Fallback to most recent project
    return allProjects[0]?.id || null;
  }, [allProjectsResult.type, userCache?.selected_video_project]);

  // Show loading state while initializing
  if (!currentProjectId) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading video editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
   
      {/* Video Editor */}
      <div className="flex-1 h-full">
        <ReactVideoEditor projectId={currentProjectId} />
      </div>
    </div>
  );
}