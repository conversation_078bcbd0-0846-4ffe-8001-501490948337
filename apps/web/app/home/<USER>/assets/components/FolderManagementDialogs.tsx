'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Pencil, Trash, MoreVertical } from 'lucide-react';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';

import { Button } from '@kit/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@kit/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@kit/ui/form';
import { Input } from '@kit/ui/input';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@kit/ui/alert-dialog';
import { toast } from '@kit/ui/sonner';
import { Trans } from '@kit/ui/trans';

import { deleteAssetFolder, renameAssetFolder } from '~/services/storage';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';

const renameFolderSchema = z.object({
  newName: z.string().min(1, 'Folder name is required').max(50, 'Folder name is too long'),
});

type RenameFolderFormData = z.infer<typeof renameFolderSchema>;

interface FolderManagementDialogsProps {
  folderName: string;
  onFolderUpdated?: () => void;
}

export function FolderManagementDialogs({ folderName, onFolderUpdated }: FolderManagementDialogsProps) {
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const queryClient = useQueryClient();
  const { account } = useTeamAccountWorkspace();

  const form = useForm<RenameFolderFormData>({
    resolver: zodResolver(renameFolderSchema),
    defaultValues: {
      newName: folderName,
    },
  });

  const renameMutation = useMutation({
    mutationFn: (data: RenameFolderFormData) => 
      renameAssetFolder(folderName, data.newName, account.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assetFolders'] });
      setIsRenameDialogOpen(false);
      form.reset();
      toast.success('Folder renamed successfully');
      onFolderUpdated?.();
    },
    onError: (error) => {
      console.error('Failed to rename folder:', error);
      toast.error('Failed to rename folder. Please try again.');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: () => deleteAssetFolder(folderName, account.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assetFolders'] });
      setIsDeleteDialogOpen(false);
      toast.success('Folder deleted successfully');
      onFolderUpdated?.();
    },
    onError: (error) => {
      console.error('Failed to delete folder:', error);
      toast.error('Failed to delete folder. Please try again.');
    },
  });

  const handleRename = (data: RenameFolderFormData) => {
    if (data.newName === folderName) {
      setIsRenameDialogOpen(false);
      return;
    }
    renameMutation.mutate(data);
  };

  const handleDelete = () => {
    deleteMutation.mutate();
  };

  return {
    menu: (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="icon" 
            className="absolute top-2 right-2 h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={(e) => e.stopPropagation()}
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align="end"
          onClick={(e) => e.stopPropagation()}
        >
          <DropdownMenuItem 
            onClick={(e) => {
              e.stopPropagation();
              setIsRenameDialogOpen(true);
            }}
          >
            <Pencil className="mr-2 h-4 w-4" />
            <Trans i18nKey="common:rename" defaults="Rename" />
          </DropdownMenuItem>
          <DropdownMenuItem
            className="text-red-600 focus:text-red-600"
            onClick={(e) => {
              e.stopPropagation();
              setIsDeleteDialogOpen(true);
            }}
          >
            <Trash className="mr-2 h-4 w-4" />
            <Trans i18nKey="common:delete" defaults="Delete" />
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ),
    dialogs: (
      <>
        {/* Rename Dialog */}
        <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
          <DialogContent onClick={(e) => e.stopPropagation()}>
            <DialogHeader>
              <DialogTitle>
                <Trans i18nKey="assets:renameFolder" defaults="Rename Folder" />
              </DialogTitle>
              <DialogDescription>
                <Trans 
                  i18nKey="assets:renameFolderDescription" 
                  defaults="Enter a new name for the folder."
                />
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleRename)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="newName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        <Trans i18nKey="common:folderName" defaults="Folder Name" />
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter folder name"
                          {...field}
                          disabled={renameMutation.isPending}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsRenameDialogOpen(false);
                    }}
                    disabled={renameMutation.isPending}
                  >
                    <Trans i18nKey="common:cancel" defaults="Cancel" />
                  </Button>
                  <Button
                    type="submit"
                    disabled={renameMutation.isPending || !form.formState.isValid}
                    onClick={(e) => e.stopPropagation()}
                  >
                    {renameMutation.isPending ? (
                      <Trans i18nKey="common:renaming" defaults="Renaming..." />
                    ) : (
                      <Trans i18nKey="common:rename" defaults="Rename" />
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent onClick={(e) => e.stopPropagation()}>
            <AlertDialogHeader>
              <AlertDialogTitle>
                <Trans i18nKey="assets:deleteFolder" defaults="Delete Folder" />
              </AlertDialogTitle>
              <AlertDialogDescription>
                <Trans 
                  i18nKey="assets:deleteFolderWarning" 
                  values={{ folderName }}
                  defaults={`Are you sure you want to delete the folder ${folderName}? This action will permanently delete all files in this folder and cannot be undone.`}
                />
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel 
                disabled={deleteMutation.isPending}
                onClick={(e) => e.stopPropagation()}
              >
                <Trans i18nKey="common:cancel" defaults="Cancel" />
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete();
                }}
                disabled={deleteMutation.isPending}
                className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
              >
                {deleteMutation.isPending ? (
                  <Trans i18nKey="common:deleting" defaults="Deleting..." />
                ) : (
                  <Trans i18nKey="common:delete" defaults="Delete" />
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    )
  };
}
