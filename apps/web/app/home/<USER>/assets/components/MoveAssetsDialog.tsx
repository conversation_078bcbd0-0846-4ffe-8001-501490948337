"use client";

import { useMemo, useState } from "react";
import { But<PERSON> } from "@kit/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@kit/ui/dialog";
import { Input } from "@kit/ui/input";
import { AssetFolder } from "~/types/assets";

interface MoveAssetsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  folders: AssetFolder[];
  currentFolder: string;
  onConfirm: (destination: string) => void;
  isPending?: boolean;
}

export function MoveAssetsDialog({
  open,
  onOpenChange,
  folders,
  currentFolder,
  onConfirm,
  isPending,
}: MoveAssetsDialogProps) {
  const [query, setQuery] = useState("");
  const [selected, setSelected] = useState<string>("");

  const options = useMemo(() => {
    const q = query.toLowerCase();
    return folders
      .filter((f) => f.name && f.path !== currentFolder)
      .filter((f) => !q || f.name.toLowerCase().includes(q))
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [folders, query, currentFolder]);

  const canConfirm = !!selected && selected !== currentFolder;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Move to Folder</DialogTitle>
        </DialogHeader>
        <div className="space-y-3 p-1">
          <Input
            placeholder="Search folders..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <div className="max-h-64 overflow-auto rounded border">
            {options.length === 0 ? (
              <div className="p-3 text-sm text-muted-foreground">No folders</div>
            ) : (
              <ul>
                {options.map((f) => (
                  <li key={`${f.type}:${f.path}`}>
                    <label className="flex cursor-pointer items-center gap-3 px-3 py-2 hover:bg-accent">
                      <input
                        type="radio"
                        name="dest"
                        className="h-4 w-4"
                        value={f.path}
                        onChange={() => setSelected(f.path)}
                        checked={selected === f.path}
                        disabled={isPending}
                      />
                      <span className="text-sm">
                        {f.name}
                        {f.type ? <span className="ml-2 text-xs text-muted-foreground">({f.type})</span> : null}
                      </span>
                    </label>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={!!isPending}>
            Cancel
          </Button>
          <Button onClick={() => canConfirm && onConfirm(selected)} disabled={!canConfirm || !!isPending}>
            {isPending ? "Moving..." : "Move"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

