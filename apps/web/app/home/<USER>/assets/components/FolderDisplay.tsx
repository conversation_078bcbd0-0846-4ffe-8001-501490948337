import { useState } from 'react';

import Image from 'next/image';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { AxiosResponse } from 'axios';
import { upload } from '@vercel/blob/client';
import {
  ChevronLeft,
  FileAudioIcon,
  FileIcon,
  ImageIcon,
  Loader,
  MoreVertical,
  Trash,
  Upload,
  VideoIcon,
  X,
} from 'lucide-react';

import { toast } from '@kit/ui/sonner';

import { Button } from '@kit/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import {
  deleteAssetFile,
  listFolderContents,
  uploadMultipleAssetFiles,
  listAssetFolders,
  moveAssetFiles,
} from '~/services/storage';
import { AssetFolder, StorageFile } from '~/types/assets';
import { getLocalApi } from '~/utils/api.util';
import { truncateFilename } from '~/utils/string.util';

import { AudioPlayer } from './AudioPlayer';
import { MoveAssetsDialog } from './MoveAssetsDialog';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';

interface FolderDisplayProps {
  folderName: string;
  onBack: () => void;
}

export function FolderDisplay({ folderName, onBack }: FolderDisplayProps) {
  const queryClient = useQueryClient();
  const [selectedFile, setSelectedFile] = useState<StorageFile | null>(null);
  const [imageAspect, setImageAspect] = useState<'portrait' | 'landscape'>(
    'landscape',
  );
  const workspace = useTeamAccountWorkspace();
  const z = useZero();

  // Query asset records from database for processing status
  const [assets] = useZeroQuery(
    z.query.assets
      .where('account_id', workspace.account.id!)
      .where('folder_name', folderName)
  );

  const {
    data: files = [],
    isLoading,
    refetch,
  } = useQuery<StorageFile[]>({
    queryKey: ['folderContents', workspace.account.id, folderName],
    queryFn: () => listFolderContents(folderName, workspace.account.id),
  });

  const [selected, setSelected] = useState<Set<string>>(new Set());
  const [isMoveOpen, setIsMoveOpen] = useState(false);

  const { data: folders = [] } = useQuery<AssetFolder[]>({
    queryKey: ['assetFolders', workspace.account.id],
    queryFn: () => listAssetFolders(workspace.account.id),
  });

  const moveMutation = useMutation({
    mutationFn: async (destination: string) => {
      const selectedNames = (files || [])
        .filter((f) => selected.has(f.path))
        .map((f) => f.path.split('/').pop() || '');
      
      
      if (selectedNames.length === 0) return { success: true, moved: 0 } as any;
      return moveAssetFiles(workspace.account.id, folderName, destination, selectedNames);
    },
    onMutate: async () => {
      await queryClient.cancelQueries({ queryKey: ['folderContents', folderName] });
      const previous = queryClient.getQueryData<StorageFile[]>(['folderContents', folderName]);
      queryClient.setQueryData<StorageFile[]>(['folderContents', folderName], (old) =>
        (old || []).filter((f) => !selected.has(f.path))
      );
      return { previous } as const;
    },
    onError: (err, _vars, ctx) => {
      toast.error(`Failed to move files: ${err instanceof Error ? err.message : 'Unknown error'}`);
      if (ctx?.previous) {
        queryClient.setQueryData(['folderContents', folderName], ctx.previous);
      }
    },
    onSuccess: (result) => {
      setSelected(new Set());
      setIsMoveOpen(false);
      queryClient.invalidateQueries({ queryKey: ['folderContents', workspace.account.id, folderName] });
      queryClient.invalidateQueries({ queryKey: ['assetFolders', workspace.account.id] });
      toast.success(`Successfully moved ${result.moved} file(s)`);
    },
  });

  const handleSelectAll = () => setSelected(new Set((files || []).map((f) => f.path)));
  const handleDeselectAll = () => setSelected(new Set());
  const toggleSelect = (file: StorageFile) => {
    setSelected((prev) => {
      const next = new Set(prev);
      if (next.has(file.path)) next.delete(file.path);
      else next.add(file.path);
      return next;
    });
  };


  const extractTranscript = useMutation({
    mutationFn: async (file: { url: string; name: string }) => {
      console.log('Extracting transcript for:', workspace.account.id);
      if (!workspace.account.id) {
        throw new Error('No company ID found');
      }

      const response: AxiosResponse = await getLocalApi().post(
        '/ai/extract-audio-transcript',
        {
          audioUrl: file.url,
          fileName: file.name,
          folderName,
          companyId: workspace.account.id,
        },
      );

      if (response.status !== 200) {
        throw new Error('Failed to extract transcript');
      }

      return response.data;
    },
  });

  const extractVideoTranscript = useMutation({
    mutationFn: async (file: { url: string; name: string; assetId?: string }) => {
      console.log('Extracting transcript from video for:', workspace.account.id);
      if (!workspace.account.id) {
        throw new Error('No company ID found');
      }

      const response: AxiosResponse = await getLocalApi().post(
        '/ai/extract-video-audio', // API endpoint unchanged for backward compatibility
        {
          videoUrl: file.url,
          fileName: file.name,
          folderName,
          companyId: workspace.account.id,
          assetId: file.assetId, // Pass asset ID for precise database updates
        },
      );

      if (response.status !== 200) {
        throw new Error('Failed to extract transcript from video');
      }

      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate queries to refresh the folder contents and show the new transcript file
      queryClient.invalidateQueries({
        queryKey: ['folderContents', workspace.account.id, folderName],
      });
      
      console.log('Video transcript extracted successfully:', data);
    },
  });

  // Asset insertion using Zero for immediate database tracking
  const insertAsset =  (assetData: any) => {
    console.log('Inserting asset record into Zero:', assetData);
    (z.mutate.assets as any).insert(assetData);
  };

  // Helper function to create asset record from uploaded file
  const createAssetRecord = (file: StorageFile, assetId: string) => {
    const fileName = file.path.split('/').pop() || '';
    const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
    
    // Determine if this file type supports transcript extraction
    const isAudioFile = ['mp3', 'wav', 'ogg', 'm4a'].includes(fileExtension);
    const isVideoFile = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(fileExtension);
    const supportsTranscript = isAudioFile || isVideoFile;
    
    console.log('Creating asset record:', {
      id: assetId,
      account_id: workspace.account.id!,
      file_name: fileName,
      file_path: file.path,
      file_size: (file as any).size || undefined,
      file_type: file.type || undefined,
      folder_name: folderName,
      original_url: file.url,
      file_extension: fileExtension,
      has_transcript: false,
      processing_status: supportsTranscript ? 'processing' : 'completed',
      supportsTranscript,
    });
    
    return {
      id: assetId, // Use the provided UUID
      account_id: workspace.account.id!,
      file_name: fileName,
      file_path: file.path,
      file_size: (file as any).size || undefined,
      file_type: file.type || undefined,
      folder_name: folderName,
      original_url: file.url,
      file_extension: fileExtension,
      has_transcript: false,
      processing_status: supportsTranscript ? ('processing' as const) : ('completed' as const),
      processing_started_at: supportsTranscript ? Date.now() : undefined,
    };
  };

  const extractAudioTranscript = useMutation({
    mutationFn: async (file: { url: string; name: string; assetId?: string }) => {
      console.log('Extracting transcript from audio for:', workspace.account.id, 'with asset ID:', file.assetId);
      if (!workspace.account.id) {
        throw new Error('No company ID found');
      }

      const response: AxiosResponse = await getLocalApi().post(
        '/ai/extract-audio-transcript',
        {
          audioUrl: file.url,
          fileName: file.name,
          folderName,
          companyId: workspace.account.id,
          assetId: file.assetId, // Pass asset ID for precise database updates
        },
      );

      if (response.status !== 200) {
        throw new Error('Failed to extract transcript from audio');
      }

      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate queries to refresh the folder contents and show the new transcript file
      queryClient.invalidateQueries({
        queryKey: ['folderContents', workspace.account.id, folderName],
      });
      
      console.log('Audio transcript extracted successfully:', data);
    },
  });

  const uploadFiles = async (files: File[]): Promise<{ file: StorageFile; assetId: string }[]> => {
    const FILE_SIZE_LIMIT = 10 * 1024 * 1024; // 50MB - Vercel's body size limit is around this
    
    const uploadPromises = files.map(async (file) => {
      const assetId = crypto.randomUUID();
      
      if (file.size > FILE_SIZE_LIMIT) {
        // Use blob upload for large files
        console.log(`Using blob upload for large file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
        
        const blob = await upload(file.name, file, {
          access: 'public',
          handleUploadUrl: '/api/assets/upload',
        });

        // Process the blob and store in Supabase
        const response = await fetch('/api/assets/process', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            blobUrl: blob.url,
            folderName,
            companyId: workspace.account.id,
            assetId,
            fileName: file.name,
            fileType: file.type,
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to process ${file.name}`);
        }

        const result = await response.json();
        return { file: result.file, assetId };
      } else {
        // Use direct server action for smaller files
        console.log(`Using direct upload for small file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
        
        const uploadedFiles = await uploadMultipleAssetFiles(folderName, [file], workspace.account.id);
        const uploadedFile = uploadedFiles[0];
        
        if (!uploadedFile) {
          throw new Error(`Failed to upload ${file.name}`);
        }
        
        return {
          file: {
            ...uploadedFile,
            type: file.type,
            name: uploadedFile.path.split('/').pop() || file.name,
          },
          assetId
        };
      }
    });

    return Promise.all(uploadPromises);
  };

  const uploadMutation = useMutation({
    mutationFn: (files: FileList | File[]) => uploadFiles(Array.from(files)),
    onSuccess: async (uploadResults: { file: StorageFile; assetId: string }[]) => {
      queryClient.invalidateQueries({
        queryKey: ['folderContents', workspace.account.id, folderName],
      });

      // Insert asset records immediately into the database using Zero
      const assetIdMap = new Map<string, string>(); // Map file path to asset ID
      for (const uploadResult of uploadResults) {
        try {
          const { file, assetId } = uploadResult;
          const assetRecord = createAssetRecord(file, assetId); // Pass the UUID
          await insertAsset(assetRecord);
          assetIdMap.set(file.path, assetId); // Store the same UUID for later use
          console.log('Asset record inserted:', assetRecord.file_name, 'with ID:', assetId);
        } catch (error) {
          console.error('Failed to insert asset record:', error);
          // Continue processing other files even if one fails
        }
      }

      // Extract just the files for processing
      const uploadedFiles = uploadResults.map(result => result.file);

      // Filter audio files by checking file extension
      const audioFiles = uploadedFiles.filter((file: StorageFile) => {
        const extension = file.path.split('.').pop()?.toLowerCase();
        return ['mp3', 'wav', 'ogg', 'm4a'].includes(extension || '');
      });

      // Filter video files by checking file extension
      const videoFiles = uploadedFiles.filter((file: StorageFile) => {
        const extension = file.path.split('.').pop()?.toLowerCase();
        return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].includes(extension || '');
      });

      console.log('Audio files:', audioFiles);
      console.log('Video files:', videoFiles);

      // Process audio files for transcript extraction
      for (const file of audioFiles) {
        try {
          const assetId = assetIdMap.get(file.path);
          console.log('Processing audio file:', file.path, 'with asset ID:', assetId);
          
          // Show toast notification for audio processing
          toast.promise(
            extractAudioTranscript.mutateAsync({
              url: file.url,
              name: file.path.split('/').pop() || '',
              assetId: assetId, // Pass the asset ID for precise database updates
            }),
            {
              loading: `Extracting transcript from ${file.path.split('/').pop()}...`,
              success: (data) => `Transcript extracted: ${data.transcriptFileName}`,
              error: 'Failed to extract transcript from audio',
            }
          );
        } catch (error) {
          console.error('Failed to extract transcript from audio:', error);
        }
      }

      // Process video files for transcript extraction
      for (const file of videoFiles) {
        try {
          const assetId = assetIdMap.get(file.path);
          console.log('Processing video file:', file.path, 'with asset ID:', assetId);
          
          // Show toast notification for video processing
          toast.promise(
            extractVideoTranscript.mutateAsync({
              url: file.url,
              name: file.path.split('/').pop() || '',
              assetId: assetId, // Pass the asset ID for precise database updates
            }),
            {
              loading: `Extracting transcript from ${file.path.split('/').pop()}...`,
              success: (data) => `Transcript extracted: ${data.audioFileName}`,
              error: 'Failed to extract transcript from video',
            }
          );
        } catch (error) {
          console.error('Failed to extract transcript from video:', error);
        }
      }
    },
  });

  const deleteFile = useMutation({
    mutationFn: async (file: StorageFile) => {
      const fileName = file.path.split('/').pop() || '';
      return deleteAssetFile(folderName, fileName, workspace.account.id);
    },
    onSuccess: (_, file) => {
      // Invalidate and refetch to ensure UI is updated
      queryClient.invalidateQueries({
        queryKey: ['folderContents', workspace.account.id, folderName],
      });

      // Explicit refetch to ensure UI updates immediately
      refetch();

      // If a file was selected and it was deleted, clear the selection
      if (selectedFile && selectedFile.path === file.path) {
        setSelectedFile(null);
      }
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Check if any file is larger than 16MB
    const MAX_FILE_SIZE = 500 * 1024 * 1024;
    const oversizedFiles = files.filter((file) => file.size > MAX_FILE_SIZE);

    if (oversizedFiles.length > 0) {
      alert('Some files are larger than 16MB and cannot be uploaded');
      return;
    }

    uploadMutation.mutate(files);
  };

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget;
    setImageAspect(
      img.naturalWidth > img.naturalHeight ? 'landscape' : 'portrait',
    );
  };

  const handleFileContentClick = (file: StorageFile) => {
    setSelectedFile(file);
  };

  const getFileName = (path: string): string => {
    return path.split('/').pop() || 'Unnamed file';
  };

  const hasAssociatedTranscript = (mediaFile: StorageFile): boolean => {
    // Find the corresponding asset record in the database
    const asset = assets.find(asset => asset.file_path === mediaFile.path);
    
    // If no asset record found, fallback to file-based check
    if (!asset) {
      const mediaFileName = getFileName(mediaFile.path);
      const mediaBaseName = mediaFileName.split('.')[0] || mediaFileName;
      
      return files.some((file) => {
        const fileName = getFileName(file.path);
        const isTranscriptFile = fileName.endsWith('.transcript.json');
        const isAudioMetadata = fileName.endsWith('.audio-metadata.json');
        
        // Check if transcript file name contains the media base name (accounting for UUID prefixes)
        return (isAudioMetadata || isTranscriptFile) && fileName.includes(mediaBaseName.replace(/^[a-f0-9-]+-/, ''));
      });
    }
    
    // Use database status for more accurate transcript state
    return asset.has_transcript === true && asset.processing_status === 'completed';
  };

  const isMediaProcessing = (mediaFile: StorageFile): boolean => {
    // Find the corresponding asset record in the database
    const asset = assets.find(asset => asset.file_path === mediaFile.path);
    
    // If no asset record found, fallback to file-based check
    if (!asset) {
      const mediaFileName = getFileName(mediaFile.path);
      const mediaBaseName = mediaFileName.split('.')[0] || mediaFileName;
      
      return files.some((file) => {
        const fileName = getFileName(file.path);
        const isProcessingFile = fileName.endsWith('.processing.json');
        
        return isProcessingFile && fileName.includes(mediaBaseName.replace(/^[a-f0-9-]+-/, ''));
      });
    }
    
    // Use database status for more accurate processing state
    return asset.processing_status === 'processing';
  };

  const getAudioMetadataFile = (videoFile: StorageFile): StorageFile | null => {
    const videoFileName = getFileName(videoFile.path);
    const videoBaseName = videoFileName.split('.')[0] || videoFileName;
    
    return files.find((file) => {
      const fileName = getFileName(file.path);
      const isAudioMetadata = fileName.endsWith('.audio-metadata.json');
      return isAudioMetadata && fileName.includes(videoBaseName.replace(/^[a-f0-9-]+-/, ''));
    }) || null;
  };

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="p-4">
      <div className="mb-6 flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <Button variant="ghost" onClick={onBack}>
            <ChevronLeft className="h-5 w-5" />
            Back
          </Button>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setIsMoveOpen(true)}
              disabled={selected.size === 0 || moveMutation.isPending}
            >
              {moveMutation.isPending ? 'Moving...' : 'Move'}
            </Button>
            <Button
              color="primary"
              className="inline-flex items-center"
              disabled={uploadMutation.isPending}
            >
              <label className="flex cursor-pointer items-center">
                {uploadMutation.isPending ? (
                  <>
                    <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-5 w-5" />
                    Upload Files
                  </>
                )}
                <input
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                  accept="image/*,video/*,audio/*"
                  multiple
                  disabled={uploadMutation.isPending}
                />
              </label>
            </Button>
          </div>
        </div>
        <div className="flex items-center px-2 gap-4">
          <h2 className="text-2xl font-bold">{folderName}</h2>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleSelectAll} disabled={(files || []).length === 0}>Select All</Button>
            <Button variant="outline" size="sm" onClick={handleDeselectAll} disabled={selected.size === 0}>Deselect All</Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
        {files.map((file: StorageFile) => (
          <div
            key={file.path}
            className={`group relative flex flex-col overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md ${selected.has(file.path) ? 'ring-2 ring-primary' : ''}`}
          >
            <div className="absolute left-2 top-2 z-10" onClick={(e) => e.stopPropagation()}>
              <input
                type="checkbox"
                className="h-4 w-4"
                checked={selected.has(file.path)}
                onChange={() => toggleSelect(file)}
              />
            </div>
            {/* File header with title and menu */}
            <div className="flex items-center justify-between border-b border-gray-100 bg-gray-50 px-3 py-2">
              <div className="flex items-center gap-1">
                {
                  // show icon based on file type
                  file?.type?.startsWith('image/') ? (
                    <ImageIcon className="h-6 w-6" />
                  ) : file?.type?.startsWith('video/') ? (
                    <VideoIcon className="h-6 w-6" />
                  ) : file?.type?.startsWith('audio/') ? (
                    <FileAudioIcon className="h-6 w-6" />
                  ) : (
                    <FileIcon className="h-6 w-6" />
                  )
                }
                {/* Show transcript indicators for audio/video files */}
                {(file?.type?.startsWith('video/') || file?.type?.startsWith('audio/')) && isMediaProcessing(file) && (
                  <div title="Transcript extraction in progress..." className="animate-spin">
                    <Loader className="h-3 w-3 text-blue-600" />
                  </div>
                )}
                {(file?.type?.startsWith('video/') || file?.type?.startsWith('audio/')) && !isMediaProcessing(file) && hasAssociatedTranscript(file) && (
                  <div title="Transcript extracted">
                    <FileAudioIcon className="h-3 w-3 text-green-600" />
                  </div>
                )}
              </div>
              <h3
                className="truncate text-sm font-medium text-gray-700"
                title={getFileName(file.path)}
              >
                {truncateFilename(getFileName(file.path), 20)}
              </h3>
              <div onClick={(e) => e.stopPropagation()}>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      className="text-red-600 focus:text-red-600"
                      onClick={() => deleteFile.mutate(file)}
                      disabled={deleteFile.isPending}
                    >
                      <Trash className="mr-2 h-4 w-4" />
                      {deleteFile.isPending ? 'Deleting...' : 'Delete'}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* File content */}
            <div
              className="flex aspect-square cursor-pointer items-center justify-center p-2"
              onClick={() => handleFileContentClick(file)}
            >
              {file.type?.startsWith('image/') ? (
                <Image
                  src={file.url}
                  alt={file.name || 'Asset image'}
                  width={200}
                  height={200}
                  className="h-full w-full rounded object-contain"
                  loading="lazy"
                  onLoad={handleImageLoad}
                />
              ) : file.type?.startsWith('video/') ? (
                <video
                  src={file.url}
                  className="h-full w-full rounded object-cover"
                  controls
                />
              ) : file.type?.startsWith('audio/') ? (
                <AudioPlayer url={file.url} />
              ) : (
                <div className="flex h-full w-full items-center justify-center rounded bg-gray-100 p-4">
                  <p className="text-sm text-gray-500">
                    File preview not available
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Image/Media Viewer Overlay */}
      {selectedFile && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 p-4"
          onClick={() => setSelectedFile(null)}
        >
          <button
            onClick={() => setSelectedFile(null)}
            className="absolute top-4 right-4 text-white hover:text-gray-300"
          >
            <X size={24} />
          </button>
          <div className="absolute top-4 left-4 text-white">
            <h2 className="text-lg font-medium">
              {getFileName(selectedFile.path)}
            </h2>
          </div>
          {selectedFile.type?.startsWith('image/') ? (
            <div className="relative flex h-full w-full items-center justify-center">
              <Image
                src={selectedFile.url}
                alt="Full size"
                width={1000}
                height={1000}
                className={`h-auto max-h-[90vh] w-auto max-w-[90vw] rounded-lg object-contain ${
                  imageAspect === 'landscape' ? 'w-full' : 'h-full'
                }`}
                onClick={(e) => e.stopPropagation()}
                onLoad={handleImageLoad}
              />
            </div>
          ) : selectedFile.type?.startsWith('video/') ? (
            <video
              src={selectedFile.url}
              className="max-h-[90vh] max-w-[90vw]"
              controls
              autoPlay
              onClick={(e) => e.stopPropagation()}
            />
          ) : selectedFile.type?.startsWith('audio/') ? (
            <div
              className="rounded-lg bg-gray-800 p-8"
              onClick={(e) => e.stopPropagation()}
            >
              <AudioPlayer url={selectedFile.url} />
            </div>
          ) : null}
        </div>
      )}

      <MoveAssetsDialog
        open={isMoveOpen}
        onOpenChange={setIsMoveOpen}
        folders={folders}
        currentFolder={
          folderName === 'logos' 
            ? 'brand/logos' 
            : folderName.startsWith('generated/') || folderName === 'generated'
              ? folderName
              : `assets/${folderName}`
        }
        onConfirm={(dest) => moveMutation.mutate(dest)}
        isPending={moveMutation.isPending}
      />

    </div>
  );
}
