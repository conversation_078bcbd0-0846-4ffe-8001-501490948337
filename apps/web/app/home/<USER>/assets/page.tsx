import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { createTeamAccountsApi } from '@kit/team-accounts/api';
import { PageBody } from '@kit/ui/page';
import { AppBreadcrumbs } from '@kit/ui/app-breadcrumbs';
import { requireUserInServerComponent } from '~/lib/server/require-user-in-server-component';
import { redirect } from 'next/navigation';

import { listAssetFolders } from '~/services/storage';
import { AssetsFolderClient } from './components/AssetsFolderClient';
import { TeamAccountLayoutPageHeader } from '../_components/layout';

interface BrandAssetsPageProps {
  params: Promise<{ account: string }>;
}

async function BrandAssetsPage(props: BrandAssetsPageProps) {
  const supabase = getSupabaseServerClient();
  const api = createTeamAccountsApi(supabase);
  const slug = (await props.params).account;
  const data = await api.getTeamAccount(slug);

  // ✅ Verify user has access to this team account
  const user = await requireUserInServerComponent();
  const { data: membership } = await supabase
    .from('accounts_memberships')
    .select('account_id')
    .eq('user_id', user.id)
    .eq('account_id', data.id)
    .single();
    
  if (!membership) {
    redirect('/unauthorized');
  }

  const account = {
    id: data.id,
    name: data.name,
    pictureUrl: data.picture_url,
    slug: data.slug as string,
    primaryOwnerUserId: data.primary_owner_user_id,
  };

  const initialFolders = await listAssetFolders(data.id);
  console.log("initialFolders", initialFolders);
  return (
    <>
      <TeamAccountLayoutPageHeader
        account={account.slug}
        title={'Brand Assets'}
        description={<AppBreadcrumbs />}
      />
      <PageBody>
        <AssetsFolderClient initialFolders={initialFolders} />
      </PageBody>
    </>
  );
}

export default BrandAssetsPage;
