'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@kit/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@kit/ui/dialog';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Separator } from '@kit/ui/separator';
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  Eye, 
  Bookmark, 
  ExternalLink,
  User,
  Clock,
  MapPin,
  Music,
  Hash,
  Users,
  Video,
  Calendar,
  CheckCircle
} from 'lucide-react';

interface TikTokCardProps {
  data: any;
}

export function TikTokCard({ data }: TikTokCardProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Format numbers to be more readable
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Format duration from seconds
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <>
      <Card className="w-full hover:shadow-md transition-shadow cursor-pointer" onClick={() => setIsDialogOpen(true)}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Avatar className="h-10 w-10">
                <AvatarImage src={data.authorMeta?.avatar} alt={data.authorMeta?.name} />
                <AvatarFallback>
                  {data.authorMeta?.name?.substring(0, 2).toUpperCase() || 'TT'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold text-sm truncate">
                    {data.authorMeta?.nickName || data.authorMeta?.name || 'Unknown'}
                  </h4>
                  {data.authorMeta?.verified && (
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                  )}
                </div>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {formatNumber(data.authorMeta?.fans || 0)} followers
                  </span>
                  <span className="flex items-center gap-1">
                    <Video className="h-3 w-3" />
                    {formatNumber(data.authorMeta?.video || 0)} videos
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                TIKTOK
              </Badge>
              {data.createTimeISO && (
                <span className="text-xs text-muted-foreground">
                  {formatDate(data.createTimeISO)}
                </span>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Video Content */}
          {data.text && (
            <p className="text-sm line-clamp-3">{data.text}</p>
          )}

          {/* Hashtags */}
          {data.hashtags && data.hashtags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {data.hashtags.slice(0, 5).map((hashtag: any, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  #{hashtag.name}
                </Badge>
              ))}
              {data.hashtags.length > 5 && (
                <Badge variant="outline" className="text-xs">
                  +{data.hashtags.length - 5} more
                </Badge>
              )}
            </div>
          )}

          {/* Engagement Stats */}
          <div className="grid grid-cols-5 gap-4 text-center">
            <div className="flex flex-col items-center">
              <div className="flex items-center gap-1 text-red-600">
                <Heart className="h-4 w-4" />
                <span className="text-sm font-medium">{formatNumber(data.diggCount || 0)}</span>
              </div>
              <span className="text-xs text-muted-foreground">Likes</span>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="flex items-center gap-1 text-blue-600">
                <MessageCircle className="h-4 w-4" />
                <span className="text-sm font-medium">{formatNumber(data.commentCount || 0)}</span>
              </div>
              <span className="text-xs text-muted-foreground">Comments</span>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="flex items-center gap-1 text-green-600">
                <Share2 className="h-4 w-4" />
                <span className="text-sm font-medium">{formatNumber(data.shareCount || 0)}</span>
              </div>
              <span className="text-xs text-muted-foreground">Shares</span>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="flex items-center gap-1 text-purple-600">
                <Eye className="h-4 w-4" />
                <span className="text-sm font-medium">{formatNumber(data.playCount || 0)}</span>
              </div>
              <span className="text-xs text-muted-foreground">Views</span>
            </div>
            
            <div className="flex flex-col items-center">
              <div className="flex items-center gap-1 text-orange-600">
                <Bookmark className="h-4 w-4" />
                <span className="text-sm font-medium">{formatNumber(data.collectCount || 0)}</span>
              </div>
              <span className="text-xs text-muted-foreground">Saves</span>
            </div>
          </div>

          {/* Video Info */}
          {data.videoMeta && (
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              {data.videoMeta.duration && (
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {formatDuration(data.videoMeta.duration)}
                </span>
              )}
              {data.videoMeta.definition && (
                <span>{data.videoMeta.definition}</span>
              )}
            </div>
          )}

          {/* Action Button */}
          <div className="flex items-center justify-between pt-2">
            <Button variant="outline" size="sm" asChild>
              <a href={data.webVideoUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-3 w-3 mr-1" />
                View on TikTok
              </a>
            </Button>
            <Button variant="ghost" size="sm">
              View Details →
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-h-[90vh] overflow-y-auto max-w-4xl w-[95vw] max-w-[95vw] sm:w-full sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={data.authorMeta?.avatar} alt={data.authorMeta?.name} />
                <AvatarFallback>
                  {data.authorMeta?.name?.substring(0, 2).toUpperCase() || 'TT'}
                </AvatarFallback>
              </Avatar>
              TikTok Video Details
            </DialogTitle>
          </DialogHeader>
          
          <ScrollArea className="max-h-[70vh]">
            <div className="space-y-6 p-1">
              {/* Creator Information */}
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Creator Information
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Username:</span> {data.authorMeta?.name || 'N/A'}
                  </div>
                  <div>
                    <span className="font-medium">Display Name:</span> {data.authorMeta?.nickName || 'N/A'}
                  </div>
                  <div>
                    <span className="font-medium">Followers:</span> {formatNumber(data.authorMeta?.fans || 0)}
                  </div>
                  <div>
                    <span className="font-medium">Following:</span> {formatNumber(data.authorMeta?.following || 0)}
                  </div>
                  <div>
                    <span className="font-medium">Total Videos:</span> {formatNumber(data.authorMeta?.video || 0)}
                  </div>
                  <div>
                    <span className="font-medium">Total Likes:</span> {formatNumber(data.authorMeta?.heart || 0)}
                  </div>
                  <div className="col-span-2">
                    <span className="font-medium">Bio:</span> {data.authorMeta?.signature || 'No bio available'}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Video Content */}
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  Video Content
                </h3>
                {data.text && (
                  <p className="text-sm bg-muted p-3 rounded-md">{data.text}</p>
                )}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Video ID:</span> {data.id}
                  </div>
                  <div>
                    <span className="font-medium">Created:</span> {data.createTimeISO ? formatDate(data.createTimeISO) : 'N/A'}
                  </div>
                  <div>
                    <span className="font-medium">Language:</span> {data.textLanguage || 'N/A'}
                  </div>
                  <div>
                    <span className="font-medium">Sponsored:</span> {data.isSponsored ? 'Yes' : 'No'}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Engagement Metrics */}
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Engagement Metrics
                </h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-red-600">{formatNumber(data.diggCount || 0)}</div>
                    <div className="text-sm text-muted-foreground">Likes</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-blue-600">{formatNumber(data.commentCount || 0)}</div>
                    <div className="text-sm text-muted-foreground">Comments</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-green-600">{formatNumber(data.shareCount || 0)}</div>
                    <div className="text-sm text-muted-foreground">Shares</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-purple-600">{formatNumber(data.playCount || 0)}</div>
                    <div className="text-sm text-muted-foreground">Views</div>
                  </div>
                  <div className="text-center p-3 bg-muted rounded-md">
                    <div className="text-2xl font-bold text-orange-600">{formatNumber(data.collectCount || 0)}</div>
                    <div className="text-sm text-muted-foreground">Saves</div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Video Technical Details */}
              {data.videoMeta && (
                <>
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Video className="h-4 w-4" />
                      Technical Details
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Duration:</span> {data.videoMeta.duration ? formatDuration(data.videoMeta.duration) : 'N/A'}
                      </div>
                      <div>
                        <span className="font-medium">Resolution:</span> {data.videoMeta.width}x{data.videoMeta.height}
                      </div>
                      <div>
                        <span className="font-medium">Quality:</span> {data.videoMeta.definition || 'N/A'}
                      </div>
                      <div>
                        <span className="font-medium">Format:</span> {data.videoMeta.format || 'N/A'}
                      </div>
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* Location */}
              {data.locationMeta && (
                <>
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Location
                    </h3>
                    <div className="text-sm">
                      {data.locationMeta.locationName && (
                        <p><span className="font-medium">Location:</span> {data.locationMeta.locationName}</p>
                      )}
                      {data.locationMeta.address && (
                        <p><span className="font-medium">Address:</span> {data.locationMeta.address}</p>
                      )}
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* Music Info */}
              {data.musicMeta && (
                <>
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Music className="h-4 w-4" />
                      Music
                    </h3>
                    <div className="text-sm">
                      <p><span className="font-medium">Track:</span> {data.musicMeta.musicName || 'N/A'}</p>
                      <p><span className="font-medium">Artist:</span> {data.musicMeta.musicAuthor || 'N/A'}</p>
                      <p><span className="font-medium">Original:</span> {data.musicMeta.musicOriginal ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* Hashtags */}
              {data.hashtags && data.hashtags.length > 0 && (
                <>
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Hash className="h-4 w-4" />
                      Hashtags ({data.hashtags.length})
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {data.hashtags.map((hashtag: any, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          #{hashtag.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* Raw Data */}
              <div className="space-y-3">
                <h3 className="font-semibold">Raw Data (Debug)</h3>
                <pre className="text-xs bg-muted p-3 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(data, null, 2)}
                </pre>
              </div>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
} 