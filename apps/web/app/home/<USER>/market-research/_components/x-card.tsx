'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Button } from '@kit/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@kit/ui/avatar';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@kit/ui/dialog';
import { ScrollArea } from '@kit/ui/scroll-area';
import { Separator } from '@kit/ui/separator';
import { 
  Heart, 
  MessageCircle, 
  Repeat2, 
  Eye, 
  ExternalLink,
  User,
  Clock,
  MapPin,
  Hash,
  Users,
  Calendar,
  CheckCircle,
  Link2,
  AtSign
} from 'lucide-react';

interface XCardProps {
  data: any;
}

export function XCard({ data }: XCardProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Format numbers to be more readable
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Check if this is a user profile or a tweet
  const isUserProfile = data.content_type === 'user_profile' || 
                       (!data.text && !data.fullText && data.username);

  return (
    <>
      <Card className="w-full hover:shadow-md transition-shadow cursor-pointer" onClick={() => setIsDialogOpen(true)}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <Avatar className="h-10 w-10">
                <AvatarImage src={data.profilePicture || data.avatar} alt={data.userName || data.username} />
                <AvatarFallback>
                  {(data.userName || data.username || data.name || 'X')?.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h4 className="font-semibold text-sm truncate">
                    {data.name || data.displayName || data.userName || data.username || 'Unknown'}
                  </h4>
                  {data.verified && (
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                  )}
                </div>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <AtSign className="h-3 w-3" />
                    {data.userName || data.username || 'unknown'}
                  </span>
                  {(data.followersCount || data.followers) && (
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {formatNumber(data.followersCount || data.followers)} followers
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {isUserProfile ? 'X PROFILE' : 'X POST'}
              </Badge>
              {data.createdAt && (
                <span className="text-xs text-muted-foreground">
                  {formatDate(data.createdAt)}
                </span>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Tweet Content or User Bio */}
          {(data.text || data.fullText) && (
            <p className="text-sm line-clamp-3">{data.text || data.fullText}</p>
          )}
          
          {/* User Bio (for profile cards) */}
          {isUserProfile && data.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">{data.description}</p>
          )}

          {/* Hashtags */}
          {data.hashtags && data.hashtags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {data.hashtags.slice(0, 5).map((hashtag: string, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  #{hashtag}
                </Badge>
              ))}
              {data.hashtags.length > 5 && (
                <Badge variant="outline" className="text-xs">
                  +{data.hashtags.length - 5} more
                </Badge>
              )}
            </div>
          )}

          {/* Engagement Stats for Tweets */}
          {!isUserProfile && (
            <div className="grid grid-cols-4 gap-4 text-center">
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-red-600">
                  <Heart className="h-4 w-4" />
                  <span className="text-sm font-medium">{formatNumber(data.likes || data.likeCount || 0)}</span>
                </div>
                <span className="text-xs text-muted-foreground">Likes</span>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-blue-600">
                  <MessageCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">{formatNumber(data.replies || data.replyCount || 0)}</span>
                </div>
                <span className="text-xs text-muted-foreground">Replies</span>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-green-600">
                  <Repeat2 className="h-4 w-4" />
                  <span className="text-sm font-medium">{formatNumber(data.retweets || data.retweetCount || 0)}</span>
                </div>
                <span className="text-xs text-muted-foreground">Retweets</span>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-purple-600">
                  <Eye className="h-4 w-4" />
                  <span className="text-sm font-medium">{formatNumber(data.views || data.viewCount || 0)}</span>
                </div>
                <span className="text-xs text-muted-foreground">Views</span>
              </div>
            </div>
          )}

          {/* Profile Stats for Users */}
          {isUserProfile && (
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-blue-600">
                  <Users className="h-4 w-4" />
                  <span className="text-sm font-medium">{formatNumber(data.followersCount || data.followers || 0)}</span>
                </div>
                <span className="text-xs text-muted-foreground">Followers</span>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-green-600">
                  <Users className="h-4 w-4" />
                  <span className="text-sm font-medium">{formatNumber(data.followingCount || data.following || 0)}</span>
                </div>
                <span className="text-xs text-muted-foreground">Following</span>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="flex items-center gap-1 text-purple-600">
                  <MessageCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">{formatNumber(data.statusesCount || data.tweetsCount || 0)}</span>
                </div>
                <span className="text-xs text-muted-foreground">Posts</span>
              </div>
            </div>
          )}

          {/* Location */}
          {data.location && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <MapPin className="h-3 w-3" />
              <span>{data.location}</span>
            </div>
          )}

          {/* Action Button */}
          <div className="flex items-center justify-between pt-2">
            <Button variant="outline" size="sm" asChild>
              <a href={data.url || data.tweetUrl || `https://x.com/${data.userName || data.username}`} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-3 w-3 mr-1" />
                View on X
              </a>
            </Button>
            <Button variant="ghost" size="sm">
              View Details →
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-h-[90vh] overflow-y-auto max-w-4xl w-[95vw] max-w-[95vw] sm:w-full sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={data.profilePicture || data.avatar} alt={data.userName || data.username} />
                <AvatarFallback>
                  {(data.userName || data.username || 'X')?.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              X {isUserProfile ? 'Profile' : 'Post'} Details
            </DialogTitle>
          </DialogHeader>
          
          <ScrollArea className="max-h-[70vh]">
            <div className="space-y-6 p-1">
              {/* User Information */}
              <div className="space-y-3">
                <h3 className="font-semibold flex items-center gap-2">
                  <User className="h-4 w-4" />
                  User Information
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Username:</span> @{data.userName || data.username || 'N/A'}
                  </div>
                  <div>
                    <span className="font-medium">Display Name:</span> {data.name || data.displayName || 'N/A'}
                  </div>
                  <div>
                    <span className="font-medium">Followers:</span> {formatNumber(data.followersCount || data.followers || 0)}
                  </div>
                  <div>
                    <span className="font-medium">Following:</span> {formatNumber(data.followingCount || data.following || 0)}
                  </div>
                  <div>
                    <span className="font-medium">Posts:</span> {formatNumber(data.statusesCount || data.tweetsCount || 0)}
                  </div>
                  <div>
                    <span className="font-medium">Verified:</span> {data.verified ? 'Yes' : 'No'}
                  </div>
                  {data.location && (
                    <div className="col-span-2">
                      <span className="font-medium">Location:</span> {data.location}
                    </div>
                  )}
                  {data.description && (
                    <div className="col-span-2">
                      <span className="font-medium">Bio:</span> {data.description}
                    </div>
                  )}
                </div>
              </div>

              <Separator />

              {/* Content Details */}
              {!isUserProfile && (
                <>
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <MessageCircle className="h-4 w-4" />
                      Post Content
                    </h3>
                    {(data.text || data.fullText) && (
                      <p className="text-sm bg-muted p-3 rounded-md">{data.text || data.fullText}</p>
                    )}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Post ID:</span> {data.id || 'N/A'}
                      </div>
                      <div>
                        <span className="font-medium">Created:</span> {data.createdAt ? formatDate(data.createdAt) : 'N/A'}
                      </div>
                      <div>
                        <span className="font-medium">Language:</span> {data.lang || data.language || 'N/A'}
                      </div>
                      <div>
                        <span className="font-medium">Type:</span> {data.isRetweet ? 'Retweet' : 'Original'}
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Engagement Metrics */}
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Heart className="h-4 w-4" />
                      Engagement Metrics
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-muted rounded-md">
                        <div className="text-2xl font-bold text-red-600">{formatNumber(data.likes || data.likeCount || 0)}</div>
                        <div className="text-sm text-muted-foreground">Likes</div>
                      </div>
                      <div className="text-center p-3 bg-muted rounded-md">
                        <div className="text-2xl font-bold text-blue-600">{formatNumber(data.replies || data.replyCount || 0)}</div>
                        <div className="text-sm text-muted-foreground">Replies</div>
                      </div>
                      <div className="text-center p-3 bg-muted rounded-md">
                        <div className="text-2xl font-bold text-green-600">{formatNumber(data.retweets || data.retweetCount || 0)}</div>
                        <div className="text-sm text-muted-foreground">Retweets</div>
                      </div>
                      <div className="text-center p-3 bg-muted rounded-md">
                        <div className="text-2xl font-bold text-purple-600">{formatNumber(data.views || data.viewCount || 0)}</div>
                        <div className="text-sm text-muted-foreground">Views</div>
                      </div>
                    </div>
                  </div>

                  <Separator />
                </>
              )}

              {/* Hashtags */}
              {data.hashtags && data.hashtags.length > 0 && (
                <>
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Hash className="h-4 w-4" />
                      Hashtags ({data.hashtags.length})
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {data.hashtags.map((hashtag: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          #{hashtag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* URLs/Links */}
              {data.urls && data.urls.length > 0 && (
                <>
                  <div className="space-y-3">
                    <h3 className="font-semibold flex items-center gap-2">
                      <Link2 className="h-4 w-4" />
                      Links ({data.urls.length})
                    </h3>
                    <div className="space-y-2">
                      {data.urls.map((url: string, index: number) => (
                        <a 
                          key={index}
                          href={url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-xs text-blue-600 hover:underline break-all block"
                        >
                          {url}
                        </a>
                      ))}
                    </div>
                  </div>
                  <Separator />
                </>
              )}

              {/* Raw Data */}
              <div className="space-y-3">
                <h3 className="font-semibold">Raw Data (Debug)</h3>
                <pre className="text-xs bg-muted p-3 rounded-md overflow-auto max-h-96">
                  {JSON.stringify(data, null, 2)}
                </pre>
              </div>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </>
  );
} 