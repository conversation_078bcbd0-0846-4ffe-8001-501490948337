'use client';

import React, { useState } from 'react';
import { useQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { If } from "@kit/ui/if";
import { Trans } from "@kit/ui/trans";
import { But<PERSON> } from '@kit/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@kit/ui/dropdown-menu';
import { Plus, ChevronDown, Sparkles, Edit, Linkedin, UserRound } from 'lucide-react';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { HorizontalICPsList } from './HorizontalICPsList';
import CreateICPDialog from '../../../personas/_components/create-icp-dialog';
import EmptyICPs from '../../../personas/_components/empty-icps';
import { GenerateICPDialog } from '../../../personas/_components/generate-icp-dialog';
import { GenerateICPWithSocialProfileDialog } from '../../../personas/_components/generate-icp-with-social-profile';

interface CustomerProfilesSectionProps {
  brandId: string;
}

export function CustomerProfilesSection({ brandId }: CustomerProfilesSectionProps) {
  const { account } = useTeamAccountWorkspace();
  const [isNew, setIsNew] = useState(false);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [isLinkedInDialogOpen, setIsLinkedInDialogOpen] = useState(false);
  const accountSlug = account?.slug;
  const zero = useZero();
  
  const [icps] = useQuery(zero.query.icps.where("company_id", "=", account.id).orderBy("created_at", "desc"), {
    ttl: '1d'
  });
  
  const [products] = useQuery(zero.query.products.where("company_id", "=", account.id), {
    ttl: '1d'
  });

  const hasICPs = icps.length > 0;

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Customer Profiles</h1>
        <p className="text-gray-600">Define your ideal customer profiles and organize your personas by customer segments</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold">
            <Trans i18nKey="personas:yourICPs" defaults="Your Ideal Customer Profiles" />
          </h2>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                <Trans i18nKey="personas:createICP" defaults="Create ICP" />
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem className='cursor-pointer' onClick={() => setIsGenerateDialogOpen(true)}>
                <Sparkles className="h-4 w-4 mr-2" />
                <Trans i18nKey="personas:generateWithAI" defaults="Generate With AI" />
              </DropdownMenuItem>
              <DropdownMenuItem className='cursor-pointer' onClick={() => setIsLinkedInDialogOpen(true)}>
                <Linkedin className="h-4 w-4 mr-2" />
                <Trans i18nKey="personas:useLinkedInProfiles" defaults="Use LinkedIn Profiles" />
              </DropdownMenuItem>
              <DropdownMenuItem className='cursor-pointer' onClick={() => setIsNew(true)}>
                <Edit className="h-4 w-4 mr-2" />
                <Trans i18nKey="personas:createManually" defaults="Create Manually" />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <If condition={hasICPs}>
          {/* @ts-expect-error readonly */}
          <HorizontalICPsList icps={icps} accountSlug={accountSlug} />
        </If>
        
        <If condition={!hasICPs}>
          <div className="p-8 bg-gray-50 rounded-lg text-center border-2 border-dashed border-gray-300">
            <UserRound className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <h4 className="text-lg font-medium text-gray-600 mb-2">No Customer Profiles Defined</h4>
            <p className="text-sm text-gray-500 mb-3">
              Create your ideal customer profiles to better understand your target audience.
            </p>
            <div className="text-xs text-gray-400">
              💡 Tip: Start with your most important customer segment
            </div>
          </div>
        </If>
        
        <CreateICPDialog isNew={isNew} setIsNew={setIsNew} companyId={account.id} />
        
        <GenerateICPDialog
          isOpen={isGenerateDialogOpen}
          onClose={() => setIsGenerateDialogOpen(false)}
          onGenerate={({ description, selectedProducts }) => {
            zero.mutate.icps.insert({
              id: crypto.randomUUID(),
              values: {
                company_id: account.id,
                withAi: true,
                withLinkedIn: false,
                name: null,
                data: {},
                is_generating: true,
                error_generating: false,
                reference_products: selectedProducts,
                reference_description: description
              }
            });
          }}
          products={products.map(product => ({
            id: product.id,
            name: product.name,
            description: product.description ?? undefined,
            key_features: product.key_features as Array<{name: string; value_prop: string; differentiation: string}> | undefined
          }))}
        />
        
        <GenerateICPWithSocialProfileDialog
          isOpen={isLinkedInDialogOpen}
          onClose={() => setIsLinkedInDialogOpen(false)}
          products={products.map(product => ({
            id: product.id,
            name: product.name,
            description: product.description ?? undefined,
            key_features: product.key_features as Array<{name: string; value_prop: string; differentiation: string}> | undefined
          }))}
        />
      </div>
    </div>
  );
}
