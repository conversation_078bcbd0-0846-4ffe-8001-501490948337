'use client';

import { ICP } from "~/types/icp";
import { HorizontalICPCard } from "./HorizontalICPCard";
import { ICPDetailModal } from "./ICPDetailModal";
import { useState } from "react";

interface HorizontalICPsListProps {
  icps: ICP[];
  accountSlug: string;
}

export function HorizontalICPsList({ icps, accountSlug }: HorizontalICPsListProps) {
  const [selectedICP, setSelectedICP] = useState<ICP | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewDetails = (icp: ICP) => {
    setSelectedICP(icp);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedICP(null);
  };

  return (
    <>
      <div className="space-y-3">
        {icps.map((icp) => (
          <HorizontalICPCard
            key={icp.id}
            icp={icp}
            accountSlug={accountSlug}
            onViewDetails={handleViewDetails}
          />
        ))}
      </div>

      <ICPDetailModal
        icp={selectedICP}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        accountSlug={accountSlug}
      />
    </>
  );
}
