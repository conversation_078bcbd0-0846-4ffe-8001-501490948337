'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ead<PERSON>, DialogTitle } from "@kit/ui/dialog";
import { <PERSON><PERSON> } from "@kit/ui/button";
import { Badge } from "@kit/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@kit/ui/card";
import { Separator } from "@kit/ui/separator";
import { X, Users, Building, MapPin, DollarSign, Target, Zap, Lightbulb, UserCheck } from "lucide-react";
import { Trans } from "@kit/ui/trans";
import { ICP } from "~/types/icp";

interface ICPDetailModalProps {
  icp: ICP | null;
  isOpen: boolean;
  onClose: () => void;
  accountSlug: string;
}

export function ICPDetailModal({ icp, isOpen, onClose, accountSlug }: ICPDetailModalProps) {
  if (!icp) return null;

  const renderSection = (title: string, icon: React.ReactNode, children: React.ReactNode) => {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            {icon}
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {children}
        </CardContent>
      </Card>
    );
  };

  const renderArrayField = (data: string[] | undefined, emptyMessage: string) => {
    if (!data || data.length === 0) {
      return <p className="text-muted-foreground italic">{emptyMessage}</p>;
    }
    
    return (
      <div className="flex flex-wrap gap-2">
        {data.map((item, index) => (
          <Badge key={index} variant="secondary">
            {item}
          </Badge>
        ))}
      </div>
    );
  };

  const renderStringField = (data: string | undefined, emptyMessage: string) => {
    if (!data) {
      return <p className="text-muted-foreground italic">{emptyMessage}</p>;
    }
    
    return <p className="text-sm">{data}</p>;
  };

  const renderRevenueRange = (revenue: any) => {
    if (!revenue?.min || !revenue?.max) {
      return <p className="text-muted-foreground italic">No revenue range specified</p>;
    }
    
    return (
      <Badge variant="secondary" className="text-sm px-3 py-1">
        ${revenue.min.toLocaleString()} - ${revenue.max.toLocaleString()}
      </Badge>
    );
  };

  const renderEmployeeRange = (employees: any) => {
    if (!employees?.min || !employees?.max) {
      return <p className="text-muted-foreground italic">No employee range specified</p>;
    }
    
    return (
      <Badge variant="secondary" className="text-sm px-3 py-1">
        {employees.min.toLocaleString()} - {employees.max.toLocaleString()} employees
      </Badge>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-2xl font-bold">{icp.name}</DialogTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="space-y-6">
          {/* Company Profile */}
          {renderSection(
            "Company Profile",
            <Building className="h-5 w-5" />,
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Industries</h4>
                {renderArrayField(icp.data?.target_industries as string[], "No industries specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Company Size (Employees)</h4>
                {renderEmployeeRange(icp.data?.company_employee_range)}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Revenue Range</h4>
                {renderRevenueRange(icp.data?.company_revenue_range_usd)}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Company Stage</h4>
                {renderArrayField(icp.data?.company_stage as string[], "No company stage specified")}
              </div>
            </div>
          )}

          {/* Geographic Markets */}
          {renderSection(
            "Geographic Markets",
            <MapPin className="h-5 w-5" />,
            <div>
              {renderArrayField(icp.data?.geography_markets as string[], "No geographic markets specified")}
            </div>
          )}

          {/* Decision Making */}
          {renderSection(
            "Decision Making",
            <UserCheck className="h-5 w-5" />,
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Decision Making Departments</h4>
                {renderArrayField(icp.data?.decision_making_departments as string[], "No decision making departments specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Decision Making Process</h4>
                {renderStringField(icp.data?.decision_making_process as string, "No decision making process specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Budget Authority</h4>
                {renderStringField(icp.data?.budget_authority as string, "No budget authority specified")}
              </div>
            </div>
          )}

          {/* Use Cases & Problems */}
          {renderSection(
            "Use Cases & Problems",
            <Target className="h-5 w-5" />,
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Primary Use Cases</h4>
                {renderArrayField(icp.data?.use_cases_problems as string[], "No use cases specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Pain Points</h4>
                {renderArrayField(icp.data?.pain_points as string[], "No pain points specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Goals & Objectives</h4>
                {renderArrayField(icp.data?.goals_objectives as string[], "No goals specified")}
              </div>
            </div>
          )}

          {/* Buying Behavior */}
          {renderSection(
            "Buying Behavior",
            <Zap className="h-5 w-5" />,
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Buying Triggers</h4>
                {renderArrayField(icp.data?.buying_triggers as string[], "No buying triggers specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Buying Timeline</h4>
                {renderStringField(icp.data?.buying_timeline as string, "No buying timeline specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Budget Range</h4>
                {renderStringField(icp.data?.budget_range as string, "No budget range specified")}
              </div>
            </div>
          )}

          {/* Technology & Tools */}
          {renderSection(
            "Technology & Tools",
            <Lightbulb className="h-5 w-5" />,
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Current Technology Stack</h4>
                {renderArrayField(icp.data?.technology_stack as string[], "No technology stack specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Integration Requirements</h4>
                {renderArrayField(icp.data?.integration_requirements as string[], "No integration requirements specified")}
              </div>
            </div>
          )}

          {/* Communication Preferences */}
          {renderSection(
            "Communication & Engagement",
            <Users className="h-5 w-5" />,
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Preferred Communication Channels</h4>
                {renderArrayField(icp.data?.communication_channels as string[], "No communication channels specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Content Preferences</h4>
                {renderArrayField(icp.data?.content_preferences as string[], "No content preferences specified")}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Objections & Concerns</h4>
                {renderArrayField(icp.data?.objections_concerns as string[], "No objections specified")}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              variant="default"
              className="flex-1"
              asChild
            >
              <a href={`/home/<USER>/personas/icp/${icp.id}`}>
                <Users className="h-4 w-4 mr-2" />
                <Trans i18nKey="personas:viewPersonas" defaults="View Personas" />
              </a>
            </Button>
            <Button variant="outline" onClick={onClose}>
              <Trans i18nKey="common:close" defaults="Close" />
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
