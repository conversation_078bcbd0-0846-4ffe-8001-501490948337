'use client';

import React, { useState } from 'react';
import { Save, X, Plus, Camera } from 'lucide-react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@kit/ui/dialog';
import { PhotographyGuidelines } from '../../types/brand';

interface PhotographyDialogProps {
  isOpen: boolean;
  photography: PhotographyGuidelines | null;
  onClose: () => void;
  onSave: (photography: PhotographyGuidelines) => void;
}

export function PhotographyDialog({ isOpen, photography, onClose, onSave }: PhotographyDialogProps) {
  const [editingPhotographyData, setEditingPhotographyData] = useState<PhotographyGuidelines | null>(photography);
  const [newArtDirection, setNewArtDirection] = useState('');
  const [newDo, setNewDo] = useState('');
  const [newDont, setNewDont] = useState('');

  // Initialize form data when photography changes
  React.useEffect(() => {
    if (photography) {
      setEditingPhotographyData({ ...photography });
    }
  }, [photography]);

  const handleSave = () => {
    if (!editingPhotographyData) return;
    onSave(editingPhotographyData);
  };

  const handleAddArtDirection = () => {
    if (!newArtDirection.trim() || !editingPhotographyData) return;
    
    const updatedArtDirection = [...editingPhotographyData.artDirection, newArtDirection.trim()];
    setEditingPhotographyData({
      ...editingPhotographyData,
      artDirection: updatedArtDirection
    });
    setNewArtDirection('');
  };

  const handleRemoveArtDirection = (index: number) => {
    if (!editingPhotographyData) return;
    
    const updatedArtDirection = editingPhotographyData.artDirection.filter((_, i) => i !== index);
    setEditingPhotographyData({
      ...editingPhotographyData,
      artDirection: updatedArtDirection
    });
  };

  const handleAddDo = () => {
    if (!newDo.trim() || !editingPhotographyData) return;
    
    const updatedDos = [...editingPhotographyData.rules.do, newDo.trim()];
    setEditingPhotographyData({
      ...editingPhotographyData,
      rules: {
        ...editingPhotographyData.rules,
        do: updatedDos
      }
    });
    setNewDo('');
  };

  const handleRemoveDo = (index: number) => {
    if (!editingPhotographyData) return;
    
    const updatedDos = editingPhotographyData.rules.do.filter((_, i) => i !== index);
    setEditingPhotographyData({
      ...editingPhotographyData,
      rules: {
        ...editingPhotographyData.rules,
        do: updatedDos
      }
    });
  };

  const handleAddDont = () => {
    if (!newDont.trim() || !editingPhotographyData) return;
    
    const updatedDonts = [...editingPhotographyData.rules.dont, newDont.trim()];
    setEditingPhotographyData({
      ...editingPhotographyData,
      rules: {
        ...editingPhotographyData.rules,
        dont: updatedDonts
      }
    });
    setNewDont('');
  };

  const handleRemoveDont = (index: number) => {
    if (!editingPhotographyData) return;
    
    const updatedDonts = editingPhotographyData.rules.dont.filter((_, i) => i !== index);
    setEditingPhotographyData({
      ...editingPhotographyData,
      rules: {
        ...editingPhotographyData.rules,
        dont: updatedDonts
      }
    });
  };

  if (!editingPhotographyData) return null;

  const commonStyles = [
    'Human-led, founder-focused',
    'Lifestyle and authentic',
    'Clean and minimalist',
    'Bright and colorful',
    'Professional and corporate',
    'Candid and natural',
    'Dramatic and moody'
  ];

  const commonArtDirections = [
    'Natural lighting preferred',
    'Authentic moments over posed shots',
    'Focus on human connections',
    'Brand colors in backgrounds',
    'Professional yet approachable',
    'High-quality, sharp images',
    'Diverse representation'
  ];

  const commonDos = [
    'Use natural lighting when possible',
    'Capture authentic emotions',
    'Maintain consistent color grading',
    'Show diversity and inclusion',
    'Focus on brand story',
    'Use high-resolution images',
    'Follow brand color palette'
  ];

  const commonDonts = [
    'Use overly posed or staged photos',
    'Include competitor branding',
    'Use poor quality or blurry images',
    'Over-edit or heavily filter',
    'Use stock photos exclusively',
    'Ignore brand guidelines',
    'Use inconsistent styling'
  ];

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-h-[90vh] overflow-y-auto w-[95vw] max-w-[95vw] sm:w-full sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>Photography Guidelines</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Photography Style */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Photography Style</label>
            <textarea
              value={editingPhotographyData.style}
              onChange={(e) => setEditingPhotographyData({
                ...editingPhotographyData,
                style: e.target.value
              })}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe your overall photography style..."
              rows={3}
            />
            
            {/* Quick style suggestions */}
            <div className="mt-2">
              <p className="text-xs text-gray-500 mb-1">Quick select style:</p>
              <div className="flex flex-wrap gap-1">
                {commonStyles
                  .filter(style => !editingPhotographyData.style.includes(style))
                  .slice(0, 4)
                  .map((style) => (
                    <button
                      key={style}
                      onClick={() => setEditingPhotographyData({
                        ...editingPhotographyData,
                        style: style
                      })}
                      className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                    >
                      {style}
                    </button>
                  ))}
              </div>
            </div>
          </div>

          {/* Art Direction */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Art Direction</label>
            
            {/* Add Art Direction Input */}
            <div className="flex gap-2 mb-2">
              <input
                type="text"
                value={newArtDirection}
                onChange={(e) => setNewArtDirection(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddArtDirection();
                  }
                }}
                className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Add art direction guidance"
              />
              <button
                onClick={handleAddArtDirection}
                disabled={!newArtDirection.trim()}
                className="px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus size={16} />
              </button>
            </div>

            {/* Common Art Direction Quick Add */}
            <div className="mb-3">
              <p className="text-xs text-gray-500 mb-1">Quick add:</p>
              <div className="flex flex-wrap gap-1">
                {commonArtDirections
                  .filter(direction => !editingPhotographyData.artDirection.includes(direction))
                  .slice(0, 3)
                  .map((direction) => (
                    <button
                      key={direction}
                      onClick={() => {
                        setEditingPhotographyData({
                          ...editingPhotographyData,
                          artDirection: [...editingPhotographyData.artDirection, direction]
                        });
                      }}
                      className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"
                    >
                      + {direction}
                    </button>
                  ))}
              </div>
            </div>

            {/* Current Art Directions */}
            {editingPhotographyData.artDirection.length > 0 && (
              <div className="space-y-1">
                {editingPhotographyData.artDirection.map((direction, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                    <span className="text-sm text-blue-800 flex items-start">
                      <span className="mr-2">•</span>
                      <span>{direction}</span>
                    </span>
                    <button
                      onClick={() => handleRemoveArtDirection(index)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <X size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Rules Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Do's */}
            <div>
              <label className="block text-sm font-medium text-green-700 mb-1">Do&quot;s</label>
              
              {/* Add Do Input */}
              <div className="flex gap-2 mb-2">
                <input
                  type="text"
                  value={newDo}
                  onChange={(e) => setNewDo(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddDo();
                    }
                  }}
                  className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Add a do"
                />
                <button
                  onClick={handleAddDo}
                  disabled={!newDo.trim()}
                  className="px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus size={16} />
                </button>
              </div>

              {/* Common Do's Quick Add */}
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">Quick add:</p>
                <div className="flex flex-wrap gap-1">
                  {commonDos
                    .filter(doItem => !editingPhotographyData.rules.do.includes(doItem))
                    .slice(0, 2)
                    .map((doItem) => (
                      <button
                        key={doItem}
                        onClick={() => {
                          setEditingPhotographyData({
                            ...editingPhotographyData,
                            rules: {
                              ...editingPhotographyData.rules,
                              do: [...editingPhotographyData.rules.do, doItem]
                            }
                          });
                        }}
                        className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200"
                      >
                        + {doItem.length > 20 ? doItem.substring(0, 20) + '...' : doItem}
                      </button>
                    ))}
                </div>
              </div>

              {/* Current Do's */}
              {editingPhotographyData.rules.do.length > 0 && (
                <div className="space-y-1">
                  {editingPhotographyData.rules.do.map((doItem, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded">
                      <span className="text-sm text-green-800 flex items-start">
                        <span className="mr-2">✓</span>
                        <span>{doItem}</span>
                      </span>
                      <button
                        onClick={() => handleRemoveDo(index)}
                        className="text-green-600 hover:text-green-800"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Don'ts */}
            <div>
              <label className="block text-sm font-medium text-red-700 mb-1">Don&quot;ts</label>
              
              {/* Add Don't Input */}
              <div className="flex gap-2 mb-2">
                <input
                  type="text"
                  value={newDont}
                  onChange={(e) => setNewDont(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      handleAddDont();
                    }
                  }}
                  className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="Add a don't"
                />
                <button
                  onClick={handleAddDont}
                  disabled={!newDont.trim()}
                  className="px-3 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Plus size={16} />
                </button>
              </div>

              {/* Common Don'ts Quick Add */}
              <div className="mb-3">
                <p className="text-xs text-gray-500 mb-1">Quick add:</p>
                <div className="flex flex-wrap gap-1">
                  {commonDonts
                    .filter(dontItem => !editingPhotographyData.rules.dont.includes(dontItem))
                    .slice(0, 2)
                    .map((dontItem) => (
                      <button
                        key={dontItem}
                        onClick={() => {
                          setEditingPhotographyData({
                            ...editingPhotographyData,
                            rules: {
                              ...editingPhotographyData.rules,
                              dont: [...editingPhotographyData.rules.dont, dontItem]
                            }
                          });
                        }}
                        className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200"
                      >
                        + {dontItem.length > 20 ? dontItem.substring(0, 20) + '...' : dontItem}
                      </button>
                    ))}
                </div>
              </div>

              {/* Current Don'ts */}
              {editingPhotographyData.rules.dont.length > 0 && (
                <div className="space-y-1">
                  {editingPhotographyData.rules.dont.map((dontItem, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded">
                      <span className="text-sm text-red-800 flex items-start">
                        <span className="mr-2">✗</span>
                        <span>{dontItem}</span>
                      </span>
                      <button
                        onClick={() => handleRemoveDont(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
        
        <DialogFooter>
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium"
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 font-medium flex items-center gap-2"
          >
            <Save size={16} />
            Save Guidelines
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}