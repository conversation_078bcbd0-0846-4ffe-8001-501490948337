'use client';

import { Card, CardContent } from "@kit/ui/card";
import { But<PERSON> } from "@kit/ui/button";
import { Edit, MoreVertical, Trash, Loader2, AlertCircle, Eye } from "lucide-react";
import { Trans } from "@kit/ui/trans";
import { Badge } from "@kit/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@kit/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@kit/ui/alert-dialog";
import { ICP } from "~/types/icp";
import { useState } from "react";
import EditICPDialog from "../../../personas/_components/edit-icp-dialog";
import { useZero } from "~/hooks/use-zero";
import { toast } from "sonner";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";

interface HorizontalICPCardProps {
  icp: ICP;
  accountSlug: string;
  onViewDetails: (icp: ICP) => void;
}

export function HorizontalICPCard({ icp, accountSlug, onViewDetails }: HorizontalICPCardProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const isGenerating = icp.is_generating;
  const errorGenerating = icp.error_generating;

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      zero.mutate.icps.delete({
        id: icp.id,
      });
      console.log('Deleting ICP:', icp.id);
    } catch (error) {
      console.error('Error deleting ICP:', error);
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
    }
  };
  
  const handleRetry = async () => {
    const reference_material = icp.reference_material || [];
    const reference_description = icp.reference_description || '';
    await handleDelete();
  
    zero.mutate.icps.insert({
      id: crypto.randomUUID(),
      values: {
        company_id: workspace.account.id,
        withAi: icp.withAi,
        withLinkedIn: icp.withLinkedIn,
        name: null,
        data: {},
        error_generating: false,
        reference_material: reference_material,
        reference_description: reference_description,
        linkedInUrls: icp.linkedInUrls
      }
    });
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger detail view if clicking on buttons or dropdowns
    if ((e.target as HTMLElement).closest('button') || (e.target as HTMLElement).closest('[role="menuitem"]')) {
      return;
    }
    if (!isGenerating && !errorGenerating) {
      onViewDetails(icp);
    }
  };

  // Error state
  if (errorGenerating) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="flex items-center justify-between py-4 px-6">
          <div className="flex items-center gap-4">
            <AlertCircle className="h-8 w-8 text-red-500 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-red-900">
                <Trans i18nKey="personas:errorGenerating" defaults="Error Generating ICP" />
              </h3>
              <p className="text-sm text-red-600">
                <Trans i18nKey="personas:errorGeneratingDescription" defaults="Something went wrong during generation" />
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            className="flex-shrink-0"
          >
            <Trans i18nKey="personas:tryAgain" defaults="Try Again" />
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Loading state
  if (isGenerating) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="flex items-center justify-between py-4 px-6">
          <div className="flex items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary flex-shrink-0" />
            <div>
              <h3 className="font-semibold">
                <Trans i18nKey="personas:generating" defaults="Generating ICP..." />
              </h3>
              <p className="text-sm text-muted-foreground">
                <Trans i18nKey="personas:generatingDescription" defaults="Creating your ideal customer profile" />
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get all available properties to show
  const industries = icp.data?.target_industries as string[] || [];
  const markets = icp.data?.geography_markets as string[] || [];
  const revenue = icp.data?.company_revenue_range_usd;
  const employees = icp.data?.company_employee_range;
  const departments = icp.data?.decision_making_departments as string[] || [];
  const useCases = icp.data?.use_cases_problems as string[] || [];
  const buyingTriggers = icp.data?.buying_triggers as string[] || [];
  const painPoints = icp.data?.pain_points as string[] || [];
  const goals = icp.data?.goals_objectives as string[] || [];
  const techStack = icp.data?.technology_stack as string[] || [];
  const companyStage = icp.data?.company_stage as string[] || [];
  const communicationChannels = icp.data?.communication_channels as string[] || [];

  // Count total properties that have data
  const allProperties = [
    { name: 'Industries', data: industries },
    { name: 'Markets', data: markets },
    { name: 'Revenue', data: revenue?.min && revenue?.max ? [revenue] : [] },
    { name: 'Employees', data: employees?.min && employees?.max ? [employees] : [] },
    { name: 'Decision Makers', data: departments },
    { name: 'Use Cases', data: useCases },
    { name: 'Buying Triggers', data: buyingTriggers },
    { name: 'Pain Points', data: painPoints },
    { name: 'Goals', data: goals },
    { name: 'Tech Stack', data: techStack },
    { name: 'Company Stage', data: companyStage },
    { name: 'Communication Channels', data: communicationChannels }
  ].filter(prop => prop.data.length > 0);

  const displayedProperties = allProperties.slice(0, 4);
  const remainingCount = Math.max(0, allProperties.length - 4);

  return (
    <>
      <Card 
        className="hover:shadow-md transition-shadow cursor-pointer"
        onClick={handleCardClick}
      >
        <CardContent className="flex items-start justify-between py-4 px-6">
          <div className="flex-1 min-w-0 pr-4">
            {/* ICP Name */}
            <h3 className="font-semibold text-lg mb-3 truncate">{icp.name}</h3>
            
            {/* Properties listed vertically */}
            <div className="space-y-2">
              {displayedProperties.map((property, index) => (
                <div key={index} className="flex items-start gap-2">
                  <span className="text-xs text-muted-foreground font-medium min-w-0 flex-shrink-0 w-20">
                    {property.name}:
                  </span>
                  <div className="flex flex-wrap gap-1 min-w-0">
                    {property.name === 'Revenue' && revenue?.min && revenue?.max ? (
                      <Badge variant="secondary" className="text-xs py-0">
                        ${revenue.min.toLocaleString()} - ${revenue.max.toLocaleString()}
                      </Badge>
                    ) : property.name === 'Employees' && employees?.min && employees?.max ? (
                      <Badge variant="secondary" className="text-xs py-0">
                        {employees.min.toLocaleString()} - {employees.max.toLocaleString()}
                      </Badge>
                    ) : (
                      (property.data as string[]).slice(0, 3).map((item) => (
                        <Badge key={item} variant="outline" className="text-xs py-0">
                          {item}
                        </Badge>
                      ))
                    )}
                    {property.data.length > 3 && property.name !== 'Revenue' && property.name !== 'Employees' && (
                      <Badge variant="outline" className="text-xs py-0 text-muted-foreground">
                        +{property.data.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
              
              {remainingCount > 0 && (
                <div className="flex items-center gap-2 pt-1">
                  <span className="text-xs text-muted-foreground font-medium">
                    +{remainingCount} more {remainingCount === 1 ? 'property' : 'properties'}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onViewDetails(icp);
              }}
            >
              <Eye className="h-4 w-4 mr-2" />
              <Trans i18nKey="common:view" defaults="View" />
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setIsEditDialogOpen(true)}>
                  <Edit className="h-4 w-4 mr-2" />
                  <Trans i18nKey="common:edit" defaults="Edit" />
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash className="h-4 w-4 mr-2" />
                  <Trans i18nKey="common:delete" defaults="Delete" />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>
      
      <EditICPDialog
        icp={icp}
        isOpen={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      />

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <Trans i18nKey="personas:deleteICPTitle" defaults="Delete ICP" />
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {icp.name}? This action cannot be undone and will also delete all associated personas.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              <Trans i18nKey="common:cancel" defaults="Cancel" />
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  <Trans i18nKey="common:deleting" defaults="Deleting..." />
                </>
              ) : (
                <>
                  <Trash className="h-4 w-4 mr-2" />
                  <Trans i18nKey="common:delete" defaults="Delete" />
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
