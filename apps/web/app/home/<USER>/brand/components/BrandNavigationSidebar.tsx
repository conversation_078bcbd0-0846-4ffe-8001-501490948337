'use client';

import React, { useState } from 'react';
import { User, MessageSquare, Palette, BookOpen, Settings, UserRound } from 'lucide-react';
import { ICPAlert } from '../../_components/navigation/icp-alert';

interface BrandNavigationSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
}

const navigationItems = [
  { 
    id: 'brand-profile', 
    label: 'Brand Profile', 
    icon: <User size={18} />,
    description: 'Mission, vision, and core attributes'
  },
  { 
    id: 'messaging-strategy', 
    label: 'Messaging Strategy', 
    icon: <MessageSquare size={18} />,
    description: 'Voice, tone, and differentiators'
  },
  { 
    id: 'customer-profiles', 
    label: 'Customer Profiles', 
    icon: <UserRound size={18} />,
    description: 'Ideal customer profiles and personas'
  },
  { 
    id: 'visual-identity', 
    label: 'Visual Identity', 
    icon: <Palette size={18} />,
    description: 'Colors, fonts, logos, and photography'
  },
  // { 
  //   id: 'prompt-library', 
  //   label: 'Prompt Library', 
  //   icon: <BookOpen size={18} />,
  //   description: 'Content generation prompts'
  // },
  { 
    id: 'settings', 
    label: 'Settings', 
    icon: <Settings size={18} />,
    description: 'Brand settings and configuration'
  }
];

export function BrandNavigationSidebar({ activeSection, onSectionChange }: BrandNavigationSidebarProps) {
  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full overflow-y-auto">
      <div className="p-4">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Brand Guide</h2>
        <nav className="space-y-2">
          {navigationItems.map((item) => (
            <button
              key={item.id}
              onClick={() => onSectionChange(item.id)}
              className={`w-full flex flex-col items-start p-3 rounded-lg text-left transition-colors ${
                activeSection === item.id
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="flex items-center w-full mb-1">
                <span className="mr-3">{item.icon}</span>
                <span className="font-medium">{item.label}</span>
                {item.id === 'customer-profiles' && <ICPAlert />}
              </div>
              <span className="text-xs text-gray-500 ml-9">{item.description}</span>
            </button>
          ))}
        </nav>
      </div>
    </div>
  );
}