// apps/web/app/constants/app.constants.ts
export const COMMON_CHANNELS = [
  'linkedin',
  'x',
  'blogs',
  'email',
  'website'
] as const;

export const COMMON_LANGUAGES = [
  'English',
  'Finnish',
  'Spanish',
  'French', 
  'German',
  'Italian',
  'Portuguese',
  'Dutch'
] as const;

// Channel to Content Type mapping (based on CONTENT_TYPE_CHOICES pattern)
export const CHANNEL_CONTENT_TYPE_MAP: Record<string, string> = {
  'linkedin': 'LinkedIn Post',
  'x': 'X post', 
  'blogs': 'Blog Post',
  'email': 'Content Share Email',
  'website': 'Website Content'
} as const;

// Function to get content type based on channel
export function getContentTypeFromChannel(channel: string): string {
  return CHANNEL_CONTENT_TYPE_MAP[channel] || 'General Content';
}

// Type-safe exports
export type CommonChannel = typeof COMMON_CHANNELS[number];
export type CommonLanguage = typeof COMMON_LANGUAGES[number];