'use client';

import { useState, useTransition, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "@kit/ui/sonner";
import { Trans } from "@kit/ui/trans";
import { Button } from "@kit/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@kit/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@kit/ui/form";
import { Input } from "@kit/ui/input";
import { Textarea } from "@kit/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { Plus, X, Building2, Globe, Target, Zap, Users, AlertTriangle, Code, FileText, DollarSign, TrendingUp, Users2, <PERSON>, StickyNote } from "lucide-react";
import { CreateICPFormSchema, CreateICPFormSchemaType, CustomField } from "../_lib/schema/icp.schema";
import { useZero } from '~/hooks/use-zero';
import { 
  GEOGRAPHY_MARKETS_OPTIONS,
  DECISION_MAKING_DEPARTMENTS_OPTIONS,
  RED_FLAGS_DISQUALIFIERS_OPTIONS,
  CONTENT_NEEDS_OPTIONS
} from "~/types/icp";
import MultiSelectField from "./multi-select-field";
import NumberRangeField from "./number-range-field";
import TagInputField from "./tag-input-field";
import HybridSelectField from "./hybrid-select-field";
import CustomFieldDialog from "./custom-field-dialog";
import { useTeamAccountWorkspace } from "@kit/team-accounts/hooks/use-team-account-workspace";

interface CreateICPDialogProps {
  isNew?: boolean;
  companyId: string;
  setIsNew: (isNew: boolean) => void;
}

type FieldKey = keyof Omit<CreateICPFormSchemaType, 'name'>;

interface FieldDefinition {
  key: FieldKey;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  component: 'TagInput' | 'MultiSelect' | 'NumberRange' | 'Select' | 'Textarea' | 'HybridSelect';
  options?: readonly string[];
  placeholder?: string;
}

const AVAILABLE_FIELDS: FieldDefinition[] = [
  {
    key: 'target_industries',
    label: 'Target Industries',
    icon: Building2,
    description: 'Industries you target',
    component: 'TagInput',
    placeholder: 'e.g., SaaS, Healthcare, Finance...'
  },
  {
    key: 'geography_markets',
    label: 'Geography / Markets',
    icon: Globe,
    description: 'Geographic markets',
    component: 'MultiSelect',
    options: GEOGRAPHY_MARKETS_OPTIONS,
    placeholder: 'Select geographic markets'
  },
  {
    key: 'use_cases_problems',
    label: 'Use Cases / Problems',
    icon: Target,
    description: 'Problems you solve',
    component: 'TagInput',
    placeholder: 'e.g., Customer churn, Data silos...'
  },
  {
    key: 'buying_triggers',
    label: 'Buying Triggers',
    icon: Zap,
    description: 'What triggers purchases',
    component: 'TagInput',
    placeholder: 'e.g., Compliance requirements, Growth...'
  },
  {
    key: 'decision_making_departments',
    label: 'Decision-Making Departments',
    icon: Users,
    description: 'Who makes buying decisions',
    component: 'MultiSelect',
    options: DECISION_MAKING_DEPARTMENTS_OPTIONS,
    placeholder: 'Select departments'
  },
  {
    key: 'technologies_used',
    label: 'Technologies Used',
    icon: Code,
    description: 'Tech stack they use',
    component: 'TagInput',
    placeholder: 'e.g., Salesforce, AWS, React...'
  },
  {
    key: 'content_needs',
    label: 'Content Needs',
    icon: FileText,
    description: 'Content they need',
    component: 'HybridSelect',
    options: CONTENT_NEEDS_OPTIONS,
    placeholder: 'Select or type content needs'
  },
  {
    key: 'red_flags_disqualifiers',
    label: 'Red Flags / Disqualifiers',
    icon: AlertTriangle,
    description: 'Deal breakers',
    component: 'HybridSelect',
    options: RED_FLAGS_DISQUALIFIERS_OPTIONS,
    placeholder: 'Select or type red flags'
  },
  {
    key: 'company_size_employees',
    label: 'Company Size (Employees)',
    icon: Users2,
    description: 'Employee count range',
    component: 'NumberRange',
    placeholder: 'Min - Max employees'
  },
  {
    key: 'company_revenue_range_usd',
    label: 'Revenue Range (USD)',
    icon: DollarSign,
    description: 'Annual revenue range',
    component: 'NumberRange',
    placeholder: 'Min - Max revenue'
  },
  {
    key: 'typical_contract_value',
    label: 'Contract Value (USD)',
    icon: TrendingUp,
    description: 'Typical deal size',
    component: 'NumberRange',
    placeholder: 'Min - Max contract value'
  },
  {
    key: 'lifecycle_stage',
    label: 'Lifecycle Stage',
    icon: Clock,
    description: 'Company maturity',
    component: 'Select',
    placeholder: 'Select lifecycle stage'
  },
  {
    key: 'notes',
    label: 'Notes',
    icon: StickyNote,
    description: 'Additional information',
    component: 'Textarea',
    placeholder: 'Add any additional notes...'
  }
];

export default function CreateICPDialog({ isNew = false, setIsNew }: CreateICPDialogProps) {
  const [pending, startTransition] = useTransition();
  const [activeFields, setActiveFields] = useState<Set<FieldKey>>(new Set());
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const router = useRouter();
  const searchParams = useSearchParams();
  const zero = useZero();
  const { account } = useTeamAccountWorkspace();
  // Clean up URL parameter when dialog opens via isNew prop
 
  const form = useForm<any>({
    resolver: zodResolver(CreateICPFormSchema),
    defaultValues: {
      name: '',
      custom_fields: [],
    },
  });

  const addField = (fieldKey: FieldKey) => {
    setActiveFields(prev => new Set([...prev, fieldKey]));
    
    // Set default values for the field
    const field = AVAILABLE_FIELDS.find(f => f.key === fieldKey);
    if (field) {
      if (field.component === 'TagInput' || field.component === 'MultiSelect' || field.component === 'HybridSelect') {
        form.setValue(fieldKey, []);
      } else if (field.component === 'NumberRange') {
        form.setValue(fieldKey, { min: null, max: null });
      } else if (field.component === 'Select') {
        form.setValue(fieldKey, null);
      } else if (field.component === 'Textarea') {
        form.setValue(fieldKey, '');
      }
    }
  };

  const removeField = (fieldKey: FieldKey) => {
    setActiveFields(prev => {
      const newSet = new Set(prev);
      newSet.delete(fieldKey);
      return newSet;
    });
    form.setValue(fieldKey, undefined);
  };

  const addCustomField = (field: CustomField) => {
    setCustomFields(prev => [...prev, field]);
    // Update form with custom fields
    form.setValue('custom_fields', [...customFields, field]);
  };

  const removeCustomField = (fieldId: string) => {
    setCustomFields(prev => prev.filter(f => f.id !== fieldId));
    const updatedFields = customFields.filter(f => f.id !== fieldId);
    form.setValue('custom_fields', updatedFields);
  };

  const updateCustomFieldValue = (fieldId: string, value: any) => {
    setCustomFields(prev => prev.map(f => 
      f.id === fieldId ? { ...f, value } : f
    ));
    const updatedFields = customFields.map(f => 
      f.id === fieldId ? { ...f, value } : f
    );
    form.setValue('custom_fields', updatedFields);
  };

  const onSubmit = (data: any) => {
    startTransition(async () => {
      zero.mutate.icps.insert({
        id: crypto.randomUUID(),
        values:  {
          company_id: account.id,
          withAi: false,
          withLinkedIn: false,
          name: data.name,
          error_generating: false,
          reference_material: [],
          reference_description: '',
          data: {
            ...data,
            custom_fields: customFields,
          },
        }
      });
      setIsNew(false);
      form.reset();
      setActiveFields(new Set());
      setCustomFields([]);

    });
  };

  const renderField = (fieldDef: FieldDefinition) => {
    const { key, label, component, options, placeholder } = fieldDef;

    return (
      <FormField
        key={key}
        name={key}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center justify-between">
              <span>{label}</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeField(key)}
                className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </Button>
            </FormLabel>
            <FormControl>
              {(() => {
                switch (component) {
                  case 'TagInput':
                    return (
                      <TagInputField
                        value={field.value || []}
                        onChange={field.onChange}
                        placeholder={placeholder}
                      />
                    );
                  case 'MultiSelect':
                    return (
                      <MultiSelectField
                        value={field.value || []}
                        onChange={field.onChange}
                        options={options!}
                        placeholder={placeholder}
                      />
                    );
                  case 'HybridSelect':
                    return (
                      <HybridSelectField
                        value={field.value || []}
                        onChange={field.onChange}
                        options={options!}
                        placeholder={placeholder}
                      />
                    );
                  case 'NumberRange':
                    return (
                      <NumberRangeField
                        value={field.value || { min: null, max: null }}
                        onChange={field.onChange}
                        placeholder={{ min: "Min", max: "Max" }}
                      />
                    );
                  case 'Select':
                    return (
                      <Select value={field.value || ''} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder={placeholder} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Startup">Startup</SelectItem>
                          <SelectItem value="Scale-up">Scale-up</SelectItem>
                          <SelectItem value="Mature Enterprise">Mature Enterprise</SelectItem>
                        </SelectContent>
                      </Select>
                    );
                  case 'Textarea':
                    return (
                      <Textarea
                        placeholder={placeholder}
                        className="resize-none"
                        rows={4}
                        {...field}
                        value={field.value || ''}
                      />
                    );
                  default:
                    return null;
                }
              })()}
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  const renderCustomField = (field: CustomField) => {
    const { id, label, type, value, options } = field;

    return (
      <div key={id} className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium">{label}</label>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => removeCustomField(id)}
            className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
        
        <div>
          {(() => {
            switch (type) {
              case 'text':
                return (
                  <Input
                    value={value || ''}
                    onChange={(e) => updateCustomFieldValue(id, e.target.value)}
                    placeholder={`Enter ${label.toLowerCase()}`}
                  />
                );
              case 'textarea':
                return (
                  <Textarea
                    value={value || ''}
                    onChange={(e) => updateCustomFieldValue(id, e.target.value)}
                    placeholder={`Enter ${label.toLowerCase()}`}
                    rows={3}
                    className="resize-none"
                  />
                );
              case 'number':
                return (
                  <Input
                    type="number"
                    value={value || ''}
                    onChange={(e) => updateCustomFieldValue(id, e.target.value ? Number(e.target.value) : null)}
                    placeholder={`Enter ${label.toLowerCase()}`}
                  />
                );
              case 'numberrange':
                return (
                  <NumberRangeField
                    value={value || { min: null, max: null }}
                    onChange={(val) => updateCustomFieldValue(id, val)}
                    placeholder={{ min: "Min", max: "Max" }}
                  />
                );
              case 'select':
                return (
                  <Select value={value || ''} onValueChange={(val) => updateCustomFieldValue(id, val)}>
                    <SelectTrigger>
                      <SelectValue placeholder={`Select ${label.toLowerCase()}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {options?.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                );
              case 'multiselect':
                return (
                  <TagInputField
                    value={value || []}
                    onChange={(val) => updateCustomFieldValue(id, val)}
                    placeholder={`Add ${label.toLowerCase()} options`}
                  />
                );
              default:
                return null;
            }
          })()}
        </div>
      </div>
    );
  };

  const availableFields = AVAILABLE_FIELDS.filter(field => !activeFields.has(field.key));

  return (
    <Dialog open={isNew} onOpenChange={setIsNew}>
      <DialogTrigger asChild>
        <Button className="fixed bottom-6 right-6 rounded-full h-14 w-14 shadow-lg">
          <Plus className="h-6 w-6" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-h-[90vh] overflow-y-auto w-[95vw] max-w-[95vw] sm:w-full sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="personas:createICP" defaults="Create Ideal Customer Profile" />
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* ICP Name - Always visible */}
            <FormField
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ICP Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Mid-Market SaaS Companies" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Dynamic Fields */}
            {Array.from(activeFields).map(fieldKey => {
              const fieldDef = AVAILABLE_FIELDS.find(f => f.key === fieldKey);
              return fieldDef ? renderField(fieldDef) : null;
            })}

            {/* Custom Fields */}
            {customFields.map((field) => renderCustomField(field))}

            {/* Add Field Buttons */}
            {(availableFields.length > 0) && (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-muted-foreground">Add Fields</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {availableFields.map((field) => {
                    const Icon = field.icon;
                    return (
                      <Button
                        key={field.key}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addField(field.key)}
                        className="h-auto p-3 flex flex-col gap-1 text-left"
                      >
                        <div className="flex items-center gap-2 w-full">
                          <Icon className="h-4 w-4 flex-shrink-0" />
                          <span className="text-xs font-medium truncate">{field.label}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{field.description}</span>
                      </Button>
                    );
                  })}
                  
                  {/* Custom Field Button */}
                  <CustomFieldDialog onAddField={addCustomField} />
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 pt-6">
              <Button type="button" variant="outline" onClick={() => setIsNew(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={pending}>
                {pending ? 'Creating...' : 'Create ICP'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}