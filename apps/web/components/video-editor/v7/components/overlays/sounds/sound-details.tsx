import React, { useRef, useState, useEffect } from "react";
import { SoundOverlay, TTSTranscript, CaptionOverlay, OverlayType, Caption } from "../../../types";
import { Play, Pause, Captions } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TranscriptViewer } from "./transcript-viewer";
import { useEditorContext } from "../../../contexts/editor-context";
import { useTimelinePositioning } from "../../../hooks/use-timeline-positioning";
import { useTimeline } from "../../../contexts/timeline-context";
import { generateOverlayId } from "../../../utils/uuid";
import { segmentWordsIntoCaptions, segmentTextIntoSentences } from "../../../utils/caption-segmentation";

/**
 * Interface for the props passed to the SoundDetails component
 * @interface
 * @property {SoundOverlay} localOverlay - The current sound overlay object containing source and styles
 * @property {Function} setLocalOverlay - Callback function to update the sound overlay
 */
interface SoundDetailsProps {
  localOverlay: SoundOverlay;
  setLocalOverlay: (overlay: SoundOverlay) => void;
}

/**
 * SoundDetails Component
 *
 * A component that provides an interface for playing and controlling sound overlays.
 * Features include:
 * - Play/pause functionality
 * - Volume control with mute/unmute option
 * - Visual feedback for playback state
 *
 * @component
 * @param {SoundDetailsProps} props - Component props
 * @returns {JSX.Element} Rendered component
 */
export const SoundDetails: React.FC<SoundDetailsProps> = ({
  localOverlay,
  setLocalOverlay,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isGeneratingCaptions, setIsGeneratingCaptions] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { addOverlay, overlays, durationInFrames } = useEditorContext();
  const { findNextAvailablePosition } = useTimelinePositioning();
  const { visibleRows } = useTimeline();

  useEffect(() => {
    audioRef.current = new Audio(localOverlay.src);
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, [localOverlay.src]);

  /**
   * Toggles the play/pause state of the audio
   * Handles audio playback and updates the UI state
   */
  const togglePlay = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current
        .play()
        .catch((error) => console.error("Error playing audio:", error));
    }
    setIsPlaying(!isPlaying);
  };

  /**
   * Updates the styles of the sound overlay
   * @param {Partial<SoundOverlay["styles"]>} updates - Partial style updates to apply
   */
  const handleStyleChange = (updates: Partial<SoundOverlay["styles"]>) => {
    const updatedOverlay = {
      ...localOverlay,
      styles: {
        ...localOverlay.styles,
        ...updates,
      },
    };
    setLocalOverlay(updatedOverlay);
  };

  /**
   * Generates captions from the audio overlay's transcript or by extracting transcript from audio
   * Creates properly timed captions that sync with the audio overlay
   */
  const handleGenerateCaptions = async () => {
    setIsGeneratingCaptions(true);

    try {
      let captions: Caption[] = [];

      // If no transcript exists, try to extract it from the audio
      if (!localOverlay.transcript) {
        try {
          // Call the audio transcript extraction endpoint
          const response = await fetch("/api/latest/extract-audio-transcript", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              audioUrl: localOverlay.src,
              fileName: `audio_${localOverlay.id}.mp3`,
              folderName: "captions",
              companyId: "temp", // You might want to get this from context
              assetId: localOverlay.id,
            }),
          });

          if (!response.ok) {
            throw new Error("Failed to extract transcript from audio");
          }

          const transcriptData = await response.json();

          // Use the extracted transcript to generate captions
          if (transcriptData.detailedTranscript) {
            // First, collect all words with proper timing
            const soundStartFrame = localOverlay.from;
            const soundStartMs = (soundStartFrame / 30) * 1000;

            const allWords = transcriptData.detailedTranscript.flatMap((segment: any) =>
              (segment.words || []).map((word: any) => ({
                word: word.word,
                startMs: soundStartMs + word.startMs,
                endMs: soundStartMs + word.endMs,
                confidence: word.confidence || 0.8
              }))
            );

            // Use smart segmentation to create optimal captions
            const segments = segmentWordsIntoCaptions(allWords, {
              maxWordsPerCaption: 6,
              maxCharactersPerCaption: 50,
              maxDurationMs: 3500,
              preferSentenceBreaks: true,
              pauseThresholdMs: 400
            });

            captions = segments.map(segment => ({
              text: segment.text,
              startMs: segment.startMs,
              endMs: segment.endMs,
              timestampMs: null,
              confidence: segment.confidence,
              words: segment.words
            }));
          } else if (transcriptData.fullTranscript) {
            // Fallback to full transcript if detailed not available
            const sentences = transcriptData.fullTranscript
              .split(/[.!?]+/)
              .map((sentence: string) => sentence.trim())
              .filter((sentence: string) => sentence.length > 0);

            const soundStartFrame = localOverlay.from;
            const soundStartMs = (soundStartFrame / 30) * 1000;
            const avgWordsPerMinute = 150;
            const msPerWord = (60 * 1000) / avgWordsPerMinute;

            let currentOffset = 0;

            captions = sentences.map((sentence: string) => {
              const words = sentence.split(/\s+/);
              const segmentStartMs = soundStartMs + currentOffset;
              const segmentDurationMs = words.length * msPerWord;
              const segmentEndMs = segmentStartMs + segmentDurationMs;

              const segmentWords = words.map((word, wordIndex) => ({
                word,
                startMs: segmentStartMs + (wordIndex * msPerWord),
                endMs: segmentStartMs + ((wordIndex + 1) * msPerWord),
                confidence: 0.8
              }));

              currentOffset += segmentDurationMs + 500;

              return {
                text: sentence,
                startMs: segmentStartMs,
                endMs: segmentEndMs,
                timestampMs: null,
                confidence: 0.8,
                words: segmentWords
              };
            });
          }
        } catch (transcriptError) {
          console.error("Failed to extract transcript:", transcriptError);
          throw new Error("Could not extract transcript from audio. Please add a transcript to this audio first.");
        }
      } else if (localOverlay.transcript && typeof localOverlay.transcript === 'object' && 'detailedTranscript' in localOverlay.transcript) {
        // Use robust TTS transcript with smart segmentation
        const ttsTranscript = localOverlay.transcript as TTSTranscript;
        const soundStartFrame = localOverlay.from;
        const soundStartMs = (soundStartFrame / 30) * 1000;

        // Collect all words from all segments
        const allWords = ttsTranscript.detailedTranscript.flatMap(segment =>
          segment.words.map(word => ({
            word: word.word,
            startMs: soundStartMs + word.start * 1000,
            endMs: soundStartMs + word.end * 1000,
            confidence: segment.confidence
          }))
        );

        // Use smart segmentation for optimal caption breaking
        const segments = segmentWordsIntoCaptions(allWords, {
          maxWordsPerCaption: 7,
          maxCharactersPerCaption: 55,
          maxDurationMs: 4000,
          preferSentenceBreaks: true,
          pauseThresholdMs: 300 // TTS has less natural pauses
        });

        captions = segments.map(segment => ({
          text: segment.text,
          startMs: segment.startMs,
          endMs: segment.endMs,
          timestampMs: null,
          confidence: segment.confidence,
          words: segment.words
        }));
      } else if (typeof localOverlay.transcript === 'string') {
        // Fallback to simple string transcript with smart segmentation
        const sentences = segmentTextIntoSentences(localOverlay.transcript, {
          maxWordsPerCaption: 8,
          maxCharactersPerCaption: 60,
          preferSentenceBreaks: true
        });

        const soundStartFrame = localOverlay.from;
        const soundStartMs = (soundStartFrame / 30) * 1000;
        const avgWordsPerMinute = 150;
        const msPerWord = (60 * 1000) / avgWordsPerMinute;

        let currentOffset = 0;

        captions = sentences.map(sentence => {
          const words = sentence.split(/\s+/);
          const segmentStartMs = soundStartMs + currentOffset;
          const segmentDurationMs = words.length * msPerWord;
          const segmentEndMs = segmentStartMs + segmentDurationMs;

          const segmentWords = words.map((word, wordIndex) => ({
            word,
            startMs: segmentStartMs + (wordIndex * msPerWord),
            endMs: segmentStartMs + ((wordIndex + 1) * msPerWord),
            confidence: 0.9
          }));

          currentOffset += segmentDurationMs + 500; // Add pause between sentences

          return {
            text: sentence,
            startMs: segmentStartMs,
            endMs: segmentEndMs,
            timestampMs: null,
            confidence: 0.9,
            words: segmentWords
          };
        });
      } else {
        throw new Error("No transcript available for this audio");
      }

      // Find position for the caption overlay
      const position = findNextAvailablePosition(overlays, visibleRows, durationInFrames);

      // Ensure we have captions to add
      if (captions.length === 0) {
        throw new Error("No captions were generated from the audio");
      }

      // Calculate the actual duration based on the last caption
      const lastCaption = captions[captions.length - 1];
      const captionDurationMs = lastCaption.endMs - ((localOverlay.from / 30) * 1000);
      const captionDurationFrames = Math.ceil((captionDurationMs / 1000) * 30);

      // Create the caption overlay
      const newCaptionOverlay: CaptionOverlay = {
        id: generateOverlayId(),
        type: OverlayType.CAPTION,
        from: localOverlay.from, // Start at the same time as the audio
        durationInFrames: Math.max(captionDurationFrames, localOverlay.durationInFrames), // Ensure it covers all captions
        captions,
        left: 230,
        top: 414,
        width: 833,
        height: 269,
        rotation: 0,
        isDragging: false,
        row: position.row,
      };

      addOverlay(newCaptionOverlay);
    } catch (error) {
      console.error("Error generating captions:", error);
      // Could add toast notification here
    } finally {
      setIsGeneratingCaptions(false);
    }
  };

  // Check if this sound has a robust TTS transcript
  const hasRobustTranscript =
    localOverlay.transcript &&
    typeof localOverlay.transcript === 'object' &&
    'detailedTranscript' in localOverlay.transcript;

  return (
    <div className="space-y-4">
      {/* Sound Info with Play Button */}
      <div className="flex items-center gap-3 p-4 bg-background/50 rounded-md border">
        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlay}
          className="h-8 w-8 rounded-full bg-transparent hover:bg-accent text-foreground"
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
        </Button>
        <div className="min-w-0 flex-1">
          <p className="text-sm font-medium text-foreground truncate">
            {localOverlay.content}
          </p>
        </div>
      </div>

      {/* Generate Captions Button */}
      <div className="space-y-4">
        <Button
          onClick={handleGenerateCaptions}
          disabled={isGeneratingCaptions}
          className="w-full"
          variant="outline"
        >
          <Captions className="w-4 h-4 mr-2" />
          {isGeneratingCaptions
            ? "Generating Captions..."
            : localOverlay.transcript
              ? "Generate Captions"
              : "Extract & Generate Captions"
          }
        </Button>
        {!localOverlay.transcript && (
          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            This will extract transcript from the audio and create synchronized captions
          </p>
        )}
      </div>

      {/* Settings Tabs */}

      <div className="space-y-4 mt-4">
        <div className="space-y-6">
          {/* Volume Settings */}
          <div className="space-y-4 rounded-md bg-background/50 p-4 border">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-foreground">Volume</h3>
              <button
                onClick={() =>
                  handleStyleChange({
                    volume: localOverlay?.styles?.volume === 0 ? 1 : 0,
                  })
                }
                className={`text-xs px-2.5 py-1.5 rounded-md transition-colors ${
                  (localOverlay?.styles?.volume ?? 1) === 0
                    ? "bg-primary/20 text-primary hover:bg-primary/30"
                    : "bg-muted text-muted-foreground hover:bg-muted/70"
                }`}
              >
                {(localOverlay?.styles?.volume ?? 1) === 0 ? "Unmute" : "Mute"}
              </button>
            </div>

            <div className="flex items-center gap-3 pt-1">
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={localOverlay?.styles?.volume ?? 1}
                onChange={(e) =>
                  handleStyleChange({ volume: parseFloat(e.target.value) })
                }
                className="flex-1 accent-primary h-1.5 rounded-full bg-muted"
              />
              <span className="text-xs text-muted-foreground min-w-[40px] text-right">
                {Math.round((localOverlay?.styles?.volume ?? 1) * 100)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Show transcript details for TTS-generated sounds */}
      {hasRobustTranscript && (
        <TranscriptViewer transcript={localOverlay.transcript as TTSTranscript} />
      )}
    </div>
  );
};
