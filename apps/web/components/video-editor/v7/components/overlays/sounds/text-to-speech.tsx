import React, { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Play, Pause, Loader2 } from "lucide-react";
import { SoundOverlay, OverlayType, TTSTranscript } from "../../../types";
import { generateOverlayId } from "../../../utils/uuid";
import { useTimelinePositioning } from "../../../hooks/use-timeline-positioning";
import { useEditorContext } from "../../../contexts/editor-context";
import { useTimeline } from "../../../contexts/timeline-context";
import { TranscriptViewer } from "./transcript-viewer";

interface TextToSpeechProps {
  className?: string;
}

export const TextToSpeech: React.FC<TextToSpeechProps> = ({ className }) => {
  const [text, setText] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAudio, setGeneratedAudio] = useState<{
    audioBase64: string;
    duration: number;
    transcript: TTSTranscript;
  } | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const { addOverlay, overlays, durationInFrames } = useEditorContext();
  const { findNextAvailablePosition } = useTimelinePositioning();
  const { visibleRows } = useTimeline();

  const handleGenerate = async () => {
    if (!text.trim()) return;

    setIsGenerating(true);
    try {
      const response = await fetch("/api/latest/text-to-speech", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: text.trim(),
          language: "English",
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate speech");
      }

      const result = await response.json();
      setGeneratedAudio({
        audioBase64: result.audioBase64,
        duration: result.duration,
        transcript: result.transcript,
      });

      // Create audio element for playback
      if (audioRef.current) {
        audioRef.current.pause();
      }
      audioRef.current = new Audio(`data:audio/mp3;base64,${result.audioBase64}`);
    } catch (error) {
      console.error("Error generating speech:", error);
      // Could add toast notification here
    } finally {
      setIsGenerating(false);
    }
  };

  const togglePlay = () => {
    if (!audioRef.current || !generatedAudio) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play().catch((error) => {
        console.error("Error playing audio:", error);
      });
      setIsPlaying(true);

      // Reset playing state when audio ends
      audioRef.current.onended = () => {
        setIsPlaying(false);
      };
    }
  };

  const handleAddToTimeline = () => {
    if (!generatedAudio) return;

    // Find the next available position on the timeline
    const { from, row } = findNextAvailablePosition(
      overlays,
      visibleRows,
      durationInFrames
    );

    // Create the sound overlay configuration
    const newSoundOverlay: SoundOverlay = {
      id: generateOverlayId(),
      type: OverlayType.SOUND,
      content: `TTS: ${text.substring(0, 30)}${text.length > 30 ? "..." : ""}`,
      src: `data:audio/mp3;base64,${generatedAudio.audioBase64}`,
      transcript: generatedAudio.transcript,
      from,
      row,
      // Layout properties
      left: 0,
      top: 0,
      width: 1920,
      height: 100,
      rotation: 0,
      isDragging: false,
      durationInFrames: Math.ceil(generatedAudio.duration * 30), // 30fps
      styles: {
        opacity: 1,
      },
    };

    addOverlay(newSoundOverlay);

    // Reset the form after adding to timeline
    setText("");
    setGeneratedAudio(null);
    setIsPlaying(false);
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Text Input */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-900 dark:text-gray-100">
          Enter text to convert to speech:
        </label>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Type your text here..."
          className="w-full p-3 border border-gray-200 dark:border-gray-700 rounded-md
            bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100
            placeholder-gray-500 dark:placeholder-gray-400 resize-none
            focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={4}
          disabled={isGenerating}
        />
      </div>

      {/* Generate Button */}
      <Button
        onClick={handleGenerate}
        disabled={!text.trim() || isGenerating}
        className="w-full"
      >
        {isGenerating ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Generating...
          </>
        ) : (
          "Generate"
        )}
      </Button>

      {/* Generated Audio Preview */}
      {generatedAudio && (
        <div className="space-y-3 p-4 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={togglePlay}
              className="h-8 w-8 rounded-full bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700
                text-gray-700 dark:text-gray-300"
            >
              {isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
            </Button>
            <div className="min-w-0 flex-1">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                Generated Speech
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {generatedAudio.duration.toFixed(1)}s • {generatedAudio.transcript.wordCount} words •
                {(generatedAudio.transcript.averageConfidence * 100).toFixed(0)}% confidence
              </p>
              <p className="text-xs text-gray-400 dark:text-gray-500 truncate mt-1">
                {generatedAudio.transcript.fullTranscript.substring(0, 60)}
                {generatedAudio.transcript.fullTranscript.length > 60 ? "..." : ""}
              </p>
            </div>
          </div>

          <Button
            onClick={handleAddToTimeline}
            className="w-full"
            size="sm"
          >
            Add to Timeline
          </Button>

          {/* Transcript Details */}
          <TranscriptViewer transcript={generatedAudio.transcript} />
        </div>
      )}
    </div>
  );
};