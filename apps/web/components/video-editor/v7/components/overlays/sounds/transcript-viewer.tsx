import React, { useState } from "react";
import { TTSTranscript } from "../../../types";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp, FileText, Clock, Users } from "lucide-react";

interface TranscriptViewerProps {
  transcript: TTSTranscript;
  className?: string;
}

export const TranscriptViewer: React.FC<TranscriptViewerProps> = ({
  transcript,
  className = "",
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<number | null>(null);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toFixed(1).padStart(4, '0')}`;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Transcript Header */}
      <div className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <FileText className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
            Transcript Details
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="h-8 w-8 p-0"
        >
          {isExpanded ? (
            <ChevronUp className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* Transcript Stats */}
      <div className="grid grid-cols-3 gap-2 text-xs">
        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
          <Clock className="w-3 h-3" />
          {formatTime(transcript.duration)}
        </div>
        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
          <Users className="w-3 h-3" />
          {transcript.wordCount} words
        </div>
        <div className="flex items-center gap-1 text-gray-600 dark:text-gray-400">
          <span className="w-3 h-3 rounded-full bg-green-500"></span>
          {(transcript.averageConfidence * 100).toFixed(0)}%
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="space-y-3">
          {/* Full Transcript */}
          <div className="p-3 bg-gray-50 dark:bg-gray-800/50 rounded-md border border-gray-200 dark:border-gray-700">
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
              Full Transcript
            </h4>
            <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
              {transcript.fullTranscript}
            </p>
          </div>

          {/* Detailed Segments */}
          {transcript.detailedTranscript.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                Segments ({transcript.detailedTranscript.length})
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {transcript.detailedTranscript.map((segment, index) => (
                  <div
                    key={index}
                    onClick={() =>
                      setSelectedSegment(selectedSegment === index ? null : index)
                    }
                    className={`p-2 rounded-md border cursor-pointer transition-colors ${
                      selectedSegment === index
                        ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"
                        : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {segment.startTime ? formatTime(segment.startTime) : "0:00"} -{" "}
                        {segment.endTime ? formatTime(segment.endTime) : "0:00"}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {(segment.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                    <p className="text-xs text-gray-700 dark:text-gray-300">
                      {segment.transcript}
                    </p>

                    {/* Word-level details */}
                    {selectedSegment === index && segment.words.length > 0 && (
                      <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                        <span className="text-xs text-gray-500 dark:text-gray-400 mb-1 block">
                          Word Timing:
                        </span>
                        <div className="flex flex-wrap gap-1">
                          {segment.words.map((word, wordIndex) => (
                            <span
                              key={wordIndex}
                              className="inline-flex items-center px-1.5 py-0.5 rounded text-xs
                                bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
                              title={`${formatTime(word.start)} - ${formatTime(word.end)}`}
                            >
                              {word.word}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="grid grid-cols-2 gap-3 text-xs text-gray-600 dark:text-gray-400">
            <div>
              <span className="font-medium">Language:</span> {transcript.language}
            </div>
            <div>
              <span className="font-medium">Voice ID:</span> {transcript.voiceId}
            </div>
            <div className="col-span-2">
              <span className="font-medium">Generated:</span>{" "}
              {new Date(transcript.generatedAt).toLocaleString()}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};