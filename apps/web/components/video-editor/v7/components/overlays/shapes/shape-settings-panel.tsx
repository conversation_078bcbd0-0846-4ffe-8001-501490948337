import React from "react";
import { ShapeOverlay } from "../../../types";
import { AnimationSettings } from "../../shared/animation-preview";
import { animationTemplates } from "../../../templates/animation-templates";

/**
 * Props for the ShapeSettingsPanel component
 */
interface ShapeSettingsPanelProps {
  localOverlay: ShapeOverlay;
  handleStyleChange: (field: keyof ShapeOverlay["styles"], value: any) => void;
}

/**
 * Panel component for managing shape overlay animation settings
 * Allows users to select enter and exit animations for shape overlays
 */
export const ShapeSettingsPanel: React.FC<ShapeSettingsPanelProps> = ({
  localOverlay,
  handleStyleChange,
}) => {
  // Handlers for animation selection
  const handleEnterAnimationSelect = (animationKey: string) => {
    const currentAnimation = localOverlay.styles.animation || {};
    handleStyleChange("animation", {
      ...currentAnimation,
      enter: animationKey,
    });
  };

  const handleExitAnimationSelect = (animationKey: string) => {
    const currentAnimation = localOverlay.styles.animation || {};
    handleStyleChange("animation", {
      ...currentAnimation,
      exit: animationKey,
    });
  };

  return (
    <AnimationSettings
      animations={animationTemplates}
      selectedEnterAnimation={localOverlay.styles.animation?.enter}
      selectedExitAnimation={localOverlay.styles.animation?.exit}
      onEnterAnimationSelect={handleEnterAnimationSelect}
      onExitAnimationSelect={handleExitAnimationSelect}
    />
  );
};