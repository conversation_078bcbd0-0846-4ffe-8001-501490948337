import React, { useCallback } from "react";
import { useEditorContext } from "../../../contexts/editor-context";
import { ShapeOverlay } from "../../../types";
import { PaintBucket, Settings } from "lucide-react";
import debounce from "lodash/debounce";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ShapeSettingsPanel } from "./shape-settings-panel";
import { ShapeStylePanel } from "./shape-style-panel";

/**
 * Props for the ShapeDetails component
 */
interface ShapeDetailsProps {
  localOverlay: ShapeOverlay;
  setLocalOverlay: (overlay: ShapeOverlay) => void;
}

/**
 * ShapeDetails component provides a UI for editing shape overlay properties and styles.
 * It includes a live preview and tabbed panels for settings and styling.
 * Changes are debounced to prevent excessive re-renders.
 */
export const ShapeDetails: React.FC<ShapeDetailsProps> = ({
  localOverlay,
  setLocalOverlay,
}) => {
  const { changeOverlay, selectedOverlayId, overlays } = useEditorContext();

  /**
   * Debounced function to update the overlay in the global state
   * Prevents excessive re-renders by waiting 300ms between updates
   */
  const debouncedUpdateOverlay = useCallback(
    debounce((id: number, newOverlay: ShapeOverlay) => {
      changeOverlay(id, newOverlay);
    }, 300),
    [changeOverlay]
  );

  /**
   * Handles changes to direct overlay properties
   */
  const handleInputChange = (field: keyof ShapeOverlay, value: string) => {
    // Update local state immediately for responsive UI
    setLocalOverlay({ ...localOverlay, [field]: value });

    // Debounce the actual overlay update if an overlay is selected
    if (selectedOverlayId !== null) {
      const overlay = overlays.find((o) => o.id === selectedOverlayId);
      if (overlay) {
        debouncedUpdateOverlay(selectedOverlayId, {
          ...overlay,
          [field]: value,
        } as ShapeOverlay);
      }
    }
  };

  /**
   * Handles changes to nested style properties
   */
  const handleStyleChange = (
    field: keyof ShapeOverlay["styles"],
    value: string | number
  ) => {
    // Update local state immediately for responsive UI
    setLocalOverlay({
      ...localOverlay,
      styles: { ...localOverlay.styles, [field]: value },
    });

    // Debounce the actual overlay update if an overlay is selected
    if (selectedOverlayId !== null) {
      const overlay = overlays.find((o) => o.id === selectedOverlayId);
      if (overlay) {
        debouncedUpdateOverlay(selectedOverlayId, {
          ...overlay,
          styles: { ...overlay.styles, [field]: value },
        } as ShapeOverlay);
      }
    }
  };

  // Get shape type display name
  const getShapeDisplayName = (content: string) => {
    switch (content) {
      case "rectangle":
        return "Rectangle";
      case "circle":
        return "Circle";
      case "triangle":
        return "Triangle";
      default:
        return "Shape";
    }
  };

  return (
    <div className="space-y-4">
      {/* Preview Section */}
      <div className="flex flex-col gap-2">
        <div className="flex flex-col gap-2 bg-slate-100/90 dark:bg-gray-800 rounded-sm overflow-hidden border border-border">
          <div className="relative w-full h-32 p-4 flex items-center justify-center">
            <div
              className="relative"
              style={{
                width: "80px",
                height: "60px",
                backgroundColor: localOverlay.styles.fill || "#000000",
                borderRadius: localOverlay.content === "circle" ? "50%" : (localOverlay.styles.borderRadius || "0px"),
                border: localOverlay.styles.stroke
                  ? `${localOverlay.styles.strokeWidth || 1}px solid ${localOverlay.styles.stroke}`
                  : undefined,
                boxShadow: localOverlay.styles.boxShadow,
                opacity: localOverlay.styles.opacity ?? 1,
                background: localOverlay.styles.gradient || localOverlay.styles.fill || "#000000",
                clipPath: localOverlay.content === "triangle"
                  ? "polygon(50% 0%, 0% 100%, 100% 100%)"
                  : undefined,
              }}
            />
          </div>
          <div className="px-4 pb-2">
            <p className="text-xs text-muted-foreground">
              {getShapeDisplayName(localOverlay.content)}
            </p>
          </div>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="settings" className="w-full">
        <TabsList className="w-full grid grid-cols-2 bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-sm border border-gray-200 dark:border-gray-700 gap-1">
          <TabsTrigger
            value="settings"
            className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-gray-900 dark:data-[state=active]:text-white
            rounded-sm transition-all duration-200 text-gray-600 dark:text-zinc-400 hover:text-gray-900 dark:hover:text-zinc-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50"
          >
            <span className="flex items-center gap-2 text-xs">
              <Settings className="w-3 h-3" />
              Settings
            </span>
          </TabsTrigger>
          <TabsTrigger
            value="style"
            className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-gray-900 dark:data-[state=active]:text-white
            rounded-sm transition-all duration-200 text-gray-600 dark:text-zinc-400 hover:text-gray-900 dark:hover:text-zinc-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50"
          >
            <span className="flex items-center gap-2 text-xs">
              <PaintBucket className="w-3 h-3" />
              Style
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4 mt-4">
          <ShapeSettingsPanel
            localOverlay={localOverlay}
            handleStyleChange={handleStyleChange}
          />
        </TabsContent>

        <TabsContent value="style" className="space-y-4 mt-4">
          <ShapeStylePanel
            localOverlay={localOverlay}
            handleInputChange={handleInputChange}
            handleStyleChange={handleStyleChange}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};