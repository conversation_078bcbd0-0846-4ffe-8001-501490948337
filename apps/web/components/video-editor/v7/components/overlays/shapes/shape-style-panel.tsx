import React from "react";
import { ShapeOverlay } from "../../../types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Slider } from "@/components/ui/slider";
import ColorPicker from "react-best-gradient-color-picker";

/**
 * Available shape types
 */
const shapeTypes = [
  { value: "rectangle", label: "Rectangle" },
  { value: "circle", label: "Circle" },
  { value: "triangle", label: "Triangle" },
];

/**
 * Props for the ShapeStylePanel component
 */
interface ShapeStylePanelProps {
  localOverlay: ShapeOverlay;
  handleInputChange: (field: keyof ShapeOverlay, value: string) => void;
  handleStyleChange: (field: keyof ShapeOverlay["styles"], value: any) => void;
}

/**
 * Panel component for managing shape overlay styling options
 * Provides controls for shape type, colors, opacity, stroke, and other visual properties
 */
export const ShapeStylePanel: React.FC<ShapeStylePanelProps> = ({
  localOverlay,
  handleInputChange,
  handleStyleChange,
}) => {
  return (
    <div className="space-y-6">
      {/* Shape Settings */}
      <div className="space-y-4 rounded-md bg-background/50 p-4 border">
        <h3 className="text-sm font-medium">Shape</h3>

        <div className="space-y-2">
          <label className="text-xs text-muted-foreground">Shape Type</label>
          <Select
            value={localOverlay.content}
            onValueChange={(value) => handleInputChange("content", value)}
          >
            <SelectTrigger className="w-full text-xs">
              <SelectValue placeholder="Select a shape" />
            </SelectTrigger>
            <SelectContent>
              {shapeTypes.map((shape) => (
                <SelectItem
                  key={shape.value}
                  value={shape.value}
                  className="text-xs"
                >
                  {shape.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Appearance Settings */}
      <div className="space-y-4 rounded-md bg-background/50 p-4 border">
        <h3 className="text-sm font-medium">Appearance</h3>

        <div className="grid grid-cols-2 gap-4">
          {/* Fill Color */}
          <div className="space-y-2">
            <label className="text-xs text-muted-foreground">Fill Color</label>
            <Popover>
              <PopoverTrigger asChild>
                <div
                  className="h-8 w-full rounded-md border cursor-pointer"
                  style={{ backgroundColor: localOverlay.styles.fill || "#000000" }}
                />
              </PopoverTrigger>
              <PopoverContent
                className="w-[330px] dark:bg-gray-900 border border-gray-700"
                side="right"
              >
                <ColorPicker
                  value={localOverlay.styles.fill || "#000000"}
                  onChange={(color) => handleStyleChange("fill", color)}
                  hideInputs
                  hideHue
                  hideControls
                  hideColorTypeBtns
                  hideAdvancedSliders
                  hideColorGuide
                  hideInputType
                  height={200}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Stroke Color */}
          <div className="space-y-2">
            <label className="text-xs text-muted-foreground">Stroke Color</label>
            <Popover>
              <PopoverTrigger asChild>
                <div
                  className="h-8 w-full rounded-md border cursor-pointer"
                  style={{
                    backgroundColor: localOverlay.styles.stroke || "transparent",
                    backgroundImage: localOverlay.styles.stroke
                      ? "none"
                      : "linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)",
                    backgroundSize: "8px 8px",
                    backgroundPosition: "0 0, 0 4px, 4px -4px, -4px 0px"
                  }}
                />
              </PopoverTrigger>
              <PopoverContent
                className="w-[330px] dark:bg-gray-900 border border-gray-700"
                side="right"
              >
                <ColorPicker
                  value={localOverlay.styles.stroke || "#000000"}
                  onChange={(color) => handleStyleChange("stroke", color)}
                  hideInputs
                  hideHue
                  hideControls
                  hideColorTypeBtns
                  hideAdvancedSliders
                  hideColorGuide
                  hideInputType
                  height={200}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Opacity */}
        <div className="space-y-2">
          <label className="text-xs text-muted-foreground">
            Opacity ({Math.round((localOverlay.styles.opacity ?? 1) * 100)}%)
          </label>
          <Slider
            value={[(localOverlay.styles.opacity ?? 1) * 100]}
            onValueChange={(value) => handleStyleChange("opacity", value[0] / 100)}
            max={100}
            min={0}
            step={1}
            className="w-full"
          />
        </div>

        {/* Stroke Width */}
        {localOverlay.styles.stroke && (
          <div className="space-y-2">
            <label className="text-xs text-muted-foreground">
              Stroke Width ({localOverlay.styles.strokeWidth || 1}px)
            </label>
            <Slider
              value={[localOverlay.styles.strokeWidth || 1]}
              onValueChange={(value) => handleStyleChange("strokeWidth", value[0])}
              max={20}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        )}

        {/* Border Radius (only for rectangle) */}
        {localOverlay.content === "rectangle" && (
          <div className="space-y-2">
            <label className="text-xs text-muted-foreground">
              Corner Radius ({parseInt(localOverlay.styles.borderRadius || "0")}px)
            </label>
            <Slider
              value={[parseInt(localOverlay.styles.borderRadius || "0")]}
              onValueChange={(value) => handleStyleChange("borderRadius", `${value[0]}px`)}
              max={50}
              min={0}
              step={1}
              className="w-full"
            />
          </div>
        )}
      </div>

      {/* Effects */}
      <div className="space-y-4 rounded-md bg-background/50 p-4 border">
        <h3 className="text-sm font-medium">Effects</h3>

        <div className="space-y-2">
          <label className="text-xs text-muted-foreground">Box Shadow</label>
          <Select
            value={localOverlay.styles.boxShadow || "none"}
            onValueChange={(value) => handleStyleChange("boxShadow", value === "none" ? undefined : value)}
          >
            <SelectTrigger className="w-full text-xs">
              <SelectValue placeholder="Select shadow" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">No Shadow</SelectItem>
              <SelectItem value="0 1px 3px rgba(0,0,0,0.1)">Light</SelectItem>
              <SelectItem value="0 4px 6px rgba(0,0,0,0.1)">Medium</SelectItem>
              <SelectItem value="0 10px 15px rgba(0,0,0,0.1)">Heavy</SelectItem>
              <SelectItem value="0 20px 25px rgba(0,0,0,0.15)">Extra Heavy</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};