import React from "react";
import { ShapeOverlay } from "../../../types";

/**
 * Props for the ShapeLayerContent component
 */
interface ShapeLayerContentProps {
  overlay: ShapeOverlay;
}

/**
 * ShapeLayerContent Component
 *
 * Renders shape overlays in the video composition.
 * Currently supports rectangles with customizable styling.
 */
export const ShapeLayerContent: React.FC<ShapeLayerContentProps> = ({
  overlay,
}) => {
  const { content, styles } = overlay;

  // Base styles for the shape
  const shapeStyle: React.CSSProperties = {
    width: "100%",
    height: "100%",
    position: "absolute",
    top: 0,
    left: 0,
    opacity: styles.opacity ?? 1,
    zIndex: styles.zIndex ?? 1,
    transform: styles.transform ?? "none",
  };

  // Render based on shape type
  switch (content) {
    case "rectangle":
      return (
        <div
          style={{
            ...shapeStyle,
            backgroundColor: styles.fill || "#000000",
            border: styles.stroke ? `${styles.strokeWidth || 1}px solid ${styles.stroke}` : undefined,
            borderRadius: styles.borderRadius || "0px",
            boxShadow: styles.boxShadow,
            background: styles.gradient || styles.fill || "#000000",
          }}
        />
      );

    case "circle":
      return (
        <div
          style={{
            ...shapeStyle,
            backgroundColor: styles.fill || "#000000",
            border: styles.stroke ? `${styles.strokeWidth || 1}px solid ${styles.stroke}` : undefined,
            borderRadius: "50%",
            boxShadow: styles.boxShadow,
            background: styles.gradient || styles.fill || "#000000",
          }}
        />
      );

    case "triangle":
      // Create triangle using CSS borders
      return (
        <div
          style={{
            ...shapeStyle,
            width: 0,
            height: 0,
            borderLeft: "50% solid transparent",
            borderRight: "50% solid transparent",
            borderBottom: `100% solid ${styles.fill || "#000000"}`,
            backgroundColor: "transparent",
            boxShadow: styles.boxShadow,
          }}
        />
      );

    default:
      // Fallback to rectangle for unknown shapes
      return (
        <div
          style={{
            ...shapeStyle,
            backgroundColor: styles.fill || "#000000",
            border: styles.stroke ? `${styles.strokeWidth || 1}px solid ${styles.stroke}` : undefined,
            borderRadius: styles.borderRadius || "0px",
            boxShadow: styles.boxShadow,
            background: styles.gradient || styles.fill || "#000000",
          }}
        />
      );
  }
};