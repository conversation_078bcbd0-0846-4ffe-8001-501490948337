"use client";

import React, { useState } from "react";
import { useEditorContext } from "../../../contexts/editor-context";
import { OverlayType, ShapeOverlay } from "../../../types";
import { ShapeDetails } from "./shape-details";
import { SelectShapeOverlay } from "./select-shape-overlay";

export const ShapeOverlaysPanel: React.FC = () => {
  const { selectedOverlayId, overlays } = useEditorContext();
  const [localOverlay, setLocalOverlay] = useState<ShapeOverlay | null>(null);

  // Update local overlay when selected overlay changes or when overlays change
  React.useEffect(() => {
    if (selectedOverlayId === null) {
      return;
    }

    const selectedOverlay = overlays.find(
      (overlay) => overlay.id === selectedOverlayId
    );

    if (selectedOverlay?.type === OverlayType.SHAPE) {
      setLocalOverlay(selectedOverlay as ShapeOverlay);
    }
  }, [selectedOverlayId, overlays]);

  const handleSetLocalOverlay = (overlay: ShapeOverlay) => {
    setLocalOverlay(overlay);
  };

  const isValidShapeOverlay = localOverlay && selectedOverlayId !== null;

  return (
    <div className="p-2 h-full bg-background">
      {!isValidShapeOverlay ? (
        <SelectShapeOverlay setLocalOverlay={handleSetLocalOverlay} />
      ) : (
        <ShapeDetails
          localOverlay={localOverlay}
          setLocalOverlay={handleSetLocalOverlay}
        />
      )}
    </div>
  );
};