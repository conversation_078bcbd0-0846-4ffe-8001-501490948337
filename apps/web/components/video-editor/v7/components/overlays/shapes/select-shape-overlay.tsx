import React from "react";
import { ShapeOverlay } from "../../../types";

interface SelectShapeOverlayProps {
  setLocalOverlay: (overlay: ShapeOverlay) => void;
}

export const SelectShapeOverlay: React.FC<SelectShapeOverlayProps> = () => {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center text-muted-foreground">
        <div className="mb-2">
          <svg
            className="w-8 h-8 mx-auto mb-2 opacity-50"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          </svg>
        </div>
        <p className="text-sm">Select a shape to edit its properties</p>
      </div>
    </div>
  );
};