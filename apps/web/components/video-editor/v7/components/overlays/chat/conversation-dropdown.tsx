'use client';

import React from 'react';
import { Button } from '@kit/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kit/ui/dropdown-menu';
import { ChevronDown, Plus, MessageSquare } from 'lucide-react';
import { Trans } from '@kit/ui/trans';

interface VideoConversation {
  id: string;
  title?: string;
  user_id: string;
  company_id: string;
  video_project_id?: string;
  created_at: number;
  updated_at: number;
}

interface ConversationDropdownProps {
  conversations: VideoConversation[];
  currentConversationId: string | null;
  onSelectConversation: (conversationId: string) => void;
  onNewConversation: () => void;
}

export function ConversationDropdown({
  conversations,
  currentConversationId,
  onSelectConversation,
  onNewConversation,
}: ConversationDropdownProps) {
  const currentConversation = conversations.find(c => c.id === currentConversationId);
  
  const formatConversationTitle = (conversation: VideoConversation): string => {
    if (conversation.title) {
      return conversation.title;
    }
    
    // Fallback to date-based title
    const date = new Date(conversation.created_at || Date.now());
    return `Chat ${date.toLocaleDateString()}`;
  };

  const formatDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="h-8 text-xs font-medium bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <MessageSquare className="h-3.5 w-3.5 mr-1.5" />
          {currentConversation ? (
            <span className="max-w-[120px] truncate">
              {formatConversationTitle(currentConversation)}
            </span>
          ) : (
            <Trans i18nKey="videoEditor:newChat" defaults="New Chat" />
          )}
          <ChevronDown className="h-3.5 w-3.5 ml-1.5" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        className="w-64 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700"
        align="end"
        sideOffset={5}
      >
        {/* New Conversation Option */}
        <DropdownMenuItem
          onClick={onNewConversation}
          className="flex items-center px-3 py-2 text-sm text-gray-700 dark:text-zinc-200 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer"
        >
          <Plus className="h-4 w-4 mr-2" />
          <Trans i18nKey="videoEditor:startNewChat" defaults="Start New Chat" />
        </DropdownMenuItem>
        
        {conversations.length > 0 && (
          <>
            <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-700" />
            
            {/* Recent Conversations */}
            <div className="px-3 py-2">
              <p className="text-xs font-medium text-gray-500 dark:text-zinc-400 uppercase tracking-wider">
                <Trans i18nKey="videoEditor:recentChats" defaults="Recent Chats" />
              </p>
            </div>
            
            {conversations.slice(0, 8).map((conversation) => (
              <DropdownMenuItem
                key={conversation.id}
                onClick={() => onSelectConversation(conversation.id)}
                className={`flex items-start justify-between px-3 py-2 text-sm cursor-pointer ${
                  currentConversationId === conversation.id
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-zinc-200 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">
                    {formatConversationTitle(conversation)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-zinc-400 mt-0.5">
                    {formatDate(conversation.updated_at || conversation.created_at)}
                  </div>
                </div>
                
                {currentConversationId === conversation.id && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 flex-shrink-0 mt-1.5" />
                )}
              </DropdownMenuItem>
            ))}
          </>
        )}
        
        {conversations.length === 0 && (
          <div className="px-3 py-6 text-center">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400 dark:text-zinc-500" />
            <p className="text-sm text-gray-500 dark:text-zinc-400">
              <Trans i18nKey="videoEditor:noChatsYet" defaults="No chats yet" />
            </p>
            <p className="text-xs text-gray-400 dark:text-zinc-500 mt-1">
              <Trans i18nKey="videoEditor:startFirstChat" defaults="Start your first conversation" />
            </p>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
