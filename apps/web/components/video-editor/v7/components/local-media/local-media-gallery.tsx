"use client";

import React, { useState, useRef, useEffect } from "react";
import { useLocalMedia } from "../../contexts/local-media-context";
import { formatBytes, formatDuration } from "../../utils/format-utils";
import { But<PERSON> } from "@kit/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { Loader2, Upload, Trash2, Image, Video, Music, ChevronDown } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@kit/ui/dialog";
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { listAssetFolders, listFolderContents, uploadVideoFile } from '~/services/storage';
import { AssetFolder, StorageFile } from '~/types/assets';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';

/**
 * User Media Gallery Component
 *
 * Displays the user's uploaded media files and provides functionality to:
 * - Upload new media files
 * - Filter media by type (image, video, audio)
 * - Preview media files
 * - Delete media files
 * - Add media to the timeline
 */
export function LocalMediaGallery({
  onSelectMedia,
}: {
  onSelectMedia?: (mediaFile: any) => void;
}) {
  const { localMediaFiles, addMediaFile, removeMediaFile, isLoading } =
    useLocalMedia();
  const [selectedFolder, setSelectedFolder] = useState<string>("Uploads");
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [assetFiles, setAssetFiles] = useState<StorageFile[]>([]);
  const [loadingAssets, setLoadingAssets] = useState(false);
  const [videoDurations, setVideoDurations] = useState<{ [key: string]: number }>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  const workspace = useTeamAccountWorkspace();
  const queryClient = useQueryClient();
  const zero = useZero();

  // Fetch assets from database to get transcript URLs
  const [assetsData] = useZeroQuery(
    zero?.query.assets.where('account_id', '=', workspace.account.id),
    {
      ttl: '5m' // Cache for 5 minutes
    }
  );

  // Log assets data when it changes
  useEffect(() => {
    if (assetsData) {
      console.log('🗄️ Assets data loaded:', {
        totalAssets: assetsData.length,
        assetsWithTranscripts: assetsData.filter(asset => asset.has_transcript).length,
        transcriptUrls: assetsData
          .filter(asset => asset.has_transcript && asset.transcript_url)
          .map(asset => ({ fileName: asset.file_name, transcriptUrl: asset.transcript_url }))
      });
    }
  }, [assetsData]);

  // Fetch asset folders
  const { data: assetFolders = [] } = useQuery<AssetFolder[]>({
    queryKey: ['assetFolders', workspace.account.id],
    queryFn: () => listAssetFolders(workspace.account.id),
    enabled: !!workspace.account.id,
  });

  // Load asset files when folder selection changes
  useEffect(() => {
    const loadAssetFiles = async () => {
      if (!selectedFolder) {
        setAssetFiles([]);
        return;
      }

      try {
        setLoadingAssets(true);
        const files = await listFolderContents(selectedFolder, workspace.account.id);
        setAssetFiles(files);
      } catch (error) {
        console.error('Error loading asset files:', error);
        setAssetFiles([]);
      } finally {
        setLoadingAssets(false);
      }
    };

    loadAssetFiles();
  }, [selectedFolder, workspace.account.id]);

  // Get video duration using HTML5 video element
  const getVideoDuration = (url: string): Promise<number> => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      video.addEventListener('loadedmetadata', () => {
        resolve(video.duration || 0);
      });
      video.addEventListener('error', () => {
        resolve(0);
      });
      video.src = url;
    });
  };

  // Load video durations asynchronously
  useEffect(() => {
    const loadVideoDurations = async () => {
      const videoFiles = assetFiles.filter(file => file.type?.startsWith('video/'));

      for (const file of videoFiles) {
        if (!videoDurations[file.path]) {
          try {
            const duration = await getVideoDuration(file.url);
            setVideoDurations(prev => ({
              ...prev,
              [file.path]: duration
            }));
          } catch (error) {
            console.warn('Could not load duration for:', file.path, error);
          }
        }
      }
    };

    if (assetFiles.length > 0) {
      loadVideoDurations();
    }
  }, [assetFiles]);

  // Get files to display based on selected folder
  const getFilesToDisplay = () => {
    // Show only asset files from selected folder
    return assetFiles.map(file => {
      const fileName = file.name || file.path.split('/').pop() || 'Unknown';

      // Look up transcript URL from assets data
      const assetRecord = assetsData?.find(asset => asset.file_name === fileName);
      const transcriptUrl = assetRecord?.has_transcript ? assetRecord.transcript_url : undefined;

      // Log transcript lookup for video/audio files
      if (file.type && (file.type.startsWith('video/') || file.type.startsWith('audio/'))) {
        console.log(`📄 Transcript lookup for ${fileName}:`, {
          fileName,
          fileType: file.type,
          assetRecordFound: !!assetRecord,
          hasTranscript: assetRecord?.has_transcript || false,
          transcriptUrl: transcriptUrl,
          assetsDataLength: assetsData?.length || 0
        });
      }

      return {
        id: file.path,
        name: fileName,
        type: getFileTypeFromMimeType(file.type),
        path: file.url,
        size: 0,
        lastModified: Date.now(),
        thumbnail: file.url,
        duration: videoDurations[file.path] || 0, // Use detected duration
        transcript: transcriptUrl, // Add transcript URL
        source: 'asset'
      };
    });
  };

  // Helper function to get file type from mime type
  const getFileTypeFromMimeType = (mimeType?: string) => {
    if (!mimeType) return 'file';
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    if (mimeType.startsWith('audio/')) return 'audio';
    return 'file';
  };

  const filteredMedia = getFilesToDisplay();

  // Handle file upload
  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      try {
        setUploadError(null);
        const file = files[0];
        if (!file) return;
        
        // Upload to the Uploads folder in asset library
        await uploadVideoFile(file, workspace.account.id);
        
        // Invalidate and refetch asset folders to ensure Uploads folder appears
        await queryClient.invalidateQueries({ 
          queryKey: ['assetFolders', workspace.account.id] 
        });
        
        // Switch to Uploads folder and refresh asset files
        setSelectedFolder("Uploads");
        
        // Reset the input value to allow uploading the same file again
        event.target.value = "";
      } catch (error) {
        console.error("Error uploading file:", error);
        setUploadError("Failed to upload file. Please try again.");
        event.target.value = "";
      }
    }
  };

  // Handle upload button click
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // Handle media selection
  const handleMediaSelect = (file: any) => {
    setSelectedFile(file);
    setPreviewOpen(true);
  };

  // Add media to timeline
  const handleAddToTimeline = async () => {
    if (selectedFile && onSelectMedia) {
      let fileWithDuration = { ...selectedFile };

      // Get video duration if it's a video file
      if (selectedFile.type === 'video') {
        try {
          const duration = await getVideoDuration(selectedFile.path);
          fileWithDuration.duration = duration;
        } catch (error) {
          console.warn('Could not detect video duration:', error);
        }
      }

      onSelectMedia(fileWithDuration);
      setPreviewOpen(false);
    }
  };

  // Render preview content based on file type
  const renderPreviewContent = () => {
    if (!selectedFile) return null;

    const commonClasses =
      "max-h-[50vh] w-full object-contain rounded-lg shadow-sm";

    switch (selectedFile.type) {
      case "image":
        return (
          <div className="relative bg-gray-50 dark:bg-gray-900 rounded-lg p-2">
            <img
              src={selectedFile.path}
              alt={selectedFile.name}
              className={`${commonClasses} object-contain`}
            />
          </div>
        );
      case "video":
        return (
          <div className="relative bg-gray-50 dark:bg-gray-900 rounded-lg p-2">
            <video
              src={selectedFile.path}
              controls
              className={commonClasses}
              controlsList="nodownload"
              playsInline
            />
          </div>
        );
      case "audio":
        return (
          <div className="flex flex-col items-center space-y-3 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <Music className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <audio
              src={
                selectedFile.path.startsWith("http")
                  ? selectedFile.path
                  : `${window.location.origin}${selectedFile.path}`
              }
              controls
              className="w-[280px] max-w-full"
              controlsList="nodownload"
            />
          </div>
        );
      default:
        return (
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Unsupported file type
          </div>
        );
    }
  };

  // Render media item
  const renderMediaItem = (file: any) => {
    console.log(file);
    return (
      <div
        key={file.id}
        className="relative group/item border dark:border-gray-700 border-gray-200 rounded-md overflow-hidden cursor-pointer 
          hover:border-blue-500 dark:hover:border-blue-400 transition-all 
          bg-white dark:bg-gray-800/80 shadow-sm hover:shadow-md"
        onClick={() => handleMediaSelect(file)}
      >
        {/* Thumbnail */}
        <div className="aspect-video relative">
          {file.type === "image" && (
            <img
              src={file.thumbnail || file.path}
              alt={file.name}
              className="w-full h-full object-cover bg-gray-50 dark:bg-gray-900"
            />
          )}
          {file.type === "video" && (
            <>
              <img
                src={file.thumbnail || file.path}
                alt={file.name}
                className="w-full h-full object-cover bg-gray-50 dark:bg-gray-900"
              />
              {file.duration > 0 && (
                <div className="absolute bottom-1.5 right-1.5 bg-black/75 dark:bg-black/90 text-white text-xs px-1.5 py-0.5 rounded-md">
                  {formatDuration(file.duration)}
                </div>
              )}
            </>
          )}
          {file.type === "audio" && (
            <div className="w-full h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
              <Music className="w-10 h-10 text-gray-400 dark:text-gray-500" />
            </div>
          )}
        </div>

        {/* Media info */}
        <div className="p-2.5">
          <p className="text-sm font-medium truncate text-gray-900 dark:text-gray-100">
            {file.name}
          </p>
          <div className="flex items-center justify-between mt-0.5">
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {file.size > 0 ? formatBytes(file.size) : ''}
            </p>
            <span className="text-xs bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded text-gray-600 dark:text-gray-300">
              Asset
            </span>
          </div>
        </div>

        {/* Delete button removed - files are managed in the asset library */}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-sm">Media Library</h2>
        <div>
          <Button
            variant="outline"
            size="sm"
            className="gap-1"
            onClick={handleUploadClick}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Upload className="w-4 h-4" />
            )}
            Upload
          </Button>
          <input
            ref={fileInputRef}
            id="file-upload"
            type="file"
            className="hidden"
            onChange={handleFileUpload}
            accept="image/*,video/*,audio/*"
            disabled={isLoading}
          />
        </div>
      </div>

      {uploadError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded mb-4">
          {uploadError}
        </div>
      )}

      {/* Folder Selection */}
      <div className="flex items-center gap-2 mb-4">
        <span className="text-xs text-gray-600 dark:text-gray-400">Folder</span>
        <Select value={selectedFolder} onValueChange={setSelectedFolder}>
          <SelectTrigger className="w-auto min-w-[120px] h-8 text-xs">
            <SelectValue />
            <ChevronDown className="w-3 h-3" />
          </SelectTrigger>
          <SelectContent>
            {assetFolders.map((folder) => (
              <SelectItem key={folder.name} value={folder.name}>
                {folder.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Media Grid */}
      <div className="flex-1 overflow-y-auto">
        {(isLoading || loadingAssets) ? (
          <div className="h-full flex flex-col items-center justify-center text-center space-y-4 text-sm text-gray-500">
            <Loader2 className="w-5 h-5 animate-spin" />
            <p>Loading media files...</p>
          </div>
        ) : filteredMedia.length === 0 ? (
          <div className="h-full flex flex-col items-center justify-center text-center space-y-3">
            <div className="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
              <Upload className="w-4 h-4 text-gray-400" />
            </div>
            <div className="space-y-1">
              <p className="text-sm font-medium">No media files</p>
              <p className="text-xs text-gray-500">
                {selectedFolder 
                  ? `No files in ${selectedFolder} folder`
                  : 'Select a folder to view files'
                }
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleUploadClick}
              className="text-xs"
            >
              Upload Media
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-2 gap-2">
            {filteredMedia.map(renderMediaItem)}
          </div>
        )}
      </div>

      {/* Media Preview Dialog */}
      <Dialog open={previewOpen} onOpenChange={setPreviewOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedFile?.name}</DialogTitle>
            <DialogDescription>
              {selectedFile?.type} • {formatBytes(selectedFile?.size)}
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-center">{renderPreviewContent()}</div>
          <div className="flex justify-end mt-4">
            <Button variant="default" size="sm" onClick={handleAddToTimeline}>
              Add to Timeline
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
