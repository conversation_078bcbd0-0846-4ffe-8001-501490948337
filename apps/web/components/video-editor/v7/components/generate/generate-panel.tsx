"use client";

import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useQuery } from '@tanstack/react-query';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { listAssetFolders, listFolderContents } from '~/services/storage';
import { AssetFolder, StorageFile } from '~/types/assets';
import { Loader2, File, Video, Image, Music, FileText } from 'lucide-react';
import { formatBytes } from '../../utils/format-utils';

// Types for the generate form
type VideoType =
  | "LinkedIn Ad"
  | "X Ad"
  | "Promo Video"
  | "Presentation"
  | "Animated Explainer"
  | "Technical Tutorial";

type SourceType = "Product Document" | "Asset Library" | "URL";

interface GenerateFormData {
  type: VideoType | null;
  length: string;
  sourceType: SourceType | null;
  selectedDocument: any | null;
  selectedFolder: string | null;
  selectedAsset: StorageFile | null;
  url: string;
}

const VIDEO_TYPES: VideoType[] = [
  "LinkedIn Ad",
  "X Ad",
  "Promo Video",
  "Presentation",
  "Animated Explainer",
  "Technical Tutorial"
];

/**
 * GeneratePanel Component
 *
 * Allows users to generate videos from various sources (URLs, PDFs, videos)
 * with different video types and lengths. Provides a comprehensive form
 * interface for video generation configuration.
 */
export const GeneratePanel: React.FC = () => {
  const workspace = useTeamAccountWorkspace();
  const [currentStep, setCurrentStep] = useState<1 | 2 | 3>(1); // 1: Setup, 2: Review, 3: Generate
  const [formData, setFormData] = useState<GenerateFormData>({
    type: null,
    length: "",
    sourceType: null,
    selectedDocument: null,
    selectedFolder: null,
    selectedAsset: null,
    url: ""
  });
  const [assetFiles, setAssetFiles] = useState<StorageFile[]>([]);
  const [loadingAssets, setLoadingAssets] = useState(false);

  // Fetch asset folders
  const { data: assetFolders = [] } = useQuery<AssetFolder[]>({
    queryKey: ['assetFolders', workspace.account.id],
    queryFn: () => listAssetFolders(workspace.account.id),
    enabled: !!workspace.account.id,
  });

  // Load asset files when folder selection changes
  useEffect(() => {
    const loadAssetFiles = async () => {
      if (!formData.selectedFolder || formData.sourceType !== "Asset Library") {
        setAssetFiles([]);
        return;
      }

      try {
        setLoadingAssets(true);
        const files = await listFolderContents(formData.selectedFolder, workspace.account.id);
        setAssetFiles(files);
      } catch (error) {
        console.error('Error loading asset files:', error);
        setAssetFiles([]);
      } finally {
        setLoadingAssets(false);
      }
    };

    loadAssetFiles();
  }, [formData.selectedFolder, workspace.account.id, formData.sourceType]);

  // Convert seconds to minutes for display and check validation
  const lengthValidation = useMemo(() => {
    const seconds = parseInt(formData.length);
    if (isNaN(seconds) || seconds === 0) {
      return { display: "", isValid: false, error: "" };
    }

    if (seconds > 180) {
      return {
        display: `${Math.floor(seconds / 60)}:${(seconds % 60).toString().padStart(2, '0')} minutes`,
        isValid: false,
        error: "Video length cannot exceed 3 minutes (180 seconds)"
      };
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    let display = "";
    if (minutes === 0) {
      display = `${remainingSeconds} seconds`;
    } else if (remainingSeconds === 0) {
      display = `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
    } else {
      display = `${minutes}:${remainingSeconds.toString().padStart(2, '0')} minutes`;
    }

    return { display, isValid: true, error: "" };
  }, [formData.length]);

  const handleTypeChange = useCallback((value: VideoType) => {
    setFormData(prev => ({ ...prev, type: value }));
  }, []);

  const handleLengthChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numeric input
    if (value === "" || /^\d+$/.test(value)) {
      setFormData(prev => ({ ...prev, length: value }));
    }
  }, []);

  const handleSourceTypeChange = useCallback((value: SourceType) => {
    setFormData(prev => ({
      ...prev,
      sourceType: value,
      // Reset other source-related fields when changing source type
      selectedDocument: null,
      selectedFolder: null,
      selectedAsset: null,
      url: ""
    }));
  }, []);

  const handleUrlChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, url: e.target.value }));
  }, []);

  const handleAssetSelect = useCallback((asset: StorageFile) => {
    setFormData(prev => ({ ...prev, selectedAsset: asset }));
  }, []);

  // Get file icon based on file type
  const getFileIcon = (file: StorageFile) => {
    if (!file.type) return <File className="w-4 h-4" />;

    if (file.type.startsWith('video/')) return <Video className="w-4 h-4" />;
    if (file.type.startsWith('image/')) return <Image className="w-4 h-4" />;
    if (file.type.startsWith('audio/')) return <Music className="w-4 h-4" />;
    if (file.type.includes('pdf') || file.type.includes('document')) return <FileText className="w-4 h-4" />;

    return <File className="w-4 h-4" />;
  };

  // Get file size from the file path or name
  const getFileName = (file: StorageFile) => {
    return file.name || file.path.split('/').pop() || 'Unknown file';
  };

  // Breadcrumb component
  const renderBreadcrumb = () => {
    return (
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            {currentStep > 1 ? (
              <BreadcrumbLink
                className="cursor-pointer hover:text-foreground"
                onClick={() => setCurrentStep(1)}
              >
                Setup
              </BreadcrumbLink>
            ) : (
              <BreadcrumbPage className="font-medium">
                Setup
              </BreadcrumbPage>
            )}
          </BreadcrumbItem>

          <BreadcrumbSeparator />

          <BreadcrumbItem>
            {currentStep === 2 ? (
              <BreadcrumbPage className="font-medium">
                Review
              </BreadcrumbPage>
            ) : currentStep > 2 ? (
              <BreadcrumbLink
                className="cursor-pointer hover:text-foreground"
                onClick={() => setCurrentStep(2)}
              >
                Review
              </BreadcrumbLink>
            ) : (
              <span className="text-muted-foreground">Review</span>
            )}
          </BreadcrumbItem>

          <BreadcrumbSeparator />

          <BreadcrumbItem>
            {currentStep === 3 ? (
              <BreadcrumbPage className="font-medium">
                Generate
              </BreadcrumbPage>
            ) : (
              <span className="text-muted-foreground">Generate</span>
            )}
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
    );
  };

  const handleNext = useCallback(() => {
    if (currentStep < 3) {
      setCurrentStep((prev) => (prev + 1) as 1 | 2 | 3);
    } else {
      // Final generate step
      console.log("Generate clicked with data:", {
        type: formData.type,
        length: formData.length,
        sourceType: formData.sourceType,
        selectedFile: formData.selectedDocument || formData.selectedAsset,
        url: formData.url
      });

      // Log specific source data
      if (formData.sourceType === "Product Document" && formData.selectedDocument) {
        console.log("Selected Product Document:", formData.selectedDocument);
      } else if (formData.sourceType === "Asset Library" && formData.selectedAsset) {
        console.log("Selected Asset:", formData.selectedAsset);
      } else if (formData.sourceType === "URL" && formData.url) {
        console.log("Selected URL:", formData.url);
      }
    }
  }, [currentStep, formData]);

  // Check if form is valid for generation
  const isFormValid = useMemo(() => {
    const hasType = !!formData.type;
    const hasValidLength = lengthValidation.isValid && formData.length.trim() !== "";
    const hasValidSource =
      (formData.sourceType === "Product Document" && formData.selectedDocument) ||
      (formData.sourceType === "Asset Library" && formData.selectedAsset) ||
      (formData.sourceType === "URL" && formData.url.trim() !== "");

    return hasType && hasValidLength && hasValidSource;
  }, [formData, lengthValidation.isValid]);

  const renderSourceSelection = () => {
    if (!formData.sourceType) return null;

    switch (formData.sourceType) {
      case "Product Document":
        return (
          <div className="space-y-3">
            <div className="text-sm text-muted-foreground">
              Product Documents will be displayed here
            </div>
            <div className="text-sm">
              <a
                href="#"
                className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline"
                onClick={(e) => {
                  e.preventDefault();
                  // TODO: Navigate to Product Information page
                  console.log("Navigate to Product Information page");
                }}
              >
                Add Product Documents in the Product Information page
              </a>
            </div>
            {/* TODO: Implement Product Documents list */}
          </div>
        );

      case "Asset Library":
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor="folder-select">Folder</Label>
              <Select onValueChange={(value) => setFormData(prev => ({ ...prev, selectedFolder: value, selectedAsset: null }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select folder" />
                </SelectTrigger>
                <SelectContent>
                  {assetFolders.map((folder) => (
                    <SelectItem key={folder.path} value={folder.path}>
                      {folder.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.selectedFolder && (
              <div className="space-y-2">
                <Label>Assets</Label>
                {loadingAssets ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="ml-2 text-sm">Loading assets...</span>
                  </div>
                ) : assetFiles.length === 0 ? (
                  <div className="text-sm text-muted-foreground py-4 text-center">
                    No assets found in this folder
                  </div>
                ) : (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {assetFiles.map((file) => (
                      <button
                        key={file.path}
                        onClick={() => handleAssetSelect(file)}
                        className={`w-full p-2 rounded border text-left transition-colors ${
                          formData.selectedAsset?.path === file.path
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          {getFileIcon(file)}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-sm truncate">
                              {getFileName(file)}
                            </div>
                            {file.type && (
                              <div className="text-xs text-muted-foreground">
                                {file.type}
                              </div>
                            )}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}

            <div className="text-xs text-muted-foreground">
              To upload files, go to the Local tab or Asset Library
            </div>
          </div>
        );

      case "URL":
        return (
          <div className="space-y-3">
            <div>
              <Label htmlFor="url-input">URL</Label>
              <Input
                id="url-input"
                type="url"
                placeholder="https://example.com"
                value={formData.url}
                onChange={handleUrlChange}
                className="mt-1"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex flex-col gap-6 p-4 bg-white dark:bg-gray-900/50 h-full">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Generate Video</h3>

        {renderBreadcrumb()}

        <ScrollArea className="h-[calc(100vh-180px)]">
          <div className="space-y-6 pr-4">
            {/* Type Selection */}
            <div className="space-y-2">
              <Label htmlFor="type-select">Type</Label>
              <Select onValueChange={handleTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select video type" />
                </SelectTrigger>
                <SelectContent>
                  {VIDEO_TYPES.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Length Input */}
            <div className="space-y-2">
              <Label htmlFor="length-input">Length (seconds)</Label>
              <Input
                id="length-input"
                type="text"
                placeholder="e.g., 30"
                value={formData.length}
                onChange={handleLengthChange}
                className={`mt-1 ${
                  lengthValidation.error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
                }`}
              />
              {lengthValidation.display && (
                <div className={`text-xs ${
                  lengthValidation.error ? 'text-red-500' : 'text-muted-foreground'
                }`}>
                  {lengthValidation.display}
                </div>
              )}
              {lengthValidation.error && (
                <div className="text-xs text-red-500 font-medium">
                  {lengthValidation.error}
                </div>
              )}
            </div>

            {/* Source Selection */}
            <div className="space-y-2">
              <Label htmlFor="source-select">Source</Label>
              <Select onValueChange={handleSourceTypeChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select source type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Product Document">Product Document</SelectItem>
                  <SelectItem value="Asset Library">Asset Library</SelectItem>
                  <SelectItem value="URL">URL</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Dynamic Source Content */}
            {renderSourceSelection()}

            {/* Next/Generate Button */}
            <div className="pt-4 flex gap-3">
              {currentStep > 1 && (
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep((prev) => (prev - 1) as 1 | 2 | 3)}
                  className="flex-1"
                  size="lg"
                >
                  Back
                </Button>
              )}
              <Button
                onClick={handleNext}
                disabled={!isFormValid}
                className="flex-1"
                size="lg"
              >
                {currentStep === 3 ? 'Generate' : 'Next'}
              </Button>
            </div>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};