import { Separator } from "@/components/ui/separator";
import dynamic from "next/dynamic";
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { useZero } from '~/hooks/use-zero';
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import RenderControls from "../rendering/render-controls";
import { useEditorContext } from "../../contexts/editor-context";
import { VideoProjectSelector } from "@/app/home/<USER>/video-studio/_components/video-project-selector";
import { useMemo } from "react";

/**
 * Dynamic import of the ThemeToggle component to enable client-side rendering only.
 * This prevents hydration mismatches since theme detection requires browser APIs.
 */
const ThemeToggleClient = dynamic(
  () =>
    import("@/components/theme-toggle")
      .then((mod) => mod.ThemeToggle)
      .catch((err) => {
        console.error("Error loading ThemeToggle:", err);
        return () => null; // Fallback component
      }),
  {
    ssr: false,
    loading: () => <></>, // Optional loading state
  }
);

/**
 * EditorHeader component renders the top navigation bar of the editor interface.
 *
 * @component
 * @description
 * This component provides the main navigation and control elements at the top of the editor:
 * - A theme toggle switch for light/dark mode
 * - Rendering controls for media export
 *
 * The header is sticky-positioned at the top of the viewport and includes
 * responsive styling for both light and dark themes.
 *
 * @example
 * ```tsx
 * <EditorHeader />
 * ```
 *
 * @returns {JSX.Element} A header element containing navigation and control components
 */
export function EditorHeader() {
  /**
   * Destructure required values from the editor context:
   * - renderMedia: Function to handle media rendering/export
   * - state: Current editor state
   * - renderType: Type of render
   */
  const { renderMedia, state, saveProject, renderType } = useEditorContext();
  const workspace = useTeamAccountWorkspace();
  const zero = useZero();

  // Get all projects for this account, sorted by most recent
  const [allProjects, allProjectsResult] = useZeroQuery(
    zero?.query.video_projects
      .where('account_id', '=', workspace.account.id)
      .orderBy("updated_at", "desc"),
    {
      ttl: '5m'
    }
  );
  // Get user cache to check for selected video project
  const [userCacheResults] = useZeroQuery(
    zero?.query.user_cache.where('user_id', '=', workspace.user?.id || ''),
    {
      ttl: '1d'
    }
  );

  const userCache = userCacheResults?.[0];

  // Determine current project ID based on user_cache and available projects
  const currentProjectId = useMemo(() => {
    // If still loading, return null
    if (!zero || !workspace.user?.id || allProjectsResult.type !== 'complete') {
      return null;
    }

    // If user has no projects, create first project
    if (!allProjects || allProjects.length === 0) {
      const projectId = crypto.randomUUID();
      
      // Create the first project asynchronously using custom mutator
      (zero.mutate as any).video_projects.insert({
        id: projectId,
        account_id: workspace.account.id,
        user_id: workspace.user.id,
        name: 'My First Video Project',
        description: 'Welcome to the video studio! Start creating amazing videos.',
        aspect_ratio: '16:9',
        fps: 30,
        width: 1920,
        height: 1080,
        status: 'draft',
        is_template: false,
        created_by: workspace.user.id,
        updated_by: workspace.user.id,
      }).then(() => {
        // Reset overlays to default when creating a new project
        // This will be handled by the react-video-editor component
        console.log('Created new project:', projectId);
      }).catch((error: any) => {
        console.error('Failed to create first project:', error);
      });

      return projectId;
    }

    // Check if user has a selected project in cache and it exists in current projects
    if (userCache?.selected_video_project) {
      const cachedProject = allProjects.find(p => p.id === userCache.selected_video_project);
      if (cachedProject) {
        return userCache.selected_video_project;
      }
    }

    // Fallback to most recent project
    return allProjects[0]?.id || null;
  }, [zero, workspace.user?.id, workspace.account.id, allProjects, allProjectsResult.type, userCache?.selected_video_project]);

  const handleProjectChange = async (projectId: string) => {
    // Update user cache with selected project
    if (zero && workspace.user?.id) {
      try {
        // First try to update if user_cache exists
        if (userCache?.user_id) {
          await (zero.mutate as any).user_cache.update({
            user_id: workspace.user.id,
            values: {
              selected_video_project: projectId
            }
          });
        } else {
          // If no user_cache exists, create it
          await (zero.mutate as any).user_cache.upsert({
            user_id: workspace.user.id,
            values: {
              selected_video_project: projectId
            }
          });
        }
      } catch (error) {
        console.error('Failed to update user cache:', error);
      }
    }
  };
  return (
    <header
      className="sticky top-0 flex shrink-0 items-center gap-2.5 
      bg-white dark:bg-gray-900/10
      border-l 
      border-b border-gray-200 dark:border-gray-800
      p-2.5 px-4.5"
    >
      {/* Video Editor header - no sidebar trigger needed since video editor manages its own sidebar */}

      {/* Theme toggle component (client-side only) */}
      {/* <ThemeToggleClient /> */}
      <VideoProjectSelector
        currentProjectId={currentProjectId}
        onProjectChange={handleProjectChange}
      />

      {/* Spacer to push rendering controls to the right */}
      <div className="flex-grow" />

      {/* Media rendering controls */}
      <RenderControls
        handleRender={renderMedia}
        state={state}
        saveProject={saveProject}
        renderType={renderType}
      />
    </header>
  );
}
