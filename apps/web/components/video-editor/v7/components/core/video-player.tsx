import React, { useEffect } from "react";
import { Player, PlayerRef } from "@remotion/player";
import { Main } from "../../remotion/main";
import { useEditorContext } from "../../contexts/editor-context";
import { FPS } from "../../constants";

/**
 * Props for the VideoPlayer component
 * @interface VideoPlayerProps
 * @property {React.RefObject<PlayerRef>} playerRef - Reference to the Remotion player instance
 */
interface VideoPlayerProps {
  playerRef: React.RefObject<PlayerRef>;
}

/**
 * VideoPlayer component that renders a responsive video editor with overlay support
 * The player automatically resizes based on its container and maintains the specified aspect ratio
 */
export const VideoPlayer: React.FC<VideoPlayerProps> = ({ playerRef }) => {
  const {
    overlays,
    setSelectedOverlayId,
    changeOverlay,
    selectedOverlayId,
    aspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    durationInFrames,
  } = useEditorContext();

  /**
   * Updates the player dimensions when the container size or aspect ratio changes
   * Also re-calculates dimensions when overlays change to ensure player remains visible
   */
  useEffect(() => {
    const handleDimensionUpdate = () => {
      const videoContainer = document.querySelector(".video-container");
      if (!videoContainer) return;

      const { width, height } = videoContainer.getBoundingClientRect();
      
      // Ensure we have valid dimensions before updating
      if (width > 0 && height > 0) {
        updatePlayerDimensions(width, height);
      }
    };

    // Use requestAnimationFrame to ensure DOM is ready
    const updateWithDelay = () => {
      requestAnimationFrame(() => {
        handleDimensionUpdate();
      });
    };

    updateWithDelay(); // Initial update
    window.addEventListener("resize", handleDimensionUpdate);

    return () => {
      window.removeEventListener("resize", handleDimensionUpdate);
    };
  }, [aspectRatio, updatePlayerDimensions, overlays.length]); // Add overlays.length to dependencies

  const { width: compositionWidth, height: compositionHeight } =
    getAspectRatioDimensions();

  // Ensure we have valid player dimensions - fallback to composition dimensions if needed
  const validPlayerDimensions = {
    width: playerDimensions.width > 0 ? playerDimensions.width : compositionWidth,
    height: playerDimensions.height > 0 ? playerDimensions.height : compositionHeight,
  };

  // Constants for player configuration
  const PLAYER_CONFIG = {
    durationInFrames: Math.round(durationInFrames),
    fps: FPS,
  };

  return (
    <div className="w-full h-full overflow-hidden">
      {/* Video container */}
      <div
        className="z-0 video-container relative w-full h-full shadow-lg"
      >
        {/* Player wrapper with centering */}
        <div className="z-10 absolute inset-2 sm:inset-4 flex items-center justify-center">
          <div
            className="relative mx-2 sm:mx-0"
            style={{
              width: Math.min(validPlayerDimensions.width, compositionWidth),
              height: Math.min(validPlayerDimensions.height, compositionHeight),
              maxWidth: "100%",
              maxHeight: "100%",
              minWidth: "200px", // Ensure minimum visible size
              minHeight: "113px", // Maintain aspect ratio (200 * 9/16 ≈ 113)
            }}
          >
            <Player
              ref={playerRef}
              className="w-full h-full"
              component={Main}
              compositionWidth={compositionWidth}
              compositionHeight={compositionHeight}
              style={{
                width: "100%",
                height: "100%",
              }}
              durationInFrames={PLAYER_CONFIG.durationInFrames}
              fps={PLAYER_CONFIG.fps}
              inputProps={{
                overlays,
                setSelectedOverlayId,
                changeOverlay,
                selectedOverlayId,
                durationInFrames,
                fps: FPS,
                width: compositionWidth,
                height: compositionHeight,
              }}
              errorFallback={() => <></>}
              overflowVisible
            />
          </div>
        </div>
      </div>
    </div>
  );
};
