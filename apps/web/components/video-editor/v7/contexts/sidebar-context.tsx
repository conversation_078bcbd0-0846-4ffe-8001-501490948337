import React, { createContext, useContext, useState } from "react";
import { OverlayType } from "../types";

// Define the shape of our context data
type SidebarContextType = {
  activePanel: OverlayType; // Stores the currently active panel name
  setActivePanel: (panel: OverlayType) => void; // Function to update the active panel
  setIsOpen: (open: boolean) => void;
  isOpen: boolean;
};

// Create the context with undefined as initial value
const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

// Custom hook to consume the sidebar context
export const useSidebar = () => {
  const context = useContext(SidebarContext);

  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }

  return context;
};

// Provider component that wraps parts of the app that need access to sidebar state
export const SidebarProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [activePanel, setActivePanel] = useState<OverlayType>(OverlayType.CHAT);
  const [isOpen, setIsOpen] = useState(true);

  const value = {
    activePanel,
    setActivePanel,
    setIsOpen,
    isOpen,
  };

  return (
    <SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>
  );
};
