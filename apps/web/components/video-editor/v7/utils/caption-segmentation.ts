/**
 * Smart caption segmentation utility
 * Breaks long text into optimal caption segments
 */

export interface WordWithTiming {
  word: string;
  startMs: number;
  endMs: number;
  confidence: number;
}

export interface CaptionSegment {
  text: string;
  startMs: number;
  endMs: number;
  words: WordWithTiming[];
  confidence: number;
}

/**
 * Configuration for caption segmentation
 */
export interface SegmentationConfig {
  maxWordsPerCaption?: number;      // Default: 8
  maxCharactersPerCaption?: number; // Default: 60
  maxDurationMs?: number;          // Default: 4000ms (4 seconds)
  preferSentenceBreaks?: boolean;   // Default: true
  pauseThresholdMs?: number;       // Default: 500ms
}

const DEFAULT_CONFIG: Required<SegmentationConfig> = {
  maxWordsPerCaption: 8,
  maxCharactersPerCaption: 60,
  maxDurationMs: 4000,
  preferSentenceBreaks: true,
  pauseThresholdMs: 500,
};

/**
 * Intelligently segments words into optimal caption chunks
 * Considers word count, character count, duration, natural breaks, and pauses
 */
export function segmentWordsIntoCaptions(
  words: WordWithTiming[],
  config: SegmentationConfig = {}
): CaptionSegment[] {
  const conf = { ...DEFAULT_CONFIG, ...config };
  const segments: CaptionSegment[] = [];
  let currentSegment: WordWithTiming[] = [];

  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    const nextWord = words[i + 1];

    currentSegment.push(word);

    // Calculate current segment stats
    const currentText = currentSegment.map(w => w.word).join(' ');
    const currentDuration = word.endMs - currentSegment[0].startMs;
    const pauseAfterWord = nextWord ? nextWord.startMs - word.endMs : 0;

    // Check if we should break the segment
    const shouldBreak =
      // Hard limits
      currentSegment.length >= conf.maxWordsPerCaption ||
      currentText.length >= conf.maxCharactersPerCaption ||
      currentDuration >= conf.maxDurationMs ||

      // Natural breaks (if enabled)
      (conf.preferSentenceBreaks && isSentenceEnd(word.word)) ||

      // Pause detection
      pauseAfterWord >= conf.pauseThresholdMs ||

      // End of words
      i === words.length - 1;

    if (shouldBreak && currentSegment.length > 0) {
      segments.push(createSegmentFromWords(currentSegment));
      currentSegment = [];
    }
  }

  return segments;
}

/**
 * Creates a caption segment from an array of words
 */
function createSegmentFromWords(words: WordWithTiming[]): CaptionSegment {
  if (words.length === 0) {
    throw new Error('Cannot create segment from empty words array');
  }

  const text = words.map(w => w.word).join(' ');
  const startMs = words[0].startMs;
  const endMs = words[words.length - 1].endMs;
  const confidence = words.reduce((sum, w) => sum + w.confidence, 0) / words.length;

  return {
    text,
    startMs,
    endMs,
    words: [...words],
    confidence,
  };
}

/**
 * Checks if a word ends a sentence
 */
function isSentenceEnd(word: string): boolean {
  return /[.!?]$/.test(word.trim());
}

/**
 * Segments text-only transcript into sentences with smart breaking
 */
export function segmentTextIntoSentences(
  text: string,
  config: SegmentationConfig = {}
): string[] {
  const conf = { ...DEFAULT_CONFIG, ...config };

  // First split on obvious sentence endings
  let sentences = text.split(/(?<=[.!?])\s+/);

  // Then check if any sentences are too long and break them
  const finalSentences: string[] = [];

  for (const sentence of sentences) {
    if (sentence.split(' ').length <= conf.maxWordsPerCaption &&
        sentence.length <= conf.maxCharactersPerCaption) {
      finalSentences.push(sentence.trim());
    } else {
      // Break long sentence into smaller chunks at natural break points
      const chunks = breakLongSentence(sentence, conf);
      finalSentences.push(...chunks);
    }
  }

  return finalSentences.filter(s => s.length > 0);
}

/**
 * Breaks a long sentence into smaller chunks
 */
function breakLongSentence(
  sentence: string,
  config: Required<SegmentationConfig>
): string[] {
  const words = sentence.split(' ');
  const chunks: string[] = [];
  let currentChunk: string[] = [];

  for (const word of words) {
    currentChunk.push(word);

    const currentText = currentChunk.join(' ');

    // Break at natural pause points or limits
    if (
      currentChunk.length >= config.maxWordsPerCaption ||
      currentText.length >= config.maxCharactersPerCaption ||
      isNaturalBreakPoint(word)
    ) {
      chunks.push(currentText);
      currentChunk = [];
    }
  }

  // Add remaining words
  if (currentChunk.length > 0) {
    chunks.push(currentChunk.join(' '));
  }

  return chunks;
}

/**
 * Checks if a word is a good place to break a sentence
 */
function isNaturalBreakPoint(word: string): boolean {
  // Break after conjunctions, prepositions, etc.
  const breakWords = ['and', 'but', 'or', 'so', 'because', 'after', 'before', 'when', 'while', 'if'];
  return breakWords.includes(word.toLowerCase());
}