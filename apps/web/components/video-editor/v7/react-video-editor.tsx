"use client";

// UI Components
import { SimpleSidebar } from "./components/sidebar/simple-sidebar";
import { Editor } from "./components/core/editor";
import { SidebarProvider as EditorSidebarProvider } from "./contexts/sidebar-context";

// Context Providers
import { EditorProvider } from "./contexts/editor-context";

// Custom Hooks
import { useOverlays } from "./hooks/use-overlays";
import { useVideoPlayer } from "./hooks/use-video-player";
import { useTimelineClick } from "./hooks/use-timeline-click";
import { useAspectRatio } from "./hooks/use-aspect-ratio";
import { useCompositionDuration } from "./hooks/use-composition-duration";
import { useHistory } from "./hooks/use-history";

// Types
import { Overlay } from "./types";
import { useRendering } from "./hooks/use-rendering";
import {
  AUTO_SAVE_INTERVAL,
  DEFAULT_OVERLAYS,
  FPS,
  RENDER_TYPE,
} from "./constants";
import { TimelineProvider } from "./contexts/timeline-context";

// Autosave Components
import { AutosaveStatus } from "./components/autosave/autosave-status";
import { useState, useEffect, useRef } from "react";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { useTeamAccountWorkspace } from '@kit/team-accounts/hooks/use-team-account-workspace';
import { LocalMediaProvider } from "./contexts/local-media-context";
import { KeyframeProvider } from "./contexts/keyframe-context";
import { AssetLoadingProvider } from "./contexts/asset-loading-context";

export default function ReactVideoEditor({ projectId }: { projectId: string }) {

  // Overlay management hooks
  const {
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    deleteOverlaysByRow,
    updateOverlayStyles,
    resetOverlays,
  } = useOverlays(DEFAULT_OVERLAYS);

    // Video player controls and state
    const { isPlaying, currentFrame, playerRef, togglePlayPause, formatTime } =
    useVideoPlayer();

  // Composition duration calculations
  const { durationInFrames, durationInSeconds } =
    useCompositionDuration(overlays);

  // Aspect ratio and player dimension fmanagement
  const {
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
  } = useAspectRatio();

  const { width: compositionWidth, height: compositionHeight } =
  getAspectRatioDimensions();

  const inputProps = {
    overlays,
    durationInFrames,
    fps: FPS,
    width: compositionWidth,
    height: compositionHeight,
    src: "",
  };

  const { renderMedia, state } = useRendering(
    "TestComponent",
    inputProps,
    RENDER_TYPE
  );

  // Replace history management code with hook
  const { undo, redo, canUndo, canRedo } = useHistory(overlays, setOverlays);

  // Create the editor state object to be saved
  const editorState = {
    overlays,
    aspectRatio,
    playerDimensions,
  };
  
  const zero = useZero();
  const workspace = useTeamAccountWorkspace();
  const [showRecoveryDialog, setShowRecoveryDialog] = useState(false);
  const [autosaveTimestamp, setAutosaveTimestamp] = useState<number | null>(
    null
  );
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState<number | null>(null);
  const [playbackRate, setPlaybackRate] = useState(1);


  // Get user cache to track project and scene changes
  const [userCacheResults] = useZeroQuery(
    zero?.query.user_cache.where('user_id', '=', workspace.user?.id || ''),
    {
      ttl: '1d'
    }
  );
  const userCache = userCacheResults?.[0];
  const selectedSceneId = userCache?.selected_scene_id;

    // Load scenes for the project
    const [projectScenes, projectScenesResult] = useZeroQuery(
      zero?.query.video_project_scenes
        .where("project_id",'=', projectId)
        .orderBy("order_index", "asc") as any
    ) as [any[], any];

    const [sceneAutosaves] = useZeroQuery(
      zero?.query.video_project_autosaves
        .where("scene_id", selectedSceneId || "")
        .orderBy("created_at", "desc")
        .limit(1) as any,
      {
        ttl: '5m'
      }
    ) as [any[], any];


  // Event handlers
  const handleOverlayChange = (updatedOverlay: Overlay) => {
    changeOverlay(updatedOverlay.id, () => updatedOverlay);
  };

  const handleTimelineClick = useTimelineClick(playerRef as any, durationInFrames);

    // Ensure new projects have at least one default scene
    useEffect(() => {
      console.log("projectScenesResult", projectScenesResult);
      console.log("projectScenes", projectScenes);
      console.log("workspace.user?.id", workspace.user?.id);
      console.log("projectId", projectId);
      if (projectScenes && projectScenes.length === 0 ) {      
        const createDefaultScene = async () => {
          try {
            const defaultSceneId = crypto.randomUUID();
        
            await (zero.mutate.video_project_scenes as any).insert({
              id: defaultSceneId,
              project_id: projectId,
              user_id: workspace.user.id,
              name: "Scene 1",
              description: "",
              order_index: 1,
              duration_frames: 300,
              aspect_ratio: "16:9",
              // created_at: Date.now(),
              // updated_at: Date.now(),
            });

            // Update user cache to select this scene
            (zero.mutate.user_cache as any).update({
              user_id: workspace.user.id,
              values: {
                selected_scene_id: defaultSceneId,
              }
            });
  
          } catch (error) {
            console.error("Failed to create default scene:", error);
          }
        };
        
        createDefaultScene();
      }
    }, [projectId]);

  // 1. Handle project changes - ensure we have a valid scene selected for the project
  useEffect(() => {
    
    if (!projectId || !projectScenes || !zero || !workspace.user?.id) return;
    
    const handleProjectChange = async () => {
      // If no scenes exist for this project, the existing initialization effect will handle it
      if (projectScenes.length === 0) {

        setOverlays(DEFAULT_OVERLAYS);
        setAspectRatio("16:9");
        updatePlayerDimensions(1280, 720);
        setSelectedOverlayId(null);
        return;
      }
      
      // If we have a selected scene but it doesn't belong to this project, switch to first scene
      if (selectedSceneId && !projectScenes.find(scene => scene.id === selectedSceneId)) {
        const firstScene = projectScenes[0];
        
        try {
          await (zero.mutate.user_cache as any).update({
            user_id: workspace.user.id,
            values: {
              selected_scene_id: firstScene.id,
            }
          });
          // setScenes(projectScenes);
        } catch (error) {
          console.error("Failed to update selected scene for project:", error);
        }
      }
      
      // If no scene is selected at all, select the first one
      if (!selectedSceneId && projectScenes.length > 0) {
        const firstScene = projectScenes[0];
        
        try {
          await (zero.mutate.user_cache as any).update({
            user_id: workspace.user.id,
            values: {
              selected_scene_id: firstScene.id,
            }
          });
        } catch (error) {
          console.error("Failed to select first scene:", error);
        }
      }
    };
    
    handleProjectChange();
  }, [projectId, projectScenes, selectedSceneId, zero, workspace.user?.id]);

  // 2. Handle scene changes - load the scene's overlay data
  useEffect(() => {
    
    if (!selectedSceneId || !projectScenes?.length) return;
    
    const loadSceneData = () => {
      // Find the current scene in the project
      const currentScene = projectScenes.find(scene => scene.id === selectedSceneId);
      
      if (!currentScene) {
        return;
      }
      
      
      // Load from autosave if available, otherwise use scene defaults
      const latestAutosave = sceneAutosaves?.[0];
      
      if (latestAutosave?.editor_state) {
        
        // Load from autosave
        setOverlays(latestAutosave.editor_state.overlays || DEFAULT_OVERLAYS);
        setAspectRatio(latestAutosave.editor_state.aspectRatio || "16:9");
        
        // Load saved dimensions or use defaults
        const savedDimensions = latestAutosave.editor_state.playerDimensions;
        const width = savedDimensions?.width && savedDimensions.width > 0 ? savedDimensions.width : 1280;
        const height = savedDimensions?.height && savedDimensions.height > 0 ? savedDimensions.height : 720;
        updatePlayerDimensions(width, height);
      } else {
        
        // Load from scene defaults
        setOverlays(currentScene.overlays || DEFAULT_OVERLAYS);
        setAspectRatio(currentScene.aspect_ratio || "16:9");
        
        // Use scene dimensions or defaults
        const width = currentScene.width && currentScene.width > 0 ? currentScene.width : 1280;
        const height = currentScene.height && currentScene.height > 0 ? currentScene.height : 720;
        updatePlayerDimensions(width, height);
      }
    };
    
    loadSceneData();
  }, [selectedSceneId,projectId, sceneAutosaves, projectScenes]);



  // Handle scene changes and autosave loading
  useEffect(() => {
    if(selectedSceneId) {
      //check if the scene is in the projectScenes array, if not, set the first scene as the selected scene
      let sceneToSet;
      if(!projectScenes.find(scene => scene.id === selectedSceneId)) {
        if(!projectScenes || projectScenes.length === 0) {
        } else {
          sceneToSet = projectScenes[0].id;
          (zero.mutate.user_cache as any).update({
            user_id: workspace.user.id,
            values: {
              selected_scene_id: sceneToSet,
            }
          });
          // set the existing overlays or default in not there
          const targetScene = projectScenes.find(scene => scene.id === sceneToSet);
          setOverlays(targetScene?.overlays || DEFAULT_OVERLAYS);
          setAspectRatio(targetScene?.aspect_ratio || "16:9");
          
          // Ensure valid dimensions are used
          const width = targetScene?.width && targetScene.width > 0 ? targetScene.width : 1280;
          const height = targetScene?.height && targetScene.height > 0 ? targetScene.height : 720;
          updatePlayerDimensions(width, height);
        }
      } else {
        const latestAutosave = sceneAutosaves[0];
        setOverlays(latestAutosave?.editor_state?.overlays || DEFAULT_OVERLAYS);
        setAspectRatio(latestAutosave?.editor_state?.aspectRatio || "16:9");
        
        // Ensure valid dimensions from autosave or use defaults
        const savedDimensions = latestAutosave?.editor_state?.playerDimensions;
        const width = savedDimensions?.width && savedDimensions.width > 0 ? savedDimensions.width : 1280;
        const height = savedDimensions?.height && savedDimensions.height > 0 ? savedDimensions.height : 720;
        updatePlayerDimensions(width, height);
      }
    }
  
  }, [ selectedSceneId, sceneAutosaves, projectId]);

  // handle project changes
  useEffect(() => {
    if(projectId) {
      
    }
  }, [projectId]);
  // Auto-save using Zero
  useEffect(() => {
    if (!zero || !projectId || !workspace.user?.id) return;

    const saveInterval = setInterval(async () => {
      try {
        setIsSaving(true);

        // Save autosave using Zero mutator
        await (zero.mutate.video_project_autosaves as any).insert({
          id: crypto.randomUUID(),
          project_id: projectId,
          user_id: workspace.user.id,
          editor_state: editorState,
          save_type: 'auto',
          created_at: Date.now(),
          scene_id: selectedSceneId,
        });

        // Update project's updated_at timestamp
        await (zero.mutate.video_projects as any).update({
          id: projectId,
          updated_at: Date.now(),
          updated_by: workspace.user.id,
        });

        setLastSaveTime(Date.now());
      } catch (error) {
        console.error("Autosave failed:", error);
      } finally {
        setIsSaving(false);
      }
    }, AUTO_SAVE_INTERVAL);

    return () => clearInterval(saveInterval);
  }, [zero, projectId, workspace.user?.id, editorState]);

  // Functions for manual save/load
  const saveState = async () => {
    if (!zero || !workspace.user?.id) return false;

    try {
      setIsSaving(true);

      // Save autosave
      await (zero.mutate.video_project_autosaves as any).insert({
        id: crypto.randomUUID(),
        project_id: projectId,
        user_id: workspace.user.id,
        editor_state: editorState,
        save_type: 'manual',
        created_at: Date.now(),
        scene_id: selectedSceneId,
      });

      // Update project timestamp
      await (zero.mutate.video_projects as any).update({
        id: projectId,
        updated_at: Date.now(),
        updated_by: workspace.user.id,
      });

      setLastSaveTime(Date.now());
      return true;
    } catch (error) {
      console.error("Manual save failed:", error);
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const loadState = async () => {
    if (!sceneAutosaves || sceneAutosaves.length === 0) return null;

    const latestAutosave = sceneAutosaves[0];
    const loadedState = latestAutosave.editor_state;
    if (loadedState) {
      // Apply loaded state to editor
      setOverlays(loadedState.overlays || []);
      if (loadedState.aspectRatio) setAspectRatio(loadedState.aspectRatio);
      if (loadedState.playerDimensions)
        updatePlayerDimensions(
          loadedState.playerDimensions.width,
          loadedState.playerDimensions.height
        );
    }

    return loadedState;
  };

  // Handle recovery dialog actions
  const handleRecoverAutosave = async () => {
    const loadedState = await loadState();

    
    // Force a re-render to ensure overlays are applied
    if (loadedState?.overlays) {
      setOverlays([...loadedState.overlays]);
    }
    
    setShowRecoveryDialog(false);
  };

  // Manual save function for use in keyboard shortcuts or save button
  const handleManualSave = async () => {
    setIsSaving(true);
    await saveState();
  };

  // Set up keyboard shortcut for manual save (Ctrl+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === "s") {
        e.preventDefault();
        handleManualSave();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [editorState]);

  // Combine all editor context values
  const editorContextValue = {
    // Overlay management
    overlays,
    setOverlays,
    selectedOverlayId,
    setSelectedOverlayId,
    changeOverlay,
    handleOverlayChange,
    addOverlay,
    deleteOverlay,
    duplicateOverlay,
    splitOverlay,
    resetOverlays,

    // Player controls
    isPlaying,
    currentFrame,
    playerRef,
    togglePlayPause,
    formatTime,
    handleTimelineClick,
    playbackRate,
    setPlaybackRate,

    // Dimensions and duration
    aspectRatio,
    setAspectRatio,
    playerDimensions,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    durationInFrames,
    durationInSeconds,

    // Add renderType to the context
    renderType: RENDER_TYPE,
    renderMedia,
    state,

    deleteOverlaysByRow,

    // History management
    undo,
    redo,
    canUndo,
    canRedo,

    // New style management
    updateOverlayStyles,

    // Autosave
    saveProject: handleManualSave,
  };

  return (
    <EditorSidebarProvider>
      <KeyframeProvider>
        <TimelineProvider>
          <EditorProvider value={editorContextValue}>
            <LocalMediaProvider>
              <AssetLoadingProvider>
                {/* Simple flex layout like Image Studio - no conflicting providers */}
                <div className="flex h-full w-full">
                  {/* Video Editor Tools Sidebar */}
                  <div className="w-[350px] shrink-0 border-r bg-background">
                    <SimpleSidebar />
                  </div>

                  {/* Main Editor Content */}
                  <div className="flex-1">
                    <Editor 
                      projectId={projectId} 
                    />
                  </div>
                </div>

                {/* Autosave Status Indicator */}
                <AutosaveStatus
                  isSaving={isSaving}
                  lastSaveTime={lastSaveTime}
                />

              </AssetLoadingProvider>
            </LocalMediaProvider>
          </EditorProvider>
        </TimelineProvider>
      </KeyframeProvider>
    </EditorSidebarProvider>
  );
}
