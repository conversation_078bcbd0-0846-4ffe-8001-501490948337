{"examples": [{"command": "Add text 'Hello World' at the top", "expectedOutput": {"overlays": [{"id": 87531, "type": "text", "content": "Hello World", "left": 100, "top": 200, "width": 880, "height": 150, "row": 0, "from": 0, "durationInFrames": 90, "rotation": 0, "isDragging": false, "styles": {"fontSize": "3rem", "fontWeight": "700", "color": "#FFFFFF", "backgroundColor": "", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0em", "textAlign": "center", "textTransform": "none", "textShadow": "2px 2px 0px rgba(0, 0, 0, 0.2)", "opacity": 1, "zIndex": 1, "transform": "none"}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Create bold red text saying 'SALE 50% OFF' that pops in", "expectedOutput": {"overlays": [{"id": 64829, "type": "text", "content": "SALE 50% OFF", "left": 90, "top": 450, "width": 900, "height": 200, "row": 0, "from": 15, "durationInFrames": 120, "rotation": 0, "isDragging": false, "styles": {"fontSize": "4rem", "fontWeight": "900", "color": "#FF0000", "backgroundColor": "", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0.02em", "textAlign": "center", "textTransform": "uppercase", "textShadow": "3px 3px 0px rgba(0, 0, 0, 0.3)", "opacity": 1, "zIndex": 1, "transform": "none", "animation": {"enter": "scale"}}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Add a person cooking video with the title 'Easy Recipe' at the bottom", "expectedOutput": {"overlays": [{"id": 52847, "type": "video", "src": "", "content": "VIDEO_DESCRIPTION: [person cooking in kitchen, preparing ingredients]", "left": 0, "top": 0, "width": 1080, "height": 1920, "row": 5, "from": 0, "durationInFrames": 180, "rotation": 0, "isDragging": false, "videoStartTime": 0, "styles": {"opacity": 1, "zIndex": 100, "objectFit": "cover", "transform": "none"}}, {"id": 73925, "type": "text", "content": "Easy Recipe", "left": 140, "top": 1400, "width": 800, "height": 150, "row": 0, "from": 30, "durationInFrames": 120, "rotation": 0, "isDragging": false, "styles": {"fontSize": "3.5rem", "fontWeight": "800", "color": "#FFFFFF", "backgroundColor": "", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0em", "textAlign": "center", "textTransform": "none", "textShadow": "2px 2px 8px rgba(0, 0, 0, 0.8)", "opacity": 1, "zIndex": 1, "transform": "none", "animation": {"enter": "slideUp"}}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Create 'ARE YOU READY?' text that appears word by word with snap animation", "expectedOutput": {"overlays": [{"id": 19263, "type": "text", "content": "ARE", "left": 120, "top": 800, "width": 250, "height": 120, "row": 0, "from": 60, "durationInFrames": 90, "rotation": 0, "isDragging": false, "styles": {"fontSize": "3rem", "fontWeight": "900", "color": "#FFFFFF", "backgroundColor": "", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0.02em", "textAlign": "center", "textTransform": "uppercase", "textShadow": "2px 2px 0px rgba(0, 0, 0, 0.2)", "opacity": 1, "zIndex": 1, "transform": "none", "animation": {"enter": "snapRotate"}}}, {"id": 85471, "type": "text", "content": "YOU", "left": 415, "top": 800, "width": 250, "height": 120, "row": 1, "from": 75, "durationInFrames": 90, "rotation": 0, "isDragging": false, "styles": {"fontSize": "3rem", "fontWeight": "900", "color": "#FFFFFF", "backgroundColor": "", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0.02em", "textAlign": "center", "textTransform": "uppercase", "textShadow": "2px 2px 0px rgba(0, 0, 0, 0.2)", "opacity": 1, "zIndex": 1, "transform": "none", "animation": {"enter": "snapRotate"}}}, {"id": 67394, "type": "text", "content": "READY?", "left": 710, "top": 800, "width": 250, "height": 120, "row": 2, "from": 90, "durationInFrames": 90, "rotation": 0, "isDragging": false, "styles": {"fontSize": "3rem", "fontWeight": "900", "color": "#FFFFFF", "backgroundColor": "", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0.02em", "textAlign": "center", "textTransform": "uppercase", "textShadow": "2px 2px 0px rgba(0, 0, 0, 0.2)", "opacity": 1, "zIndex": 1, "transform": "none", "animation": {"enter": "snapRotate"}}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Add a blue circle shape in the center", "expectedOutput": {"overlays": [{"id": 42871, "type": "shape", "content": "circle", "left": 440, "top": 860, "width": 200, "height": 200, "row": 2, "from": 0, "durationInFrames": 90, "rotation": 0, "isDragging": false, "styles": {"fill": "#0000FF", "stroke": "", "strokeWidth": 0, "borderRadius": "50%", "opacity": 1, "zIndex": 2, "transform": "none"}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Create a tutorial video with step numbers 1, 2, 3 appearing in sequence", "expectedOutput": {"overlays": [{"id": 72539, "type": "video", "src": "", "content": "VIDEO_DESCRIPTION: [tutorial demonstration, instructional content]", "left": 0, "top": 0, "width": 1080, "height": 1920, "row": 6, "from": 0, "durationInFrames": 270, "rotation": 0, "isDragging": false, "videoStartTime": 0, "styles": {"opacity": 1, "zIndex": 100, "objectFit": "cover", "transform": "none"}}, {"id": 15847, "type": "text", "content": "1", "left": 50, "top": 300, "width": 100, "height": 100, "row": 0, "from": 30, "durationInFrames": 60, "rotation": 0, "isDragging": false, "styles": {"fontSize": "4rem", "fontWeight": "900", "color": "#FFFFFF", "backgroundColor": "#FF6B35", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0em", "textAlign": "center", "textTransform": "none", "borderRadius": "50%", "opacity": 1, "zIndex": 5, "transform": "none", "animation": {"enter": "bounce"}}}, {"id": 93652, "type": "text", "content": "2", "left": 50, "top": 300, "width": 100, "height": 100, "row": 1, "from": 120, "durationInFrames": 60, "rotation": 0, "isDragging": false, "styles": {"fontSize": "4rem", "fontWeight": "900", "color": "#FFFFFF", "backgroundColor": "#4ECDC4", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0em", "textAlign": "center", "textTransform": "none", "borderRadius": "50%", "opacity": 1, "zIndex": 5, "transform": "none", "animation": {"enter": "bounce"}}}, {"id": 68274, "type": "text", "content": "3", "left": 50, "top": 300, "width": 100, "height": 100, "row": 2, "from": 210, "durationInFrames": 60, "rotation": 0, "isDragging": false, "styles": {"fontSize": "4rem", "fontWeight": "900", "color": "#FFFFFF", "backgroundColor": "#45B7D1", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0em", "textAlign": "center", "textTransform": "none", "borderRadius": "50%", "opacity": 1, "zIndex": 5, "transform": "none", "animation": {"enter": "bounce"}}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Add a car image on the right side that slides in from the right", "expectedOutput": {"overlays": [{"id": 35169, "type": "image", "src": "", "content": "IMAGE_DESCRIPTION: [car, automobile, side view]", "left": 600, "top": 700, "width": 400, "height": 300, "row": 1, "from": 45, "durationInFrames": 120, "rotation": 0, "isDragging": false, "styles": {"opacity": 1, "zIndex": 3, "objectFit": "contain", "transform": "none", "animation": {"enter": "slideRight"}}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Create a workout video with 'GET FIT' text that glitches in", "expectedOutput": {"overlays": [{"id": 84192, "type": "video", "src": "", "content": "VIDEO_DESCRIPTION: [workout exercise, fitness training, gym environment]", "left": 0, "top": 0, "width": 1080, "height": 1920, "row": 5, "from": 0, "durationInFrames": 200, "rotation": 0, "isDragging": false, "videoStartTime": 0, "styles": {"opacity": 1, "zIndex": 100, "objectFit": "cover", "transform": "none"}}, {"id": 27638, "type": "text", "content": "GET FIT", "left": 200, "top": 600, "width": 680, "height": 200, "row": 0, "from": 60, "durationInFrames": 100, "rotation": 0, "isDragging": false, "styles": {"fontSize": "4.5rem", "fontWeight": "900", "color": "#00FF00", "backgroundColor": "", "fontFamily": "font-sans", "fontStyle": "normal", "textDecoration": "none", "lineHeight": "1", "letterSpacing": "0.05em", "textAlign": "center", "textTransform": "uppercase", "textShadow": "0 0 20px #00FF00, 2px 2px 0px rgba(0, 0, 0, 0.8)", "opacity": 1, "zIndex": 1, "transform": "none", "animation": {"enter": "glitch"}}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Add thumbs up emoji sticker that rotates slightly", "expectedOutput": {"overlays": [{"id": 91875, "type": "sticker", "src": "", "content": "STICKER_DESCRIPTION: [thumbs up emoji, positive gesture]", "left": 450, "top": 1200, "width": 150, "height": 150, "row": 0, "from": 30, "durationInFrames": 90, "rotation": 15, "isDragging": false, "styles": {"opacity": 1, "zIndex": 10, "transform": "none", "animation": {"enter": "bounce"}}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Create captions that say 'Welcome to our channel' starting immediately", "expectedOutput": {"overlays": [{"id": 56394, "type": "caption", "left": 140, "top": 1600, "width": 800, "height": 120, "row": 0, "from": 0, "durationInFrames": 120, "rotation": 0, "isDragging": false, "captions": [{"text": "Welcome to our channel", "startMs": 0, "endMs": 4000, "timestampMs": null, "confidence": 0.99, "words": [{"word": "Welcome", "startMs": 0, "endMs": 800, "confidence": 0.99}, {"word": "to", "startMs": 800, "endMs": 1000, "confidence": 0.99}, {"word": "our", "startMs": 1000, "endMs": 1400, "confidence": 0.99}, {"word": "channel", "startMs": 1400, "endMs": 2200, "confidence": 0.99}]}]}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}, {"command": "Add a red rectangle with white border behind the text", "expectedOutput": {"overlays": [{"id": 73512, "type": "shape", "content": "rectangle", "left": 80, "top": 480, "width": 920, "height": 240, "row": 3, "from": 0, "durationInFrames": 90, "rotation": 0, "isDragging": false, "styles": {"fill": "#FF0000", "stroke": "#FFFFFF", "strokeWidth": 4, "borderRadius": "12px", "opacity": 0.9, "zIndex": 0, "transform": "none"}}], "aspectRatio": "9:16", "playerDimensions": {"width": 564.8642578125, "height": 1004.203125}}}], "testCommands": ["Make the text bigger and blue", "Move the video to start at 5 seconds", "Add 'SUBSCRIBE NOW' text that bounces in after 3 seconds", "Create a cooking show intro with chef image and title", "Add countdown numbers 3, 2, 1 that appear every second", "Create a news overlay with breaking news banner", "Add a product showcase with price tag and description", "Create a fitness motivation quote that fades in slowly", "Add a weather forecast with temperature and icon", "Create a gaming overlay with score counter and level indicator", "Add a travel vlog title with location pin icon", "Create a recipe ingredients list that appears item by item", "Add a music visualizer background with song title", "Create a tutorial series intro with episode number", "Add a social media call-to-action with follow button", "Create a product review with rating stars and price", "Add a workout timer with exercise name and reps", "Create a educational content with formula and diagrams", "Add a livestream overlay with viewer count and chat", "Create a promotional banner with discount percentage"], "validationRules": {"requiredFields": ["id", "type", "left", "top", "width", "height", "row", "from", "durationInFrames", "rotation", "isDragging"], "validTypes": ["text", "video", "image", "shape", "caption", "sticker"], "positionBounds": {"left": [0, 1080], "top": [0, 1920], "width": [1, 1080], "height": [1, 1920]}, "timingConstraints": {"from": [0, "unlimited"], "durationInFrames": [1, "unlimited"], "rotation": [-360, 360]}, "validAnimations": ["fade", "slideRight", "slideUp", "scale", "bounce", "flipX", "zoomBlur", "snapRotate", "glitch", "swipeReveal", "floatIn"]}}