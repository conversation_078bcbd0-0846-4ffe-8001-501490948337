# LLM Instructions for Chat-Based Video Overlay Generation

## Overview

This directory contains the complete instruction system for converting natural language commands into video overlay JSON structures AND orchestrating media generation tools. The LLM system operates in dual modes:

1. **Immediate Response**: Direct overlay JSON generation for text, shapes, and layout commands
2. **Tool Orchestration**: Function calling for AI image/video generation and stock media search

The system enables users to create complex video compositions through conversational commands, with intelligent tool usage for media-heavy requests.

## Files Structure

### 1. `llm-instructions.md`
The comprehensive instruction manual for the LLM, containing:
- **System Context**: Video dimensions, coordinate system, timeline organization
- **Response Format**: Exact JSON structure requirements
- **Overlay Types**: Complete specifications for text, video, image, shape, caption, sticker overlays
- **Animation Templates**: Available entrance/exit animations
- **Positioning Guidelines**: Screen regions, common positions, row assignments
- **Timing Calculations**: Frame-based timing, staggered animations
- **Command Interpretation**: How to parse user intents and convert to overlay parameters
- **Quality Checklist**: Validation requirements for generated JSON

### 2. `llm-prompt-template.ts`
TypeScript utilities for prompt construction and validation:
- **`SYSTEM_PROMPT`**: Core instruction text for the LLM
- **`buildPrompt()`**: Function to construct contextualized prompts
- **`validateOverlayJSON()`**: Validation function for generated JSON
- **Constants**: Animation names, colors, fonts, sizes
- **Utilities**: ID generation, time conversion functions

### 3. `llm-examples.json`
Comprehensive examples and test cases:
- **11 Detailed Examples**: Complete command → JSON transformations
- **20 Test Commands**: Additional scenarios for testing
- **Validation Rules**: Field requirements and constraints

### 4. `tool-specifications.ts`
Complete tool system architecture:
- **Tool Interfaces**: TypeScript definitions for all media generation tools
- **Service Providers**: Image, video, and stock media service abstractions
- **Queue Management**: Asynchronous tool execution pipeline
- **Error Handling**: Comprehensive error types and recovery strategies
- **Usage Analytics**: Cost tracking and performance monitoring
- **LLM Schemas**: OpenAI function calling format specifications

## Key Features

### Dual-Mode Operation

#### Immediate Mode
Direct JSON response for instant elements:
- **TEXT**: Rich text with fonts, colors, animations, positioning
- **SHAPE**: Geometric shapes (rectangles, circles) with styling
- **CAPTION**: Timed captions with word-level synchronization
- **Layout Commands**: Positioning, timing, and animation adjustments

#### Tool Mode
Function calling for media generation:
- **IMAGE GENERATION**: AI-powered image creation (DALL-E, Stable Diffusion)
- **VIDEO GENERATION**: AI video synthesis (RunwayML, Pika Labs)
- **STOCK MEDIA**: Search and integration (Pexels, Unsplash, Pixabay)
- **DYNAMIC UPDATES**: Real-time overlay modification as tools complete

### Advanced Tool System
- **Queue Management**: Concurrent tool execution with priority handling
- **Loading States**: Visual feedback with `loading: true` overlay property
- **Progress Tracking**: Real-time status updates and estimated completion
- **Error Recovery**: Automatic retries, fallback services, graceful degradation
- **Cost Optimization**: Usage tracking, service selection, and budget monitoring

### Animation System
11 entrance animations available:
- `fade`, `slideRight`, `slideUp`, `scale`, `bounce`
- `flipX`, `zoomBlur`, `snapRotate`, `glitch`
- `swipeReveal`, `floatIn`

### Positioning Intelligence
- **Screen Regions**: Automatic top/middle/bottom positioning
- **Timeline Rows**: Smart layering (0=foreground, 5+=background)
- **Timing Logic**: Understands "beginning", "after X", "halfway through"
- **Staggering**: Sequential text animations with frame offsets

### Loading State Management
For overlays awaiting tool completion:
- **Visual Indicators**: Spinner overlays in timeline items
- **Content States**: "AI_GENERATING", "SEARCHING_STOCK", "PROCESSING"
- **Progress Updates**: Real-time completion percentage
- **State Transitions**: Seamless loading → completed overlay updates

## Usage Examples

### Basic Text Command
```
Input: "Add text 'Hello World' at the top"
Output: JSON with positioned text overlay, proper styling, default animation
```

### Tool-Based Media Generation
```
Input: "Add a sunset landscape background image"
Output:
1. Immediate placeholder overlay with loading: true
2. Tool call: generateImage("sunset landscape mountains", overlayId, "photorealistic", "16:9")
3. Dynamic update when generation completes
```

### Hybrid Commands (Immediate + Tools)
```
Input: "Add title 'Summer Vibes' with beach background video"
Output:
1. Immediate text overlay for "Summer Vibes"
2. Tool call: generateVideo("beach waves sunset", overlayId, 5, "cinematic", "16:9")
3. Both overlays positioned and timed appropriately
```

### Stock Media Search
```
Input: "Add a business meeting video in the background"
Output:
1. Placeholder video overlay with loading: true
2. Tool call: searchStockMedia("business meeting corporate", "video", overlayId, "16:9")
3. Update with selected stock video when search completes
```

## Implementation Integration

### In Chat Service (`chat-service.ts`):
```typescript
import { buildPrompt, validateOverlayJSON } from './llm-prompt-template';
import { ToolExecutor } from './tool-executor';

const prompt = buildPrompt(userCommand, {
  currentOverlays: existingOverlays,
  totalDurationFrames: timelineDuration,
  aspectRatio: '9:16'
});

// Use function calling LLM for tool-capable responses
const response = await llmAPI.generateWithTools(prompt, TOOL_SCHEMAS);

// Handle immediate overlays
if (response.overlays) {
  const validation = validateOverlayJSON(JSON.stringify({ overlays: response.overlays }));
  if (validation.valid) {
    addOverlays(validation.data.overlays);
  }
}

// Handle tool calls
if (response.toolCalls) {
  const toolExecutor = new ToolExecutor();
  response.toolCalls.forEach(async (toolCall) => {
    const execution = await toolExecutor.execute(toolCall);
    // Tool will update overlay when complete via updateOverlay callback
  });
}
```

### Context Awareness
The system includes current timeline state in prompts:
- Existing overlays for conflict avoidance
- Timeline duration for timing calculations
- Recent overlay context for relative positioning

## Performance Optimizations

### Prompt Engineering
- Structured format with clear sections
- Examples embedded in system prompt
- Validation rules clearly specified
- Default values provided for missing data

### Response Validation
- JSON structure verification
- Position bounds checking
- Required field validation
- Animation name validation
- Timing constraint validation

### Error Handling
- Graceful fallbacks for invalid commands
- Default styling when properties missing
- Position adjustment for out-of-bounds coordinates
- ID collision prevention

## Quality Assurance

### Validation Checklist
- [ ] Valid JSON structure
- [ ] All required fields present
- [ ] Unique IDs generated (10000-999999 range)
- [ ] Positions within screen bounds (0-1080 x 0-1920)
- [ ] Animation names from available list
- [ ] Frame calculations correct (30 FPS)
- [ ] Color values valid hex codes
- [ ] Row assignments logical

### Testing Strategy
- Unit tests for prompt generation
- Integration tests for LLM responses
- Validation tests for edge cases
- Performance tests for response times
- Quality tests for overlay positioning

## Advanced Features

### Staggered Animations
For multi-word text that should appear sequentially:
```
"ARE YOU READY?" → 3 overlays with 7-8 frame offsets
Word 1: from: 53, duration: 70
Word 2: from: 60, duration: 67
Word 3: from: 68, duration: 63
```

### Context-Aware Positioning
- Analyzes existing overlays to avoid conflicts
- Suggests complementary positions
- Maintains visual hierarchy through row assignments
- Calculates optimal timing based on content flow

### Smart Defaults
- Professional typography settings
- Appropriate animation choices for content type
- Optimal duration based on text length
- Standard positioning for common elements

## Configuration

### Model Selection
Recommended LLM models for optimal performance:
- **Primary**: Mistral 7B (0.31s latency, good reasoning)
- **Fallback**: Gemini 1.5 Flash (314 tokens/sec, fast response)
- **Local**: Llama 3.2 1B (558 tokens/sec, for development)

### Prompt Tuning
Key parameters for fine-tuning:
- Temperature: 0.1-0.3 (low for consistent JSON structure)
- Max tokens: 2000-4000 (sufficient for complex overlays)
- Top-p: 0.9 (good balance of creativity and consistency)

## Troubleshooting

### Common Issues
1. **Invalid JSON**: Ensure proper escaping in prompt examples
2. **Out-of-bounds positions**: Validate against screen dimensions
3. **Missing animations**: Check animation name against available list
4. **Timing conflicts**: Consider existing overlay durations
5. **ID collisions**: Use proper random generation range

### Debug Mode
Enable verbose logging to trace:
- Input command parsing
- Context injection
- LLM response processing
- Validation steps
- Final overlay generation

## Future Enhancements

### Planned Features
- Multi-language command support
- Voice-to-text integration
- Advanced animation sequencing
- Template-based overlay generation
- Batch operation commands
- Undo/redo command tracking

### Model Improvements
- Fine-tuning on video editing domain
- Custom animation vocabulary expansion
- Style transfer from existing projects
- User preference learning
- Context memory for session continuity

---

This instruction system provides the foundation for natural language video editing, enabling users to create professional overlay compositions through simple conversational commands.