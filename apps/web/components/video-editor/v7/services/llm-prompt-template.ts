/**
 * LLM Prompt Template for Video Overlay Generation
 * This file contains the system prompt and utilities for generating overlay JSON from natural language
 */

export interface PromptContext {
  currentOverlays?: any[];
  totalDurationFrames?: number;
  aspectRatio?: string;
  playerDimensions?: {
    width: number;
    height: number;
  };
}

export const SYSTEM_PROMPT = `You are an expert video editor assistant that converts natural language commands into precise overlay JSON structures AND can use tools to generate/modify media assets.

## Operation Modes
1. **Immediate Mode**: Generate overlay JSON for instant elements (text, shapes, layouts)
2. **Tool Mode**: Use function calls for media generation (images, videos, stock search)
3. **Hybrid Mode**: Combine immediate overlays with tool calls for complex commands

## Available Tools
- generateImage(prompt, overlayId, style, aspectRatio, quality)
- generateVideo(prompt, overlayId, duration, style, aspectRatio)
- searchStockMedia(query, type, overlayId, duration, aspectRatio)
- updateOverlay(overlayId, updates)

## Decision Logic
- Text, shapes, captions, basic animations → Immediate Mode (JSON response)
- "Add sunset image", "Create person waving video" → Tool Mode (function calls + placeholder)
- "Add title and mountain background" → Hybrid Mode (immediate text + image generation)

## System Context
- **Video Dimensions**: Default 9:16 aspect ratio (1080x1920 pixels)
- **Timeline**: Frame-based (30 fps standard)
- **Coordinate System**: Top-left origin (0,0), positive X right, positive Y down
- **Rows**: Timeline organized in rows (0-10+), lower numbers = higher visual priority

## Response Format Requirements

### Immediate Mode (Direct JSON)
{
  "overlays": [
    {
      "id": <unique_number>,
      "type": "<overlay_type>",
      "content": "<content_string>",
      "left": <x_position>,
      "top": <y_position>,
      "width": <width_pixels>,
      "height": <height_pixels>,
      "row": <timeline_row>,
      "from": <start_frame>,
      "durationInFrames": <duration_frames>,
      "rotation": <rotation_degrees>,
      "isDragging": false,
      "loading": false,
      "styles": { /* type-specific styles */ }
    }
  ],
  "aspectRatio": "9:16",
  "playerDimensions": {
    "width": 564.8642578125,
    "height": 1004.203125
  }
}

### Tool Mode (Function Calls + Placeholders)
{
  "overlays": [
    {
      "id": 12345,
      "type": "image",
      "src": "",
      "content": "AI_GENERATING",
      "loading": true,
      /* positioning and timing */
    }
  ],
  "toolCalls": [
    {
      "function": "generateImage",
      "parameters": {
        "prompt": "sunset landscape mountains",
        "overlayId": 12345,
        "style": "photorealistic",
        "aspectRatio": "16:9"
      }
    }
  ],
  "aspectRatio": "9:16",
  "playerDimensions": {
    "width": 564.8642578125,
    "height": 1004.203125
  }
}

## Available Overlay Types
1. **"text"** - Text overlays with rich styling
2. **"video"** - Video clips (src will be filled later, use content for description)
3. **"image"** - Images (src will be filled later, use content for description)
4. **"shape"** - Geometric shapes (rectangle, circle, etc.)
5. **"caption"** - Timed captions with word-level timing
6. **"sticker"** - Emoji/sticker overlays

## Animation Templates Available
Use these exact strings for animation.enter or animation.exit:
- "fade", "slideRight", "slideUp", "scale", "bounce", "flipX", "zoomBlur", "snapRotate", "glitch", "swipeReveal", "floatIn"

## Positioning Guidelines
- Top third: y 0-640, Middle: y 640-1280, Bottom: y 1280-1920
- Common text areas: Title (y 200-500), Subtitle (y 500-800), Bottom (y 1300-1700)
- Timeline rows: 0 (foreground), 1-3 (mid), 4-7 (background), 8+ (base)

## Timing (30 FPS)
- 1 second = 30 frames
- Quick flash: 15 frames, Short: 60 frames, Medium: 90 frames, Long: 150 frames
- Stagger word animations by 6-15 frames for sequential text

## Content Descriptions for Media
- Video: "VIDEO_DESCRIPTION: [person waving, sitting by window]"
- Image: "IMAGE_DESCRIPTION: [red sports car, side view]"
- Sticker: "STICKER_DESCRIPTION: [thumbs up emoji]"
- Always use empty string for "src" field

## ID Generation
Generate unique random 5-6 digit IDs (10000-999999) for each overlay.

## Quality Requirements
- Valid JSON structure
- All required fields present
- Positions within screen bounds (0-1080 x, 0-1920 y)
- Frame calculations correct (30 FPS)
- Animation names from available list
- Valid hex colors (#FFFFFF format)

## Default Values Template
{
  "left": 100, "top": 500, "width": 880, "height": 200,
  "row": 0, "from": 0, "durationInFrames": 90,
  "rotation": 0, "isDragging": false,
  "styles": { "opacity": 1, "zIndex": 1, "transform": "none" }
}`;

export const buildPrompt = (userCommand: string, context?: PromptContext): string => {
  let prompt = SYSTEM_PROMPT;

  if (context) {
    prompt += `\n\n## Current Context`;

    if (context.currentOverlays?.length) {
      prompt += `\n**Existing Overlays**: ${context.currentOverlays.length} overlays currently on timeline`;

      // Include last few overlays for context
      const recentOverlays = context.currentOverlays.slice(-3).map(o =>
        `Row ${o.row}: ${o.type} "${o.content?.substring(0, 30) || 'N/A'}" (frames ${o.from}-${o.from + o.durationInFrames})`
      ).join(', ');
      prompt += `\n**Recent**: ${recentOverlays}`;
    }

    if (context.totalDurationFrames) {
      prompt += `\n**Timeline Duration**: ${context.totalDurationFrames} frames (${(context.totalDurationFrames / 30).toFixed(1)}s)`;
    }

    if (context.aspectRatio && context.aspectRatio !== '9:16') {
      prompt += `\n**Aspect Ratio**: ${context.aspectRatio}`;
    }
  }

  prompt += `\n\n## User Command\n"${userCommand}"`;

  prompt += `\n\n## Instructions
Analyze the user command and generate the appropriate overlay JSON. Consider:
1. Extract all elements mentioned (text, videos, images, shapes, etc.)
2. Determine positioning based on keywords (top, center, bottom, left, right)
3. Calculate timing (beginning, middle, end, "after X", "X seconds")
4. Choose appropriate animations and styling
5. Generate unique IDs for each overlay
6. Ensure overlays don't conflict in positioning/timing
7. Use appropriate row assignments for layering

Respond ONLY with valid JSON - no explanations or additional text.`;

  return prompt;
};

export const validateOverlayJSON = (jsonString: string): { valid: boolean; error?: string; data?: any } => {
  try {
    const data = JSON.parse(jsonString);

    // Check required structure
    if (!data.overlays || !Array.isArray(data.overlays)) {
      return { valid: false, error: 'Missing or invalid overlays array' };
    }

    if (!data.aspectRatio) {
      return { valid: false, error: 'Missing aspectRatio' };
    }

    if (!data.playerDimensions || typeof data.playerDimensions !== 'object') {
      return { valid: false, error: 'Missing or invalid playerDimensions' };
    }

    // Validate each overlay
    for (let i = 0; i < data.overlays.length; i++) {
      const overlay = data.overlays[i];
      const requiredFields = ['id', 'type', 'left', 'top', 'width', 'height', 'row', 'from', 'durationInFrames', 'rotation', 'isDragging'];

      for (const field of requiredFields) {
        if (overlay[field] === undefined || overlay[field] === null) {
          return { valid: false, error: `Overlay ${i}: missing required field '${field}'` };
        }
      }

      // Validate position bounds
      if (overlay.left < 0 || overlay.left > 1080) {
        return { valid: false, error: `Overlay ${i}: left position ${overlay.left} out of bounds (0-1080)` };
      }

      if (overlay.top < 0 || overlay.top > 1920) {
        return { valid: false, error: `Overlay ${i}: top position ${overlay.top} out of bounds (0-1920)` };
      }

      // Validate overlay type
      const validTypes = ['text', 'video', 'image', 'shape', 'caption', 'sticker'];
      if (!validTypes.includes(overlay.type)) {
        return { valid: false, error: `Overlay ${i}: invalid type '${overlay.type}'. Must be one of: ${validTypes.join(', ')}` };
      }

      // Validate frames
      if (overlay.from < 0 || overlay.durationInFrames <= 0) {
        return { valid: false, error: `Overlay ${i}: invalid timing (from: ${overlay.from}, duration: ${overlay.durationInFrames})` };
      }
    }

    return { valid: true, data };
  } catch (error) {
    return { valid: false, error: `Invalid JSON: ${error instanceof Error ? error.message : 'Unknown error'}` };
  }
};

export const AVAILABLE_ANIMATIONS = [
  'fade', 'slideRight', 'slideUp', 'scale', 'bounce', 'flipX',
  'zoomBlur', 'snapRotate', 'glitch', 'swipeReveal', 'floatIn'
];

export const COMMON_COLORS = {
  white: '#FFFFFF',
  black: '#000000',
  red: '#FF0000',
  green: '#00FF00',
  blue: '#0000FF',
  yellow: '#FFFF00',
  orange: '#FFA500',
  purple: '#800080',
  pink: '#FFC0CB',
  gray: '#808080',
  darkgray: '#404040',
  lightgray: '#D3D3D3'
};

export const FONT_FAMILIES = [
  'font-sans', 'font-serif', 'font-mono', 'font-league-spartan'
];

export const FONT_SIZES = [
  '1rem', '1.5rem', '2rem', '2.5rem', '3rem', '4rem', '5rem'
];

export const FONT_WEIGHTS = [
  '100', '300', '400', '500', '700', '900'
];

export const TEXT_ALIGNS = [
  'left', 'center', 'right'
];

export const TEXT_TRANSFORMS = [
  'none', 'uppercase', 'lowercase', 'capitalize'
];

export const OBJECT_FITS = [
  'contain', 'cover', 'fill', 'none', 'scale-down'
];

// Utility function to generate unique IDs
export const generateOverlayId = (): number => {
  return Math.floor(Math.random() * (999999 - 10000 + 1)) + 10000;
};

// Utility function to convert seconds to frames
export const secondsToFrames = (seconds: number, fps: number = 30): number => {
  return Math.round(seconds * fps);
};

// Utility function to convert frames to seconds
export const framesToSeconds = (frames: number, fps: number = 30): number => {
  return frames / fps;
};

export default {
  SYSTEM_PROMPT,
  buildPrompt,
  validateOverlayJSON,
  AVAILABLE_ANIMATIONS,
  COMMON_COLORS,
  FONT_FAMILIES,
  FONT_SIZES,
  FONT_WEIGHTS,
  TEXT_ALIGNS,
  TEXT_TRANSFORMS,
  OBJECT_FITS,
  generateOverlayId,
  secondsToFrames,
  framesToSeconds
};