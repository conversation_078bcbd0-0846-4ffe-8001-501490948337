/**
 * Tool Specifications for Chat-Based Video Overlay Generation
 * Defines the interface between LLM tool calls and media generation services
 */

// Base interfaces for tool system
export interface BaseToolCall {
  function: string;
  parameters: Record<string, any>;
}

export interface ToolResponse {
  success: boolean;
  overlayId: number;
  mediaUrl?: string;
  error?: string;
  processingTime?: number;
  metadata?: Record<string, any>;
}

export interface ToolExecution {
  id: string;
  toolCall: ToolCall;
  overlayId: number;
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  endTime?: number;
  estimatedDuration: number;
  progress?: number; // 0-100
  result?: ToolResponse;
  retryCount?: number;
  error?: string;
}

// Tool-specific interfaces
export interface ImageGenerationParams {
  prompt: string;
  overlayId: number;
  style: 'photorealistic' | 'cartoon' | 'artistic' | 'logo' | 'sketch';
  aspectRatio: '16:9' | '9:16' | '1:1' | '4:3' | '3:4';
  quality: 'standard' | 'high' | 'ultra';
  seed?: number;
  negativePrompt?: string;
}

export interface VideoGenerationParams {
  prompt: string;
  overlayId: number;
  duration: number; // seconds (1-10)
  style: 'realistic' | 'animated' | 'cinematic' | 'documentary';
  aspectRatio: '16:9' | '9:16' | '1:1';
  fps: 24 | 30 | 60;
  motionStrength?: 'low' | 'medium' | 'high';
  cameraMovement?: 'static' | 'pan' | 'zoom' | 'dolly';
  seed?: number;
}

export interface StockMediaSearchParams {
  query: string;
  type: 'video' | 'image';
  overlayId: number;
  duration?: number; // for videos only
  aspectRatio: '16:9' | '9:16' | '1:1' | 'any';
  quality: 'standard' | 'high' | 'premium';
  category?: string;
  color?: 'any' | 'black-white' | 'red' | 'blue' | 'green' | 'yellow';
  orientation?: 'horizontal' | 'vertical' | 'square';
  license?: 'free' | 'premium' | 'editorial';
}

export interface OverlayUpdateParams {
  overlayId: number;
  updates: {
    src?: string;
    content?: string;
    loading?: boolean;
    styles?: Record<string, any>;
    width?: number;
    height?: number;
    left?: number;
    top?: number;
    from?: number;
    durationInFrames?: number;
    rotation?: number;
  };
}

// Union type for all tool calls
export type ToolCall =
  | { function: 'generateImage'; parameters: ImageGenerationParams }
  | { function: 'generateVideo'; parameters: VideoGenerationParams }
  | { function: 'searchStockMedia'; parameters: StockMediaSearchParams }
  | { function: 'updateOverlay'; parameters: OverlayUpdateParams };

// Tool capability definitions
export interface ToolCapabilities {
  generateImage: {
    maxWidth: number;
    maxHeight: number;
    supportedFormats: string[];
    estimatedTime: number; // seconds
    costPer1K: number; // USD
    concurrent: number;
  };
  generateVideo: {
    maxDuration: number; // seconds
    maxWidth: number;
    maxHeight: number;
    supportedFormats: string[];
    estimatedTime: number; // seconds per second of video
    costPer1K: number; // USD
    concurrent: number;
  };
  searchStockMedia: {
    dailyLimit: number;
    resultsPerQuery: number;
    estimatedTime: number; // seconds
    costPer1K: number; // USD
  };
}

// Default capabilities (to be overridden by actual service implementations)
export const DEFAULT_TOOL_CAPABILITIES: ToolCapabilities = {
  generateImage: {
    maxWidth: 2048,
    maxHeight: 2048,
    supportedFormats: ['jpg', 'png', 'webp'],
    estimatedTime: 15,
    costPer1K: 20.00,
    concurrent: 3
  },
  generateVideo: {
    maxDuration: 10,
    maxWidth: 1920,
    maxHeight: 1920,
    supportedFormats: ['mp4', 'webm'],
    estimatedTime: 30, // per second of video
    costPer1K: 100.00,
    concurrent: 1
  },
  searchStockMedia: {
    dailyLimit: 1000,
    resultsPerQuery: 20,
    estimatedTime: 2,
    costPer1K: 0.50
  }
};

// Tool execution queue configuration
export interface QueueConfig {
  maxConcurrent: number;
  retryAttempts: number;
  retryDelay: number; // milliseconds
  timeout: number; // milliseconds
  priority: {
    updateOverlay: number;
    searchStockMedia: number;
    generateImage: number;
    generateVideo: number;
  };
}

export const DEFAULT_QUEUE_CONFIG: QueueConfig = {
  maxConcurrent: 5,
  retryAttempts: 3,
  retryDelay: 2000,
  timeout: 300000, // 5 minutes
  priority: {
    updateOverlay: 1, // highest priority
    searchStockMedia: 2,
    generateImage: 3,
    generateVideo: 4 // lowest priority (most expensive)
  }
};

// Error types for tool execution
export enum ToolErrorType {
  NETWORK_ERROR = 'network_error',
  API_LIMIT_EXCEEDED = 'api_limit_exceeded',
  INVALID_PARAMETERS = 'invalid_parameters',
  CONTENT_POLICY_VIOLATION = 'content_policy_violation',
  INSUFFICIENT_CREDITS = 'insufficient_credits',
  SERVICE_UNAVAILABLE = 'service_unavailable',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown'
}

export interface ToolError {
  type: ToolErrorType;
  message: string;
  details?: Record<string, any>;
  retryable: boolean;
}

// Service provider interfaces
export interface ImageGenerationService {
  name: string;
  generateImage(params: ImageGenerationParams): Promise<ToolResponse>;
  getCapabilities(): ToolCapabilities['generateImage'];
  validateParams(params: ImageGenerationParams): ToolError | null;
}

export interface VideoGenerationService {
  name: string;
  generateVideo(params: VideoGenerationParams): Promise<ToolResponse>;
  getCapabilities(): ToolCapabilities['generateVideo'];
  validateParams(params: VideoGenerationParams): ToolError | null;
}

export interface StockMediaService {
  name: string;
  searchMedia(params: StockMediaSearchParams): Promise<ToolResponse>;
  getCapabilities(): ToolCapabilities['searchStockMedia'];
  validateParams(params: StockMediaSearchParams): ToolError | null;
}

// Tool registry for managing multiple service providers
export interface ToolRegistry {
  imageServices: ImageGenerationService[];
  videoServices: VideoGenerationService[];
  stockServices: StockMediaService[];

  // Service selection logic
  selectImageService(params: ImageGenerationParams): ImageGenerationService;
  selectVideoService(params: VideoGenerationParams): VideoGenerationService;
  selectStockService(params: StockMediaSearchParams): StockMediaService;

  // Health checking
  getServiceHealth(): Record<string, boolean>;
}

// Tool usage analytics
export interface ToolUsageMetrics {
  totalExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  costToDate: number;
  errorBreakdown: Record<ToolErrorType, number>;
  popularTools: Array<{
    toolName: string;
    usage: number;
    successRate: number;
  }>;
}

// LLM Tool Function Schemas (for OpenAI function calling format)
export const TOOL_SCHEMAS = {
  generateImage: {
    name: 'generateImage',
    description: 'Generate an AI image based on a text prompt',
    parameters: {
      type: 'object',
      properties: {
        prompt: {
          type: 'string',
          description: 'Detailed description of the image to generate'
        },
        overlayId: {
          type: 'number',
          description: 'ID of the overlay to update with generated image'
        },
        style: {
          type: 'string',
          enum: ['photorealistic', 'cartoon', 'artistic', 'logo', 'sketch'],
          description: 'Visual style for the generated image'
        },
        aspectRatio: {
          type: 'string',
          enum: ['16:9', '9:16', '1:1', '4:3', '3:4'],
          description: 'Aspect ratio of the generated image'
        },
        quality: {
          type: 'string',
          enum: ['standard', 'high', 'ultra'],
          description: 'Quality level for image generation'
        }
      },
      required: ['prompt', 'overlayId', 'style', 'aspectRatio']
    }
  },

  generateVideo: {
    name: 'generateVideo',
    description: 'Generate an AI video based on a text prompt',
    parameters: {
      type: 'object',
      properties: {
        prompt: {
          type: 'string',
          description: 'Detailed description of the video action/scene to generate'
        },
        overlayId: {
          type: 'number',
          description: 'ID of the overlay to update with generated video'
        },
        duration: {
          type: 'number',
          minimum: 1,
          maximum: 10,
          description: 'Duration of video in seconds'
        },
        style: {
          type: 'string',
          enum: ['realistic', 'animated', 'cinematic', 'documentary'],
          description: 'Visual style for the generated video'
        },
        aspectRatio: {
          type: 'string',
          enum: ['16:9', '9:16', '1:1'],
          description: 'Aspect ratio of the generated video'
        }
      },
      required: ['prompt', 'overlayId', 'duration', 'style', 'aspectRatio']
    }
  },

  searchStockMedia: {
    name: 'searchStockMedia',
    description: 'Search for stock photos or videos',
    parameters: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search terms for finding relevant stock media'
        },
        type: {
          type: 'string',
          enum: ['video', 'image'],
          description: 'Type of media to search for'
        },
        overlayId: {
          type: 'number',
          description: 'ID of the overlay to update with found media'
        },
        aspectRatio: {
          type: 'string',
          enum: ['16:9', '9:16', '1:1', 'any'],
          description: 'Preferred aspect ratio'
        },
        quality: {
          type: 'string',
          enum: ['standard', 'high', 'premium'],
          description: 'Quality level for stock media'
        }
      },
      required: ['query', 'type', 'overlayId']
    }
  },

  updateOverlay: {
    name: 'updateOverlay',
    description: 'Update properties of an existing overlay',
    parameters: {
      type: 'object',
      properties: {
        overlayId: {
          type: 'number',
          description: 'ID of the overlay to update'
        },
        updates: {
          type: 'object',
          description: 'Properties to update on the overlay',
          properties: {
            src: { type: 'string' },
            content: { type: 'string' },
            loading: { type: 'boolean' },
            left: { type: 'number' },
            top: { type: 'number' },
            width: { type: 'number' },
            height: { type: 'number' },
            from: { type: 'number' },
            durationInFrames: { type: 'number' },
            rotation: { type: 'number' }
          }
        }
      },
      required: ['overlayId', 'updates']
    }
  }
};

export default {
  ToolCall,
  ToolResponse,
  ToolExecution,
  ImageGenerationParams,
  VideoGenerationParams,
  StockMediaSearchParams,
  OverlayUpdateParams,
  DEFAULT_TOOL_CAPABILITIES,
  DEFAULT_QUEUE_CONFIG,
  ToolErrorType,
  TOOL_SCHEMAS
};