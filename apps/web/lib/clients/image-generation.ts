import type { GenerateImageRequest, GenerateImageResponse } from "@kit/shared/image-generation";

export async function generateImage(params: GenerateImageRequest): Promise<GenerateImageResponse> {
  const res = await fetch("/api/ai/generate-image", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      image_prompt: params.image_prompt,
      aspect_ratio: params.aspect_ratio ?? "custom",
      company_id: params.company_id,
      image_gen_styles: params.image_gen_styles,
      brand_context: params.brand_context,
    }),
  });

  if (!res.ok) {
    let message = `Image generation failed (${res.status})`;
    try {
      const data = await res.json();
      if (data?.error) message = String(data.error);
    } catch {
      // ignore parse error
    }
    throw new Error(message);
  }

  return (await res.json()) as GenerateImageResponse;
}

