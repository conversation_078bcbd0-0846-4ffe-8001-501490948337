BEGIN;
create extension if not exists "basejump-supabase_test_helpers" version '0.0.6';

select no_plan();

-- Create two users
select tests.create_supabase_user('u1', '<EMAIL>');
select tests.create_supabase_user('u2', '<EMAIL>');

-- u1 creates a team account
select makerkit.authenticate_as('u1');
select public.create_team_account('TeamA');

-- Resolve account id by slug (lowercased slug generation assumed by makerkit helpers)
-- If slug logic differs, adjust accordingly
select ok(makerkit.get_account_id_by_slug('teama') is not null, 'TeamA account created');

-- Insert an asset as u1 (owner/member)
insert into public.assets (
  account_id,
  file_name,
  file_path,
  folder_name
) values (
  makerkit.get_account_id_by_slug('teama'),
  'logo.png',
  'teama/brand/logo.png',
  'brand'
);

-- u1 can see their asset
select results_eq(
  $$ select count(*)::int from public.assets where account_id = makerkit.get_account_id_by_slug('teama') $$,
  $$ values (1) $$,
  'Owner can see assets from their team account'
);

-- u2 (not a member of TeamA) cannot see TeamA assets
select makerkit.authenticate_as('u2');
select results_eq(
  $$ select count(*)::int from public.assets where account_id = makerkit.get_account_id_by_slug('teama') $$,
  $$ values (0) $$,
  'Non-member cannot see assets from another team account'
);

-- u2 cannot insert into TeamA assets
select throws_ok(
  $$ insert into public.assets (account_id, file_name, file_path, folder_name) values (makerkit.get_account_id_by_slug('teama'), 'bad.txt', 'teama/brand/bad.txt', 'brand') $$,
  'new row violates row-level security policy for table "assets"',
  'Non-member cannot insert assets into another team account'
);

select finish();
rollback;

