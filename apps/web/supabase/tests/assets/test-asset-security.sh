#!/bin/bash

# Security Test Script for Asset Access Fixes
# This script tests that the security fixes are working properly

echo "🔒 Testing Asset Security Fixes..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test 1: Check that storage buckets are private
echo -e "\n${YELLOW}Test 1: Checking storage bucket privacy...${NC}"
cd apps/web

# Check if migration exists
if [ -f "supabase/migrations/20250125000000_fix_storage_bucket_security.sql" ]; then
    echo -e "${GREEN}✅ Security migration file exists${NC}"
else
    echo -e "${RED}❌ Security migration file missing${NC}"
    exit 1
fi

# Test 2: Check cache key fixes
echo -e "\n${YELLOW}Test 2: Checking cache key fixes...${NC}"

# Check AssetsDisplay.tsx
if grep -q "queryKey: \['assetFolders', account\.id\]" "app/home/<USER>/assets/components/AssetsDisplay.tsx"; then
    echo -e "${GREEN}✅ AssetsDisplay cache key fixed${NC}"
else
    echo -e "${RED}❌ AssetsDisplay cache key not fixed${NC}"
fi

# Check FolderDisplay.tsx
if grep -q "queryKey: \['folderContents', workspace\.account\.id, folderName\]" "app/home/<USER>/assets/components/FolderDisplay.tsx"; then
    echo -e "${GREEN}✅ FolderDisplay cache key fixed${NC}"
else
    echo -e "${RED}❌ FolderDisplay cache key not fixed${NC}"
fi

# Test 3: Check server-side validation
echo -e "\n${YELLOW}Test 3: Checking server-side validation...${NC}"

# Check storage.ts
if grep -q "Unauthorized access to account assets" "app/services/storage.ts"; then
    echo -e "${GREEN}✅ Storage service validation added${NC}"
else
    echo -e "${RED}❌ Storage service validation missing${NC}"
fi

# Check assets page
if grep -q "requireUserInServerComponent" "app/home/<USER>/assets/page.tsx"; then
    echo -e "${GREEN}✅ Assets page server validation added${NC}"
else
    echo -e "${RED}❌ Assets page server validation missing${NC}"
fi

# Test 4: Check unauthorized page
echo -e "\n${YELLOW}Test 4: Checking unauthorized page...${NC}"
if [ -f "app/unauthorized/page.tsx" ]; then
    echo -e "${GREEN}✅ Unauthorized page created${NC}"
else
    echo -e "${RED}❌ Unauthorized page missing${NC}"
fi

echo -e "\n${GREEN}🎉 Security test completed!${NC}"
echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Run the migration: pnpm run supabase:db:push"
echo "2. Test in browser by switching between team accounts"
echo "3. Verify that you only see assets for the current team"
echo "4. Check browser dev tools to ensure cache keys include account IDs"

echo -e "\n${RED}⚠️  IMPORTANT: The storage bucket privacy fix is CRITICAL for security!${NC}"
echo "Make sure to deploy this migration immediately to production."

