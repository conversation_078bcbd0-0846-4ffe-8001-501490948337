-- Fix brand-assets storage policies for DELETE operations
-- This migration adds missing DELETE policies for the brand-assets bucket
-- which prevents users from moving assets between folders

-- Add DELETE policy for brand-assets bucket
CREATE POLICY "Users can delete brand assets for their accounts"
ON storage.objects
FOR DELETE
TO public
USING (
  bucket_id = 'brand-assets'::text 
  AND (storage.foldername(name))[1] IN (
    SELECT (accounts.id)::text AS id
    FROM accounts
    WHERE accounts.id IN (
      SELECT accounts_memberships.account_id
      FROM accounts_memberships
      WHERE accounts_memberships.user_id = auth.uid()
    )
  )
);

-- Add ALL policy for service_role to manage brand-assets (needed for server operations)
CREATE POLICY "Service role can manage all brand assets"
ON storage.objects
FOR ALL
TO service_role
USING (bucket_id = 'brand-assets'::text)
WITH CHECK (bucket_id = 'brand-assets'::text);

-- Grant necessary permissions
GRANT DELETE ON storage.objects TO public;
GRANT ALL ON storage.objects TO service_role;
