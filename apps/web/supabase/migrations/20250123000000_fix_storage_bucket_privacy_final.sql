-- Fix storage bucket privacy settings for proper team access control
-- This migration ensures all storage buckets are properly configured as private
-- with appropriate RLS policies for team-based access

-- Make brand-assets bucket private (if not already)
UPDATE storage.buckets 
SET public = false 
WHERE id = 'brand-assets';

-- Make generated bucket private (if not already)
UPDATE storage.buckets 
SET public = false 
WHERE id = 'generated';

-- Verify the changes
DO $$ 
BEGIN
  -- Check that buckets are now private
  IF EXISTS (
    SELECT 1 FROM storage.buckets 
    WHERE id IN ('brand-assets', 'generated') 
    AND public = true
  ) THEN
    RAISE EXCEPTION 'Failed to make storage buckets private - security vulnerability still exists';
  END IF;
  
  RAISE NOTICE 'Storage buckets successfully made private - team access control enabled';
END $$;
