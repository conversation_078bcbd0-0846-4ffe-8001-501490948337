alter table "public"."saved_research" add column "source_content" text;

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text, website text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    new_account public.accounts;
    column_exists boolean;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    -- Check if website column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'accounts' AND column_name = 'website'
    ) INTO column_exists;

    -- Insert with conditional website column
    IF column_exists THEN
        insert into public.accounts(
            name,
            website,
            is_personal_account)
        values (
            account_name,
            website,
            false)
        returning * into new_account;
    ELSE
        insert into public.accounts(
            name,
            is_personal_account)
        values (
            account_name,
            false)
        returning * into new_account;
    END IF;

    return new_account;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text, website text, public_data text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$
declare
    new_account public.accounts;
    website_exists boolean;
    public_data_exists boolean;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    -- Check if columns exist
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'accounts' AND column_name = 'website'
    ) INTO website_exists;

    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'accounts' AND column_name = 'public_data'
    ) INTO public_data_exists;

    -- Build dynamic insert based on existing columns
    IF website_exists AND public_data_exists THEN
        insert into public.accounts(
            name,
            website,
            public_data,
            is_personal_account)
        values (
            account_name,
            website,
            public_data::jsonb,
            false)
        returning * into new_account;
    ELSIF website_exists THEN
        insert into public.accounts(
            name,
            website,
            is_personal_account)
        values (
            account_name,
            website,
            false)
        returning * into new_account;
    ELSIF public_data_exists THEN
        insert into public.accounts(
            name,
            public_data,
            is_personal_account)
        values (
            account_name,
            public_data::jsonb,
            false)
        returning * into new_account;
    ELSE
        insert into public.accounts(
            name,
            is_personal_account)
        values (
            account_name,
            false)
        returning * into new_account;
    END IF;

    return new_account;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.create_team_account(account_name text)
 RETURNS accounts
 LANGUAGE plpgsql
 SET search_path TO ''
AS $function$declare
    new_account public.accounts;
begin
    if (not public.is_set('enable_team_accounts')) then
        raise exception 'Team accounts are not enabled';
    end if;

    insert into public.accounts(
        name,
        is_personal_account)
    values (
        account_name,
        false)
returning
    * into new_account;

    return new_account;

end;$function$
;


