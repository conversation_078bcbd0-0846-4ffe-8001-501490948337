-- Migration: Add overlay video generation support
-- Date: 2025-09-18
-- Description: Add overlay_id and project_id fields to video_generation_jobs table to support video editor overlay updates

-- Add overlay_id and project_id columns to video_generation_jobs table
ALTER TABLE IF EXISTS public.video_generation_jobs 
ADD COLUMN IF NOT EXISTS overlay_id text,
ADD COLUMN IF NOT EXISTS project_id text;

-- Add comment to document the new fields
COMMENT ON COLUMN public.video_generation_jobs.overlay_id IS 'UUID of the overlay to update when video generation completes';
COMMENT ON COLUMN public.video_generation_jobs.project_id IS 'Project ID for autosave updates via Zero sync';

-- Create index for performance on overlay_id lookups
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_overlay_id 
  ON public.video_generation_jobs(overlay_id);

-- Create index for performance on project_id lookups  
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_project_id 
  ON public.video_generation_jobs(project_id);

