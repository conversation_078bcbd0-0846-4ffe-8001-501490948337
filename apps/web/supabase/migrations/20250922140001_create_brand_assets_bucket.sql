-- Create the brand-assets storage bucket
-- This bucket is used for storing company brand assets, logos, fonts, and user-uploaded files

INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'brand-assets',
  'brand-assets',
  true,
  *********, -- 100MB limit
  ARRAY[
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
    'image/svg+xml',
    'video/mp4',
    'video/webm',
    'video/quicktime',
    'audio/mpeg',
    'audio/wav',
    'audio/mp3',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv',
    'font/woff',
    'font/woff2',
    'font/ttf',
    'font/otf'
  ]::text[]
)
ON CONFLICT (id) DO NOTHING;

-- Grant necessary permissions for the bucket (if not already granted)
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.role_table_grants
    WHERE table_name = 'objects' AND table_schema = 'storage' AND grantee = 'authenticated'
  ) THEN
    GRANT ALL ON storage.objects TO authenticated;
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.role_table_grants
    WHERE table_name = 'objects' AND table_schema = 'storage' AND grantee = 'service_role'
  ) THEN
    GRANT ALL ON storage.objects TO service_role;
  END IF;
END $$;

-- Note: Storage policies are handled in 20250922140000_create_assets_infrastructure.sql
-- to ensure comprehensive policy management and avoid conflicts
