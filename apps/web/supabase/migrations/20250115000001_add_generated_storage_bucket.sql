-- Add generated storage bucket for AI-generated images and reference images
-- This bucket is used by the image studio and content studio for storing generated/uploaded images

-- Create the generated storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'generated',
  'generated',
  true, -- Public bucket for easy access to generated images
  ********, -- 10MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']::text[]
)
ON CONFLICT (id) DO NOTHING;

-- RLS policies for generated bucket
-- Users can upload images for accounts they have access to
CREATE POLICY "Users can upload images for their accounts"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Users can view images for accounts they have access to
CREATE POLICY "Users can view images for their accounts"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Users can update images for accounts they have access to
CREATE POLICY "Users can update images for their accounts"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Users can delete images for accounts they have access to
CREATE POLICY "Users can delete images for their accounts"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Service role can do everything (for server-side uploads)
CREATE POLICY "Service role can manage all generated images"
  ON storage.objects FOR ALL
  USING (bucket_id = 'generated')
  WITH CHECK (bucket_id = 'generated');
