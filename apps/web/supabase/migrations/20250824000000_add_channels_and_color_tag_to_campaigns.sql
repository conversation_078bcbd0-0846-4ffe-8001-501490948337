-- Add channels array and color_tag fields to company_campaigns table
-- These fields support multi-channel campaign management and visual organization

-- Add channels column as jsonb array
ALTER TABLE public.company_campaigns ADD COLUMN IF NOT EXISTS channels jsonb DEFAULT '[]'::jsonb;

-- Add color_tag column for visual differentiation in calendar views
ALTER TABLE public.company_campaigns ADD COLUMN IF NOT EXISTS color_tag text;

-- Add posts_per_week column for campaign scheduling
ALTER TABLE public.company_campaigns ADD COLUMN IF NOT EXISTS posts_per_week integer DEFAULT 7;

-- Add comments to document the new columns
COMMENT ON COLUMN public.company_campaigns.channels IS 'Array of channel identifiers (e.g., ["x", "linkedin", "blog"]) for multi-channel publishing';
COMMENT ON COLUMN public.company_campaigns.color_tag IS 'Color identifier for visual differentiation in calendar views (e.g., "blue", "green", "red")';
COMMENT ON COLUMN public.company_campaigns.posts_per_week IS 'Number of posts to generate per week for this campaign (1-21)';

-- Add check constraint to ensure posts_per_week is within valid range
ALTER TABLE public.company_campaigns ADD CONSTRAINT posts_per_week_range CHECK (posts_per_week >= 1 AND posts_per_week <= 21);
