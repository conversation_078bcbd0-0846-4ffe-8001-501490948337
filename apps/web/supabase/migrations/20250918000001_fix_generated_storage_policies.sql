-- Fix duplicate storage policies for 'generated' bucket
-- This migration resolves RLS policy conflicts that were causing upload failures in production

-- Drop any existing duplicate policies for the 'generated' bucket
-- These might exist if the schema file was applied before the migration
DROP POLICY IF EXISTS "Users can upload images for their accounts" ON storage.objects;
DROP POLICY IF EXISTS "Users can view images for their accounts" ON storage.objects;
DROP POLICY IF EXISTS "Users can update images for their accounts" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete images for their accounts" ON storage.objects;
DROP POLICY IF EXISTS "Service role can manage all generated images" ON storage.objects;

-- Recreate the policies with proper configuration
-- Users can upload images for accounts they have access to
CREATE POLICY "Users can upload images for their accounts"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Users can view images for accounts they have access to
CREATE POLICY "Users can view images for their accounts"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Users can update images for accounts they have access to
CREATE POLICY "Users can update images for their accounts"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Users can delete images for accounts they have access to
CREATE POLICY "Users can delete images for their accounts"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'generated' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Service role can do everything (for server-side uploads)
-- The service role should bypass RLS entirely, but we add this policy for explicit permission
CREATE POLICY "Service role can manage all generated images"
  ON storage.objects FOR ALL
  USING (bucket_id = 'generated')
  WITH CHECK (bucket_id = 'generated');
