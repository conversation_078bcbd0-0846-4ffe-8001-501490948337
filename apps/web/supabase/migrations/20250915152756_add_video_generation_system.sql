-- Migration: Add Video Generation System
-- Description: Adds video generation tracking, job management, and Supabase Storage bucket for AI-generated videos

-- Create video generation models enum
CREATE TYPE public.video_generation_model AS ENUM(
  'veo-3-fast',
  'veo-3-standard'
);

-- Create video generation status enum  
CREATE TYPE public.video_generation_status AS ENUM(
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled'
);

-- Add video generation columns to existing video_messages table
ALTER TABLE public.video_messages 
ADD COLUMN IF NOT EXISTS is_generating BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS is_error BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS video_generation_id TEXT,
ADD COLUMN IF NOT EXISTS video_generation_model public.video_generation_model,
ADD COLUMN IF NOT EXISTS video_generation_status public.video_generation_status,
ADD COLUMN IF NOT EXISTS video_generation_prompt TEXT,
ADD COLUMN IF NOT EXISTS generated_video_url TEXT,
ADD COLUMN IF NOT EXISTS generation_error_message TEXT,
ADD COLUMN IF NOT EXISTS generation_started_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS generation_completed_at TIMESTAMPTZ;

-- Create video generation jobs table for tracking generation operations
CREATE TABLE IF NOT EXISTS public.video_generation_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  message_id UUID REFERENCES public.video_messages(id) ON DELETE CASCADE,
  account_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  
  -- Generation details
  model public.video_generation_model NOT NULL DEFAULT 'veo-3-fast',
  prompt TEXT NOT NULL,
  improved_prompt TEXT, -- AI-improved version of the prompt
  
  -- External service tracking
  external_operation_id TEXT, -- Veo operation ID
  external_operation_name TEXT, -- Veo operation name
  
  -- Status and results
  status public.video_generation_status NOT NULL DEFAULT 'pending',
  error_message TEXT,
  generated_video_url TEXT,
  video_storage_path TEXT, -- Supabase storage path
  
  -- Metadata
  generation_config JSONB DEFAULT '{}', -- Store additional config like aspect ratio, etc.
  
  -- Timestamps
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ
);

-- Enable RLS on video_generation_jobs
ALTER TABLE public.video_generation_jobs ENABLE ROW LEVEL SECURITY;

-- RLS policies for video_generation_jobs
CREATE POLICY "Users can view their own video generation jobs"
  ON public.video_generation_jobs FOR SELECT
  USING (
    user_id = auth.uid() OR
    public.has_role_on_account(account_id)
  );

CREATE POLICY "Users can insert video generation jobs for their accounts"
  ON public.video_generation_jobs FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    public.has_role_on_account(account_id)
  );

CREATE POLICY "Users can update their own video generation jobs"
  ON public.video_generation_jobs FOR UPDATE
  USING (
    user_id = auth.uid() OR
    public.has_role_on_account(account_id)
  );

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_message_id 
  ON public.video_generation_jobs(message_id);
  
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_account_id 
  ON public.video_generation_jobs(account_id);
  
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_user_id 
  ON public.video_generation_jobs(user_id);
  
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_status 
  ON public.video_generation_jobs(status);
  
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_external_operation_id 
  ON public.video_generation_jobs(external_operation_id);

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_video_generation_jobs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_video_generation_jobs_updated_at
  BEFORE UPDATE ON public.video_generation_jobs
  FOR EACH ROW
  EXECUTE FUNCTION public.update_video_generation_jobs_updated_at();

-- Function to get active video generation jobs for polling
CREATE OR REPLACE FUNCTION public.get_active_video_generation_jobs(target_account_id UUID)
RETURNS TABLE(
  id UUID,
  message_id UUID,
  external_operation_id TEXT,
  external_operation_name TEXT,
  status public.video_generation_status,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  -- Check if user has access to this account
  IF NOT public.has_role_on_account(target_account_id) THEN
    RAISE EXCEPTION 'Access denied';
  END IF;

  RETURN QUERY
  SELECT 
    vgj.id,
    vgj.message_id,
    vgj.external_operation_id,
    vgj.external_operation_name,
    vgj.status,
    vgj.created_at
  FROM public.video_generation_jobs vgj
  WHERE vgj.account_id = target_account_id
    AND vgj.status IN ('pending', 'processing')
  ORDER BY vgj.created_at DESC;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.get_active_video_generation_jobs(UUID) TO authenticated;

-- Create storage bucket for generated videos (if not exists)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'videos',
  'videos',
  true,
  *********, -- 100MB limit
  ARRAY['video/mp4', 'video/webm', 'video/quicktime']::text[]
)
ON CONFLICT (id) DO NOTHING;


