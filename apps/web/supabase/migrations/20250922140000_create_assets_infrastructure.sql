-- Create comprehensive assets infrastructure
-- This migration combines brand-assets bucket creation and assets table RLS policy fixes
-- Modern web best practices: Higher file limits, comprehensive MIME types, performance optimizations

-- 1. Create the brand-assets storage bucket
-- Modern file size limits (500MB) to accommodate high-resolution media and large documents
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'brand-assets',
  'brand-assets',
  true,
  524288000, -- 500MB limit for modern media files
  ARRAY[
    -- Images (modern formats)
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif',
    'image/svg+xml',
    'image/avif',
    'image/heic',
    'image/heif',

    -- Videos (modern formats)
    'video/mp4',
    'video/webm',
    'video/quicktime',
    'video/x-msvideo',
    'video/3gpp',
    'video/h264',

    -- Audio
    'audio/mpeg',
    'audio/wav',
    'audio/mp3',
    'audio/aac',
    'audio/ogg',
    'audio/flac',
    'audio/m4a',

    -- Documents
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'text/csv',
    'text/html',
    'text/markdown',
    'application/json',

    -- Design files
    'application/x-figma',
    'application/x-sketch',
    'application/x-adobe-indesign',
    'application/x-adobe-illustrator',
    'application/x-adobe-photoshop',
    'application/x-xd',

    -- Fonts (modern web fonts)
    'font/woff',
    'font/woff2',
    'font/ttf',
    'font/otf',
    'application/font-woff',
    'application/font-woff2',

    -- Archives
    'application/zip',
    'application/x-7z-compressed',
    'application/gzip',

    -- 3D models
    'model/gltf-binary',
    'model/gltf+json',
    'application/octet-stream'
  ]::text[]
)
ON CONFLICT (id) DO NOTHING;

-- 2. Grant necessary permissions for the bucket (if not already granted)
-- Note: Some permissions may already be granted by earlier migrations
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.role_table_grants
    WHERE table_name = 'objects' AND table_schema = 'storage' AND grantee = 'authenticated'
  ) THEN
    GRANT ALL ON storage.objects TO authenticated;
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.role_table_grants
    WHERE table_name = 'objects' AND table_schema = 'storage' AND grantee = 'service_role'
  ) THEN
    GRANT ALL ON storage.objects TO service_role;
  END IF;
END $$;

-- 3. Create RLS policies for the brand-assets bucket
-- Note: DELETE policy already exists in migration 20250919201500_fix_brand_assets_delete_policies.sql

-- SELECT policy
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'objects' AND schemaname = 'storage'
    AND policyname = 'Users can view brand assets for their accounts'
  ) THEN
    CREATE POLICY "Users can view brand assets for their accounts"
    ON storage.objects
    FOR SELECT
    TO public
    USING (
      bucket_id = 'brand-assets'::text
      AND (storage.foldername(name))[1] IN (
        SELECT (accounts.id)::text AS id
        FROM accounts
        WHERE accounts.id IN (
          SELECT accounts_memberships.account_id
          FROM accounts_memberships
          WHERE accounts_memberships.user_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- INSERT policy
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'objects' AND schemaname = 'storage'
    AND policyname = 'Users can upload brand assets to their accounts'
  ) THEN
    CREATE POLICY "Users can upload brand assets to their accounts"
    ON storage.objects
    FOR INSERT
    TO public
    WITH CHECK (
      bucket_id = 'brand-assets'::text
      AND (storage.foldername(name))[1] IN (
        SELECT (accounts.id)::text AS id
        FROM accounts
        WHERE accounts.id IN (
          SELECT accounts_memberships.account_id
          FROM accounts_memberships
          WHERE accounts_memberships.user_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- UPDATE policy
DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies
    WHERE tablename = 'objects' AND schemaname = 'storage'
    AND policyname = 'Users can update brand assets for their accounts'
  ) THEN
    CREATE POLICY "Users can update brand assets for their accounts"
    ON storage.objects
    FOR UPDATE
    TO public
    USING (
      bucket_id = 'brand-assets'::text
      AND (storage.foldername(name))[1] IN (
        SELECT (accounts.id)::text AS id
        FROM accounts
        WHERE accounts.id IN (
          SELECT accounts_memberships.account_id
          FROM accounts_memberships
          WHERE accounts_memberships.user_id = auth.uid()
        )
      )
    )
    WITH CHECK (
      bucket_id = 'brand-assets'::text
      AND (storage.foldername(name))[1] IN (
        SELECT (accounts.id)::text AS id
        FROM accounts
        WHERE accounts.id IN (
          SELECT accounts_memberships.account_id
          FROM accounts_memberships
          WHERE accounts_memberships.user_id = auth.uid()
        )
      )
    );
  END IF;
END $$;

-- Note: DELETE policy for brand-assets already exists in migration 20250919201500_fix_brand_assets_delete_policies.sql
-- Skipping duplicate policy creation to avoid conflicts

-- Note: Assets table RLS policies are handled in the assets table creation migration
-- (20250922152815_add_assets_table.sql) to ensure proper ordering
