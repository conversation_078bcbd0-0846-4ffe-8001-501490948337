-- Fix critical security vulnerability: Make storage buckets private
-- This migration addresses the critical issue where brand-assets and generated buckets
-- were configured as public, allowing unauthorized access to team assets

-- Make brand-assets bucket private
UPDATE storage.buckets 
SET public = false 
WHERE id = 'brand-assets';

-- Make generated bucket private  
UPDATE storage.buckets 
SET public = false 
WHERE id = 'generated';

-- Verify the changes
DO $$ 
BEGIN
  -- Check that buckets are now private
  IF EXISTS (
    SELECT 1 FROM storage.buckets 
    WHERE id IN ('brand-assets', 'generated') 
    AND public = true
  ) THEN
    RAISE EXCEPTION 'Failed to make storage buckets private - security vulnerability still exists';
  END IF;
  
  RAISE NOTICE 'Storage buckets successfully made private - security vulnerability fixed';
END $$;
