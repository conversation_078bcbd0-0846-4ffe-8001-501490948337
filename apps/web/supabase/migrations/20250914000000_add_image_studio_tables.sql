-- Create image_conversations table
CREATE TABLE IF NOT EXISTS public.image_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    image_path TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Create image_messages table
CREATE TABLE IF NOT EXISTS public.image_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('assistant', 'user', 'system')),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    conversation_id UUID NOT NULL REFERENCES public.image_conversations(id) ON DELETE CASCADE,
    image_path TEXT,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Enable RLS on both tables
ALTER TABLE public.image_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.image_messages ENABLE ROW LEVEL SECURITY;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_image_conversations_company_id ON public.image_conversations(company_id);
CREATE INDEX IF NOT EXISTS idx_image_conversations_user_id ON public.image_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_image_conversations_created_at ON public.image_conversations(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_image_messages_conversation_id ON public.image_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_image_messages_company_id ON public.image_messages(company_id);
CREATE INDEX IF NOT EXISTS idx_image_messages_user_id ON public.image_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_image_messages_created_at ON public.image_messages(created_at DESC);

-- Add triggers for updated_at timestamp
CREATE TRIGGER set_image_conversations_updated_at
    BEFORE UPDATE ON public.image_conversations
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_set_timestamps();

-- RLS Policies for image_conversations
-- Users can view conversations from their company
CREATE POLICY "image_conversations_select_policy" ON public.image_conversations
    FOR SELECT
    TO authenticated
    USING (
        company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
        OR user_id = auth.uid()
    );

-- Users can insert conversations for their company
CREATE POLICY "image_conversations_insert_policy" ON public.image_conversations
    FOR INSERT
    TO authenticated
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- Users can update conversations they created in their company
CREATE POLICY "image_conversations_update_policy" ON public.image_conversations
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    )
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- Users can delete conversations they created in their company
CREATE POLICY "image_conversations_delete_policy" ON public.image_conversations
    FOR DELETE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for image_messages
-- Users can view messages from conversations in their company
CREATE POLICY "image_messages_select_policy" ON public.image_messages
    FOR SELECT
    TO authenticated
    USING (
        company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
        OR user_id = auth.uid()
    );

-- Users can insert messages for conversations in their company
CREATE POLICY "image_messages_insert_policy" ON public.image_messages
    FOR INSERT
    TO authenticated
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
        AND conversation_id IN (
            SELECT id 
            FROM public.image_conversations 
            WHERE company_id IN (
                SELECT account_id 
                FROM public.accounts_memberships 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Users can update messages they created in their company
CREATE POLICY "image_messages_update_policy" ON public.image_messages
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    )
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- Users can delete messages they created in their company
CREATE POLICY "image_messages_delete_policy" ON public.image_messages
    FOR DELETE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );
