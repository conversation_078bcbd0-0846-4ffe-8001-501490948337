-- Create video_conversations table
CREATE TABLE IF NOT EXISTS public.video_conversations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    video_project_id UUID REFERENCES public.video_projects(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Create video_messages table
CREATE TABLE IF NOT EXISTS public.video_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('assistant', 'user', 'system')),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    conversation_id UUID NOT NULL REFERENCES public.video_conversations(id) ON DELETE CASCADE,
    video_project_id UUID REFERENCES public.video_projects(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Enable RLS on both tables
ALTER TABLE public.video_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_messages ENABLE ROW LEVEL SECURITY;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_video_conversations_company_id ON public.video_conversations(company_id);
CREATE INDEX IF NOT EXISTS idx_video_conversations_user_id ON public.video_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_video_conversations_video_project_id ON public.video_conversations(video_project_id);
CREATE INDEX IF NOT EXISTS idx_video_conversations_created_at ON public.video_conversations(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_video_messages_conversation_id ON public.video_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_video_messages_company_id ON public.video_messages(company_id);
CREATE INDEX IF NOT EXISTS idx_video_messages_user_id ON public.video_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_video_messages_video_project_id ON public.video_messages(video_project_id);
CREATE INDEX IF NOT EXISTS idx_video_messages_created_at ON public.video_messages(created_at DESC);

-- Add triggers for updated_at timestamp
CREATE TRIGGER set_video_conversations_updated_at
    BEFORE UPDATE ON public.video_conversations
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_set_timestamps();

-- RLS Policies for video_conversations
-- Users can view conversations from their company
CREATE POLICY "video_conversations_select_policy" ON public.video_conversations
    FOR SELECT
    TO authenticated
    USING (
        company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
        OR user_id = auth.uid()
    );

-- Users can insert conversations for their company
CREATE POLICY "video_conversations_insert_policy" ON public.video_conversations
    FOR INSERT
    TO authenticated
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- Users can update conversations they created in their company
CREATE POLICY "video_conversations_update_policy" ON public.video_conversations
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    )
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- Users can delete conversations they created in their company
CREATE POLICY "video_conversations_delete_policy" ON public.video_conversations
    FOR DELETE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for video_messages
-- Users can view messages from conversations in their company
CREATE POLICY "video_messages_select_policy" ON public.video_messages
    FOR SELECT
    TO authenticated
    USING (
        company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
        OR user_id = auth.uid()
    );

-- Users can insert messages for conversations in their company
CREATE POLICY "video_messages_insert_policy" ON public.video_messages
    FOR INSERT
    TO authenticated
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
        AND conversation_id IN (
            SELECT id 
            FROM public.video_conversations 
            WHERE company_id IN (
                SELECT account_id 
                FROM public.accounts_memberships 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Users can update messages they created in their company
CREATE POLICY "video_messages_update_policy" ON public.video_messages
    FOR UPDATE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    )
    WITH CHECK (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );

-- Users can delete messages they created in their company
CREATE POLICY "video_messages_delete_policy" ON public.video_messages
    FOR DELETE
    TO authenticated
    USING (
        user_id = auth.uid()
        AND company_id IN (
            SELECT account_id 
            FROM public.accounts_memberships 
            WHERE user_id = auth.uid()
        )
    );
