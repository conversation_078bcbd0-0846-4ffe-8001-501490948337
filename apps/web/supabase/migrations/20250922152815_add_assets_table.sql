-- Assets table to track all uploaded files with metadata
CREATE TABLE if not exists public.assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE NOT NULL,

  -- File information
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL, -- Path in Supabase Storage
  file_size BIGINT, -- File size in bytes
  file_type TEXT, -- MIME type (e.g., 'video/mp4', 'audio/mpeg', 'image/png')
  folder_name TEXT NOT NULL, -- Folder within the asset library

  -- Asset metadata
  original_url TEXT, -- Public URL of the original file
  file_extension TEXT, -- File extension (e.g., 'mp4', 'mp3', 'png')

  -- Transcript information (for audio/video files)
  transcript_path TEXT, -- Path to transcript file in storage
  transcript_url TEXT, -- Public URL of transcript file
  has_transcript BOOLEAN DEFAULT FALSE,
  transcript_extracted_at TIMESTAMPTZ,

  -- Processing status
  processing_status TEXT DEFAULT 'completed' CHECK (processing_status IN ('processing', 'completed', 'failed')),
  processing_started_at TIMESTAMPTZ,
  processing_completed_at TIMESTAMPTZ,
  processing_error TEXT,

  -- Timestamps
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  UNIQUE(account_id, file_path) -- Prevent duplicate entries for same file
);

-- Privileges: follow established pattern (orders/accounts)
-- Revoke all then grant explicit privileges controlled by RLS
REVOKE ALL ON public.assets FROM authenticated, service_role;

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.assets TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.assets TO service_role;


-- Enable RLS
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Fixed to use correct logic
-- Note: account_id = auth.uid() is wrong because account_id is a team/personal account ID, not user ID
-- The correct logic checks if user is the primary owner of the account OR has a role on the account

CREATE POLICY "assets_select" ON public.assets
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.accounts
    WHERE id = assets.account_id
    AND (
      primary_owner_user_id = auth.uid()
      OR public.has_role_on_account(assets.account_id)
    )
  )
);

CREATE POLICY "assets_insert" ON public.assets
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.accounts
    WHERE id = account_id
    AND (
      primary_owner_user_id = auth.uid()
      OR public.has_role_on_account(account_id)
    )
  )
);

CREATE POLICY "assets_update" ON public.assets
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.accounts
    WHERE id = assets.account_id
    AND (
      primary_owner_user_id = auth.uid()
      OR public.has_role_on_account(assets.account_id)
    )
  )
) WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.accounts
    WHERE id = account_id
    AND (
      primary_owner_user_id = auth.uid()
      OR public.has_role_on_account(account_id)
    )
  )
);

CREATE POLICY "assets_delete" ON public.assets
FOR DELETE USING (
  EXISTS (
    SELECT 1 FROM public.accounts
    WHERE id = assets.account_id
    AND (
      primary_owner_user_id = auth.uid()
      OR public.has_role_on_account(assets.account_id)
    )
  )
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS assets_account_id_idx ON public.assets(account_id);
CREATE INDEX IF NOT EXISTS assets_folder_name_idx ON public.assets(account_id, folder_name);
CREATE INDEX IF NOT EXISTS assets_file_type_idx ON public.assets(file_type);
CREATE INDEX IF NOT EXISTS assets_has_transcript_idx ON public.assets(has_transcript);
CREATE INDEX IF NOT EXISTS assets_processing_status_idx ON public.assets(processing_status);
CREATE INDEX IF NOT EXISTS assets_uploaded_at_idx ON public.assets(uploaded_at DESC);

