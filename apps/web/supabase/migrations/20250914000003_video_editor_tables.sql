-- Video Editor Database Schema
-- Creates tables for storing video projects, overlays, assets, autosaves, and renders

-- Video Projects table
CREATE TABLE IF NOT EXISTS public.video_projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  description TEXT,
  aspect_ratio VARCHAR(10) DEFAULT '16:9',
  duration_frames INTEGER DEFAULT 0,
  fps INTEGER DEFAULT 30,
  width INTEGER DEFAULT 1920,
  height INTEGER DEFAULT 1080,
  thumbnail_url TEXT,
  status VARCHAR(50) DEFAULT 'draft', -- draft, rendering, completed, failed
  is_template BOOLEAN DEFAULT FALSE,
  template_category VARCHAR(100),
  template_tags TEXT[],
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_by <PERSON>UI<PERSON> REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

-- Video Project Overlays table (stores all overlay data)
CREATE TABLE IF NOT EXISTS public.video_project_overlays (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES public.video_projects(id) ON DELETE CASCADE,
  overlay_id INTEGER NOT NULL, -- Internal overlay ID from editor
  overlay_type VARCHAR(50) NOT NULL, -- text, image, video, sound, etc.
  layer_order INTEGER NOT NULL DEFAULT 0,
  row_position INTEGER NOT NULL DEFAULT 0,
  from_frame INTEGER NOT NULL DEFAULT 0,
  duration_frames INTEGER NOT NULL DEFAULT 30,
  position_x DECIMAL(10,4) DEFAULT 0,
  position_y DECIMAL(10,4) DEFAULT 0,
  width DECIMAL(10,4) DEFAULT 100,
  height DECIMAL(10,4) DEFAULT 100,
  rotation DECIMAL(8,4) DEFAULT 0,
  content TEXT, -- text content, asset path, etc.
  src TEXT, -- source URL for media assets
  styles JSONB DEFAULT '{}', -- all styling properties
  animation_config JSONB DEFAULT '{}', -- animation settings
  metadata JSONB DEFAULT '{}', -- type-specific data
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Video Project Assets table (for uploaded/linked media files)
CREATE TABLE IF NOT EXISTS public.video_project_assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES public.video_projects(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  asset_type VARCHAR(50) NOT NULL, -- image, video, audio, sticker
  original_filename VARCHAR(500),
  file_path TEXT NOT NULL, -- Supabase Storage path
  file_size BIGINT,
  mime_type VARCHAR(200),
  duration_seconds DECIMAL(10,4), -- for audio/video
  thumbnail_path TEXT, -- for video thumbnails
  metadata JSONB DEFAULT '{}',
  is_external BOOLEAN DEFAULT FALSE, -- true for Pexels/external assets
  external_source VARCHAR(100), -- pexels, unsplash, etc.
  external_id VARCHAR(200),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Video Project Autosaves table
CREATE TABLE IF NOT EXISTS public.video_project_autosaves (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES public.video_projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  editor_state JSONB NOT NULL,
  save_type VARCHAR(50) DEFAULT 'auto', -- auto, manual, checkpoint
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Video Renders table (for tracking render jobs and outputs)
CREATE TABLE IF NOT EXISTS public.video_renders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID NOT NULL REFERENCES public.video_projects(id) ON DELETE CASCADE,
  account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  render_id VARCHAR(200) UNIQUE, -- External render service ID
  status VARCHAR(50) DEFAULT 'queued', -- queued, rendering, completed, failed
  progress INTEGER DEFAULT 0,
  output_url TEXT,
  output_size_bytes BIGINT,
  error_message TEXT,
  render_settings JSONB DEFAULT '{}',
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_video_projects_account_id ON public.video_projects(account_id);
CREATE INDEX IF NOT EXISTS idx_video_projects_user_id ON public.video_projects(user_id);
CREATE INDEX IF NOT EXISTS idx_video_projects_status ON public.video_projects(status);
CREATE INDEX IF NOT EXISTS idx_video_projects_updated_at ON public.video_projects(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_video_project_overlays_project_id ON public.video_project_overlays(project_id);
CREATE INDEX IF NOT EXISTS idx_video_project_overlays_layer_order ON public.video_project_overlays(project_id, layer_order);

CREATE INDEX IF NOT EXISTS idx_video_project_assets_project_id ON public.video_project_assets(project_id);
CREATE INDEX IF NOT EXISTS idx_video_project_assets_account_id ON public.video_project_assets(account_id);

CREATE INDEX IF NOT EXISTS idx_video_project_autosaves_project_user ON public.video_project_autosaves(project_id, user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_video_renders_project_id ON public.video_renders(project_id);
CREATE INDEX IF NOT EXISTS idx_video_renders_status ON public.video_renders(status);

-- Enable RLS on all tables
ALTER TABLE public.video_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_project_overlays ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_project_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_project_autosaves ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.video_renders ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Users can only access projects within their accounts
CREATE POLICY "Users can access video projects within their accounts"
  ON public.video_projects FOR ALL
  USING (
    account_id IN (
      SELECT account_id FROM public.accounts_memberships
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can access overlays for accessible projects"
  ON public.video_project_overlays FOR ALL
  USING (
    project_id IN (
      SELECT id FROM public.video_projects
      WHERE account_id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can access assets for accessible projects"
  ON public.video_project_assets FOR ALL
  USING (
    project_id IN (
      SELECT id FROM public.video_projects
      WHERE account_id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can access autosaves for their own projects"
  ON public.video_project_autosaves FOR ALL
  USING (
    user_id = auth.uid() AND
    project_id IN (
      SELECT id FROM public.video_projects
      WHERE account_id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can access renders for accessible projects"
  ON public.video_renders FOR ALL
  USING (
    project_id IN (
      SELECT id FROM public.video_projects
      WHERE account_id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Add triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION public.set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_video_projects_updated_at
  BEFORE UPDATE ON public.video_projects
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

CREATE TRIGGER set_video_project_overlays_updated_at
  BEFORE UPDATE ON public.video_project_overlays
  FOR EACH ROW
  EXECUTE FUNCTION public.set_updated_at();

-- Comments for documentation
COMMENT ON TABLE public.video_projects IS 'Video editor projects with metadata and settings';
COMMENT ON TABLE public.video_project_overlays IS 'Individual overlay elements within video projects';
COMMENT ON TABLE public.video_project_assets IS 'Media assets used in video projects';
COMMENT ON TABLE public.video_project_autosaves IS 'Autosaved editor states for recovery';
COMMENT ON TABLE public.video_renders IS 'Video render jobs and their outputs';