-- Migration: Add selected video project tracking to user_cache
-- This allows us to remember the last selected video project per user

-- Add selected_video_project column to user_cache table
ALTER TABLE user_cache
ADD COLUMN selected_video_project UUID REFERENCES video_projects(id) ON DELETE SET NULL;

-- Create index for better query performance
CREATE INDEX idx_user_cache_selected_video_project
ON user_cache(selected_video_project)
WHERE selected_video_project IS NOT NULL;