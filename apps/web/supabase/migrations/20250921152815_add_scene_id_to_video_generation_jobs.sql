-- Migration: Add scene_id to video_generation_jobs table
-- Description: Adds scene_id column to video_generation_jobs with foreign key reference to video_project_scenes.id

-- Add scene_id column to video_generation_jobs table
ALTER TABLE public.video_generation_jobs
ADD COLUMN IF NOT EXISTS scene_id uuid;

-- Add foreign key constraint to reference video_project_scenes.id
ALTER TABLE public.video_generation_jobs
ADD CONSTRAINT fk_video_generation_jobs_scene_id
FOREIGN KEY (scene_id) REFERENCES public.video_project_scenes(id) ON DELETE SET NULL;

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_video_generation_jobs_scene_id
ON public.video_generation_jobs(scene_id);

-- Add helpful comment
COMMENT ON COLUMN public.video_generation_jobs.scene_id IS 'References the video project scene this generation job belongs to';