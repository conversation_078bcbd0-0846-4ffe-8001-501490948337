-- Ensure UUID generator is available
create extension if not exists pgcrypto;

-- Add a primary key to public.config so Zero can sync it
alter table public.config add column if not exists id uuid default gen_random_uuid();

-- Set replica identity to FULL before any updates to handle logical replication
alter table public.config replica identity full;

-- Update existing rows that don't have an ID (using coalesce for safety)
update public.config
set id = coalesce(id, gen_random_uuid())
where id is null;

-- Make the id column NOT NULL
alter table public.config alter column id set not null;

-- Add primary key constraint if it doesn't exist
do $$
begin
  if not exists (
    select 1
    from pg_constraint
    where conrelid = 'public.config'::regclass
      and contype = 'p'
  ) then
    alter table public.config add primary key (id);
  end if;
end $$;

-- Now that we have a primary key, restore default replica identity
alter table public.config replica identity default;

