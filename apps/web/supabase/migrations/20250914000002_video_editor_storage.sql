-- Video Editor Storage Setup
-- Creates storage buckets and policies for video project assets and renders

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES
  ('video-project-assets', 'video-project-assets', false, *********, ARRAY['image/*', 'video/*', 'audio/*']), -- 100MB limit
  ('video-project-thumbnails', 'video-project-thumbnails', true, 5000000, ARRAY['image/*']), -- 5MB limit for thumbnails
  ('video-renders', 'video-renders', false, *********, ARRAY['video/*']) -- 500MB limit for rendered videos
ON CONFLICT (id) DO NOTHING;

-- Storage policies for video-project-assets bucket
CREATE POLICY "Users can upload assets for their accounts"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'video-project-assets' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can view assets for their accounts"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'video-project-assets' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can update assets for their accounts"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'video-project-assets' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can delete assets for their accounts"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'video-project-assets' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Storage policies for video-project-thumbnails bucket (public read)
CREATE POLICY "Anyone can view thumbnails"
  ON storage.objects FOR SELECT
  USING (bucket_id = 'video-project-thumbnails');

CREATE POLICY "Users can upload thumbnails for their accounts"
  ON storage.objects FOR INSERT
  WITH CHECK (
    bucket_id = 'video-project-thumbnails' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can update thumbnails for their accounts"
  ON storage.objects FOR UPDATE
  USING (
    bucket_id = 'video-project-thumbnails' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can delete thumbnails for their accounts"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'video-project-thumbnails' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

-- Storage policies for video-renders bucket
CREATE POLICY "Users can view renders for their accounts"
  ON storage.objects FOR SELECT
  USING (
    bucket_id = 'video-renders' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "System can upload renders"
  ON storage.objects FOR INSERT
  WITH CHECK (bucket_id = 'video-renders');

CREATE POLICY "System can update renders"
  ON storage.objects FOR UPDATE
  USING (bucket_id = 'video-renders');

CREATE POLICY "Users can delete renders for their accounts"
  ON storage.objects FOR DELETE
  USING (
    bucket_id = 'video-renders' AND
    (storage.foldername(name))[1] IN (
      SELECT id::text FROM public.accounts
      WHERE id IN (
        SELECT account_id FROM public.accounts_memberships
        WHERE user_id = auth.uid()
      )
    )
  );