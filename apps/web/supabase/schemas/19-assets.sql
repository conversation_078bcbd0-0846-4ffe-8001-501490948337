/*
 * -------------------------------------------------------
 * Section: Assets
 * Assets table for storing uploaded files with proper RLS policies
 * Modern web best practices: ENUMs for status, better indexing, larger file support
 * -------------------------------------------------------
 */

-- Asset processing status enum for better type safety
CREATE TYPE public.asset_processing_status AS ENUM (
  'pending',
  'processing',
  'completed',
  'failed',
  'cancelled'
);

-- Asset type enum for better categorization
CREATE TYPE public.asset_category AS ENUM (
  'image',
  'video',
  'audio',
  'document',
  'design',
  'font',
  'archive',
  'model',
  'other'
);

-- Assets table with modern best practices
CREATE TABLE IF NOT EXISTS public.assets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  account_id UUID REFERENCES public.accounts(id) ON DELETE CASCADE NOT NULL,

  -- File information
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size BIGINT DEFAULT 0, -- Modern: Allow larger files
  file_type TEXT NOT NULL, -- Required for better categorization
  folder_name TEXT NOT NULL,
  category public.asset_category DEFAULT 'other',

  -- Asset metadata
  original_url TEXT,
  file_extension TEXT,
  mime_type TEXT, -- Modern: Store MIME type explicitly

  -- Transcript information (for audio/video files)
  transcript_path TEXT,
  transcript_url TEXT,
  has_transcript BOOLEAN DEFAULT FALSE,
  transcript_extracted_at TIMESTAMPTZ,

  -- Processing status (using ENUM for type safety)
  processing_status public.asset_processing_status DEFAULT 'completed',
  processing_started_at TIMESTAMPTZ,
  processing_completed_at TIMESTAMPTZ,
  processing_error TEXT,

  -- Modern: Add metadata for better search and organization
  metadata JSONB DEFAULT '{}', -- Store additional file metadata
  tags TEXT[] DEFAULT '{}', -- Array of tags for better organization
  description TEXT, -- Optional description

  -- Timestamps
  uploaded_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  accessed_at TIMESTAMPTZ DEFAULT NOW(), -- Modern: Track last access

  -- Constraints
  UNIQUE(account_id, file_path),
  CHECK (file_size >= 0), -- Prevent negative file sizes
  CHECK (file_name != ''), -- Prevent empty file names
  CHECK (file_path != '')  -- Prevent empty file paths
);

-- Enable RLS
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;

-- Note: RLS policies are now handled in migration 20250922152815_add_assets_table.sql
-- to ensure proper ordering and avoid conflicts during schema initialization

-- Modern indexing strategy for performance
-- Primary access patterns
CREATE INDEX IF NOT EXISTS assets_account_id_idx ON public.assets(account_id);
CREATE INDEX IF NOT EXISTS assets_account_folder_idx ON public.assets(account_id, folder_name);
CREATE INDEX IF NOT EXISTS assets_account_uploaded_idx ON public.assets(account_id, uploaded_at DESC);

-- Content search and filtering
CREATE INDEX IF NOT EXISTS assets_file_type_idx ON public.assets(file_type);
CREATE INDEX IF NOT EXISTS assets_category_idx ON public.assets(category);
CREATE INDEX IF NOT EXISTS assets_mime_type_idx ON public.assets(mime_type);
CREATE INDEX IF NOT EXISTS assets_has_transcript_idx ON public.assets(has_transcript);

-- Processing and status
CREATE INDEX IF NOT EXISTS assets_processing_status_idx ON public.assets(processing_status) WHERE processing_status IN ('processing', 'pending');
CREATE INDEX IF NOT EXISTS assets_failed_processing_idx ON public.assets(processing_status) WHERE processing_status = 'failed';

-- Modern: Full-text search on tags and description
CREATE INDEX IF NOT EXISTS assets_tags_gin_idx ON public.assets USING GIN (tags);
CREATE INDEX IF NOT EXISTS assets_metadata_gin_idx ON public.assets USING GIN (metadata);

-- Modern: Partial indexes for common queries
CREATE INDEX IF NOT EXISTS assets_recent_idx ON public.assets(uploaded_at DESC) WHERE uploaded_at > NOW() - INTERVAL '30 days';
CREATE INDEX IF NOT EXISTS assets_large_files_idx ON public.assets(file_size DESC) WHERE file_size > *********; -- Files > 100MB

-- Modern: Access pattern optimization
CREATE INDEX IF NOT EXISTS assets_account_accessed_idx ON public.assets(account_id, accessed_at DESC);

-- Composite indexes for complex queries
CREATE INDEX IF NOT EXISTS assets_account_category_uploaded_idx ON public.assets(account_id, category, uploaded_at DESC);
CREATE INDEX IF NOT EXISTS assets_account_processing_category_idx ON public.assets(account_id, processing_status, category) WHERE processing_status != 'completed';

-- Modern: Automatic timestamp updates
CREATE OR REPLACE FUNCTION public.update_assets_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  NEW.accessed_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER assets_updated_at_trigger
  BEFORE UPDATE ON public.assets
  FOR EACH ROW
  EXECUTE FUNCTION public.update_assets_updated_at();

-- Modern: Helper function to get asset category from MIME type
CREATE OR REPLACE FUNCTION public.get_asset_category_from_mime(mime_type TEXT)
RETURNS public.asset_category AS $$
BEGIN
  IF mime_type IS NULL THEN
    RETURN 'other';
  END IF;

  CASE
    WHEN mime_type LIKE 'image/%' THEN RETURN 'image';
    WHEN mime_type LIKE 'video/%' THEN RETURN 'video';
    WHEN mime_type LIKE 'audio/%' THEN RETURN 'audio';
    WHEN mime_type IN ('application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.%', 'text/%') THEN RETURN 'document';
    WHEN mime_type LIKE 'font/%' OR mime_type LIKE 'application/font-%' THEN RETURN 'font';
    WHEN mime_type IN ('application/zip', 'application/x-7z-compressed', 'application/gzip') THEN RETURN 'archive';
    WHEN mime_type LIKE 'model/%' THEN RETURN 'model';
    WHEN mime_type LIKE 'application/x-%' THEN RETURN 'design';
    ELSE RETURN 'other';
  END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Modern: Helper function to extract file extension
CREATE OR REPLACE FUNCTION public.extract_file_extension(file_path TEXT)
RETURNS TEXT AS $$
BEGIN
  IF file_path IS NULL THEN
    RETURN NULL;
  END IF;

  RETURN LOWER(SUBSTRING(file_path FROM '\.([^\.]*)$'));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Modern: Helper function to clean file names
CREATE OR REPLACE FUNCTION public.clean_file_name(file_name TEXT)
RETURNS TEXT AS $$
BEGIN
  IF file_name IS NULL THEN
    RETURN NULL;
  END IF;

  -- Remove or replace invalid characters for file names
  RETURN REGEXP_REPLACE(
    REGEXP_REPLACE(file_name, '[<>:"/\\|?*]', '_', 'g'),
    '__+',
    '_',
    'g'
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;
