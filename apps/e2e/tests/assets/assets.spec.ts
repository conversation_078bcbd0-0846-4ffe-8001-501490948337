import { Page, expect, test } from '@playwright/test';

import { AuthPageObject } from '../authentication/auth.po';
import { TeamAccountsPageObject } from '../team-accounts/team-accounts.po';

interface AssetTestContext {
  ownerPage: Page;
  memberPage: Page;
  ownerEmail: string;
  memberEmail: string;
  teamSlug: string;
  ownerAssets: TeamAccountsPageObject;
  memberAssets: TeamAccountsPageObject;
}

// Helper function to set up a team with assets for testing RLS policies
async function setupTeamWithAssets(browser: any): Promise<AssetTestContext> {
  // Create first context for team owner
  const ownerContext = await browser.newContext();
  const ownerPage = await ownerContext.newPage();
  const ownerAssets = new TeamAccountsPageObject(ownerPage);

  // Setup team owner and create team
  const { email: ownerEmail, slug } = await ownerAssets.setup();

  // Navigate to the team
  await ownerPage.goto(`/home/<USER>

  // Create second context for team member
  const memberContext = await browser.newContext();
  const memberPage = await memberContext.newPage();
  const memberAssets = new TeamAccountsPageObject(memberPage);

  // Setup team member and join the team
  const memberEmail = memberAssets.auth.createRandomEmail();

  // Sign out owner temporarily to sign up member
  await ownerContext.clearCookies();

  // Sign up member
  await memberPage.goto('/auth/sign-up');
  await memberAssets.auth.signUp({
    email: memberEmail,
    password: 'password',
    repeatPassword: 'password',
  });
  await memberAssets.auth.visitConfirmEmailLink(memberEmail);

  // Accept invitation from member
  await memberPage.goto('/join');
  await memberPage.fill('[data-test="invitation-token"]', ownerEmail);
  await memberPage.click('[data-test="accept-invitation"]');

  // Sign back in as owner
  await ownerPage.goto('/auth/sign-in');
  await ownerAssets.auth.signIn({
    email: ownerEmail,
    password: 'password',
  });

  return {
    ownerPage,
    memberPage,
    ownerEmail,
    memberEmail,
    teamSlug: slug,
    ownerAssets,
    memberAssets,
  };
}

test.describe('Assets RLS Policies', () => {
  let context: AssetTestContext;

  test.beforeAll(async ({ browser }) => {
    context = await setupTeamWithAssets(browser);
  });

  test.afterAll(async () => {
    await context.ownerPage.context().close();
    await context.memberPage.context().close();
  });

  test('owner can create and access assets', async () => {
    // Navigate to assets page as owner
    await context.ownerPage.goto(`/home/<USER>/assets`);

    // Create a test asset
    const assetData = {
      file_name: 'test-image.png',
      file_path: `test-${Date.now()}.png`,
      file_type: 'image/png',
      folder_name: 'test-folder',
    };

    // This would typically be done through the UI, but for RLS testing
    // we'll test the database policies directly if we have database access
    // For now, we'll assume the UI works correctly with proper permissions
    await expect(context.ownerPage.locator('body')).toBeVisible();
  });

  test('team member can access team assets', async () => {
    // Navigate to assets page as member
    await context.memberPage.goto(`/home/<USER>/assets`);

    // Member should be able to access the assets page
    await expect(context.memberPage.locator('body')).toBeVisible();
  });

  test('user cannot access assets from teams they do not belong to', async () => {
    // Create a second team with a different owner
    const secondOwnerContext = await context.ownerPage.context().browser()?.newContext();
    if (!secondOwnerContext) throw new Error('Could not create new context');

    const secondOwnerPage = await secondOwnerContext.newPage();
    const secondOwnerAssets = new TeamAccountsPageObject(secondOwnerPage);

    // Setup second team
    const { email: secondOwnerEmail, slug: secondTeamSlug } = await secondOwnerAssets.setup();

    // Try to access second team's assets as the first team's member
    await context.memberPage.goto(`/home/<USER>/assets`);

    // Should be redirected to home page (no access)
    await expect(context.memberPage).toHaveURL('/home');

    await secondOwnerContext.close();
  });

  test('RLS policies prevent cross-team data access at database level', async () => {
    // This test would require direct database access to verify RLS policies
    // For now, we'll document that this should be tested with:
    // 1. Direct SQL queries to verify policies work
    // 2. API endpoint testing with different user contexts
    // 3. Verifying that users can only see assets from their own accounts/teams

    // The RLS policies should ensure:
    // - Personal account users can only access assets where account_id = their user_id
    // - Team account users can only access assets where they have a role on the account
    // - No cross-team data leakage occurs

    expect(true).toBe(true); // Placeholder - actual DB testing needed
  });

  test('owner has full CRUD access to all team assets', async () => {
    // Navigate to assets page as owner
    await context.ownerPage.goto(`/home/<USER>/assets`);

    // Owner should have full access to create, read, update, delete assets
    await expect(context.ownerPage.locator('body')).toBeVisible();

    // This test assumes the UI properly implements the permissions
    // The actual permissions are enforced at the database level by RLS policies
  });

  test('member access is properly scoped to their team', async () => {
    // Navigate to assets page as member
    await context.memberPage.goto(`/home/<USER>/assets`);

    // Member should only see assets from their team
    await expect(context.memberPage.locator('body')).toBeVisible();

    // Member should not be able to access assets from other teams
    // This is enforced by the RLS policies at the database level
  });
});
