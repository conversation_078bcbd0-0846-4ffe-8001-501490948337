---
type: "always_apply"
description: "when running commands in terminal"
---

# Project Development Rules

## Project Setup
This is a monorepo project using pnpm as the package manager with Supabase, Stripe, and web applications.
Commands are run from /sb-front-end (project's parent folder)

## Package Management
- Always use `pnpm` instead of npm or yarn
- Install dependencies with: `pnpm i`
- Use workspace filtering for specific apps: `pnpm run --filter <workspace> <command>`

## Development Workflow

### Starting Development
- Start web dev server: `pnpm run dev`
- Start Supabase: `pnpm run supabase:web:start`
- Start Stripe webhooks: `pnpm run stripe:listen`

### Supabase Commands
- All Supabase CLI commands should use: `pnpm run --filter web supabase <command>`
- Example: Instead of `supabase link`, use `pnpm run --filter web supabase link`
- Reset database: `pnpm run supabase:web:reset`
- Generate types after schema changes: `pnpm run supabase:web:typegen`

### Database Migrations
- Create schema diff: `pnpm --filter web supabase:db:diff`
- Generate migration with name: `pnpm --filter web supabase:db:diff -f <migration-name>`
- Use meaningful migration names like "add-user-table"
- Always run `pnpm run supabase:web:typegen` after schema updates

### Code Quality
- Run tests: `pnpm run test`
- Type checking: `pnpm run typecheck`
- Linting: `pnpm run lint:fix`
- Formatting: `pnpm run format:fix`

### Project Maintenance
- Clean project: `pnpm run clean:workspaces && pnpm run clean && pnpm i`

## Terminal Command Guidelines
When suggesting terminal commands:
1. Always use pnpm, never npm or yarn
2. Use workspace filtering (`--filter web`) for Supabase commands
3. Remind about type generation after schema changes
4. Suggest meaningful migration names
5. Include full command chains when needed (e.g., clean + reinstall)

## Best Practices
- Always generate Supabase types after schema updates
- Use descriptive migration names
- Reset Supabase when schema changes significantly
- Run lint and format before commits
- Test billing features with Stripe webhooks locally